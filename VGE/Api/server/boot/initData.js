module.exports = function(app, cb) {

		var RoleMapping = app.models.RoleMapping;
		var ObjectID = RoleMapping.getDataSource().connector.getDefaultIdType();

			// Because of this: https://github.com/strongloop/loopback-connector-mongodb/issues/128
		RoleMapping.defineProperty('principalId', {
  			type: ObjectID,
		});

		initDatas();


	function initDatas() {

        var env = app.get('env');
        var file = '';

        console.log(env);

		// if(env == 'development'){
        //     file = '../../_DB/initDataWihoutBlocs.json';
		// }else{
			file = '../../_DB/initData.json';
		// }

		var initData = require('../initData');
		initData(require(file), app, function(err) {

			if (err) {
				return cb(err)
			}

			var model = app.loopback.getModelByType("Role");

			model.find(function(err, items) {
				if (err) {
					return cb(err)
				}
				app.roles = {}
				items.forEach(function(item) {
					app.roles[item.name] = item

				})
				cb();

			});


		})
	}

}
