<!doctype html>
<html lang="fr">
<head>
    <base href="<%- URL_WWW %>">
    <%- include('../head.ejs') %>
</head>
<body>
<page backtop="5mm" backbottom="5mm" backleft="10mm" backright="10mm" orientation="l">

    <table width="100%" style="width: 100%; margin-top: 5px;">
        <tr>
            <td width="100" align="left"><img src="<%- vgeLogo %>" height="40" /></td>
            <td width="840" align="center" style="font-size: 30px; color: #84c6bc;">Synthèse <%- operation.libelle %></td>
            <td width="100" align="right"><span style="font-size: 9px;">Version du <%- currentDate %></span></td>
        </tr>
    </table>


<% stats.forEach(function(statSociete){ %>

    <div style="background-color: #84c6bc; color: white;margin-top: 20px; padding: 10px; text-align: center; font-size: 30px; ;margin-bottom: 50px">
        Entreprise : <%- statSociete.societe %>
    </div>

    <table width="100%">
        <tr>
            <td width="33%">
                <div class="text-center">
                    <img src="<%- canvas[statSociete.societe].GLOBAL %>" />
                </div>
                <ul class="list-unstyled">
                    <% statSociete.stats.GLOBAL.states.forEach(function(state){ %>
                        <li class="text-left" style="margin-bottom: 3px">
                            <span class="badge badge-info" style="background: <%- state.etat.couleur %>;"><%- state.nb %></span>
                            <%- state.etat.libelle %> (<%- (state.nb / statSociete.stats.GLOBAL.nb * 100).toFixed(2) %>%)
                        </li>
                    <% }); %>
                </ul>
            </td>
            <td width="33%">
                <div class="text-center">
                    <img src="<%- canvas[statSociete.societe].GPA %>" />
                </div>
                <ul class="list-unstyled">
                    <% statSociete.stats.GPA.states.forEach(function(state){ %>
                        <li class="text-left" style="margin-bottom: 3px">
                            <span class="badge badge-info" style="background: <%- state.etat.couleur %>;"><%- state.nb %></span>
                            <%- state.etat.libelle %> (<%- (state.nb / statSociete.stats.GPA.nb * 100).toFixed(2) %>%)
                        </li>
                    <% }); %>
                </ul>
            </td>
            <td width="33%">
                <div class="text-center">
                    <img src="<%- canvas[statSociete.societe].GBF %>" />
                </div>
                <ul class="list-unstyled">
                    <% statSociete.stats.GBF.states.forEach(function(state){ %>
                        <li class="text-left" style="margin-bottom: 3px">
                            <span class="badge badge-info" style="background: <%- state.etat.couleur %>;"><%- state.nb %></span>
                            <%- state.etat.libelle %> (<%- (state.nb / statSociete.stats.GBF.nb * 100).toFixed(2) %>%)
                        </li>
                    <% }); %>
                </ul>
            </td>
        </tr>
    </table>

    <table width="100%" class="table fixedLayout fs-12 table-compact table-bordered table-striped table-hover">
        <thead>
        <tr>
            <th width="10%">Mode</th>
            <th width="10%">Numéro</th>
            <th width="40%">Description</th>
            <th width="20%">État</th>
            <th width="10%">Date état</th>

        </tr>
        </thead>
        <tbody>
            <% for(let reclam of statSociete.stats.reclamations){ %>
            <tr>
                <td><%- reclam.mode %></td>
                <td>N°<%- reclam.num%></td>
                <td class="preventDisplay">
                    <div>
                        <%- reclam.description %>
                    </div>
                </td>
                <td style="background-color: <%- (reclam.etat() ? reclam.etat().couleur : '#ffffff') %>;">
                    <%- (reclam.etat() && reclam.etat().libelle) ? reclam.etat().libelle : '' %>
                </td>
                <td>
                    <%- (reclam.dateetat) ? reclam.dateetat : '' %>
                </td>
            </tr>
            <% } %>
        </tbody>
    </table>


<% }) %>






</body>
</html>
