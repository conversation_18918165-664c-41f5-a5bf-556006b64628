/**
 * Account Model. Extend standard Loopback User Model
 * Add One And One Relation to Parent AND/OR Child Model
 */

var loopback    = require('loopback');
var app = require('../../../../server/server');
var path = require('path');
var helper = require('../../../helper');

var photos = [];
var files = ['file'];

module.exports = function(IntraMultiSinistreOperationpeoplerole) {



  /**
   * Get relation & return after save
   */
  IntraMultiSinistreOperationpeoplerole.observe('after save', function(ctx, next) {
    ctx.instance.societe(function (err, societe) {
      ctx.instance.__data.societe = societe;

      ctx.instance.people(function (err, people) {
        ctx.instance.__data.people = people;

        //console.log(ctx.instance.__data);

        next();
      });
    });
  });

}
