{"name": "IntraEbuzAnalytics", "plural": "IntraEbuzAnalytics", "base": "PersistedModel", "hidden": [], "options": {"strict": false, "forceId": false, "replaceOnPUT": true, "idInjection": false, "mysql": {"table": "intra_eb_analytics"}}, "properties": {"id": {"type": "number", "id": true, "generated": true, "length": null, "precision": 10, "scale": 0, "mysql": {"columnName": "id", "dataType": "int", "dataLength": null, "dataPrecision": 10, "dataScale": 0, "nullable": "N"}, "updateOnly": false}, "annee": {"type": "string", "required": false}, "obs": {"type": "string", "required": false}, "gains": {"type": "string", "required": false}, "anteriorite": {"type": "number", "required": false}, "hidden": {"type": "number", "required": false}}, "validations": [], "relations": {"client": {"type": "belongsTo", "model": "Societes", "foreignKey": "societe_id"}, "operation": {"type": "belongsTo", "model": "IntraDoOperation", "foreignKey": "operation_id"}}, "scope": {}, "acls": [{"accessType": "*", "principalType": "ROLE", "principalId": "$everyone", "permission": "DENY"}, {"accessType": "*", "principalType": "ROLE", "principalId": "admin", "permission": "ALLOW"}, {"accessType": "*", "principalType": "ROLE", "principalId": "super-admin", "permission": "ALLOW"}, {"accessType": "*", "principalType": "ROLE", "principalId": "$authenticated", "permission": "ALLOW"}], "methods": {}}