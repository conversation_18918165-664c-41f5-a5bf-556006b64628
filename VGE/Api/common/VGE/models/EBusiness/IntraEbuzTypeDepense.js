/**
 * Account Model. Extend standard Loopback User Model
 * Add One And One Relation to Parent AND/OR Child Model
 */

var loopback    = require('loopback');
var app = require('../../../../server/server');
var path = require('path');
var helper = require('../../../helper');
const moment = require("moment-timezone");

var photos = [];
var files = [];

module.exports = function(IntraEbuzTypeDepense) {

  IntraEbuzTypeDepense.observe('after save', function(ctx, next) {

    // Find depenses with this category !
    // Maj each one with new TVA !

    if(ctx.isNewInstance){
      if(
        app.get('VGE')['MODULES'] &&
        app.get('VGE')['MODULES']['EBUSINESS'] &&
        app.get('VGE')['MODULES']['EBUSINESS']['depensesStartDate'] &&
        moment(app.get('VGE')['MODULES']['EBUSINESS']['depensesStartDate'], 'DD/MM/YYYY').isAfter(moment((ctx.instance.year+'-01-01'), 'YYYY-MM-DD'))
      ){
        const titreErr = 'La sauvegarde a été interrompue car le début de création est bloqué pour les dépenses avant le ' + app.get('VGE')['MODULES']['EBUSINESS']['depensesStartDate'];
        let err = new Error(titreErr);
        err.statusCode = 422;
        err.message = titreErr
        return next(err);
      } else {
        next()
      }

    } else {

      if(
        app.get('VGE')['MODULES'] &&
        app.get('VGE')['MODULES']['EBUSINESS'] &&
        app.get('VGE')['MODULES']['EBUSINESS']['depensesStartDate'] &&
        moment(app.get('VGE')['MODULES']['EBUSINESS']['depensesStartDate'], 'DD/MM/YYYY').isAfter(moment((ctx.instance.year+'-01-01'), 'YYYY-MM-DD'))
      ){
        const titreErr = 'La sauvegarde a été interrompue car le début de modification est bloqué pour les dépenses avant le ' + app.get('VGE')['MODULES']['EBUSINESS']['depensesStartDate'];
        let err = new Error(titreErr);
        err.statusCode = 422;
        err.message = titreErr
        return next(err);
      } else {

        const model = loopback.getModel('IntraEbuzDepense');
        const modelType = loopback.getModel('IntraEbuzTypeDepense');


        if (ctx.instance.parent_id === 0 && ctx.instance.enfants().length > 0) {
          // MAJ DES SOUS CATEGS

          const promises1 = [];
          for (let enfant of ctx.instance.enfants()) {
            promises1.push(
              new Promise((resolve, reject) => {
                modelType.findOne({where: {id: enfant.id}}, function (err, type) {
                  if (err) {
                    next(err);
                  }

                  const modifs = {
                    tva: ctx.instance.tva
                  }
                  type.updateAttributes(modifs, (err, updatedType) => {
                    if (err) return reject(err)
                    return resolve();
                  })
                });
              })
            )
          }

          Promise.all(promises1).then(() => {
            next();
          })

        } else {

          // MAJ depenses HT avec TTC + TVA !
          model.find({where: {type_depense_id: ctx.instance.id}}, function (err, depenses) {
            if (err) {
              next(err);
            }

            if (depenses.length > 0) {

              const promises = [];

              depenses.forEach((depense) => {
                promises.push(new Promise((resolve, reject) => {

                  const TTC = depense.montantTTC;
                  const HT = depense.montantTTC / (1 + ctx.instance.tva / 100)
                  const TVA = TTC - HT;

                  // On ne modifie jamais le TTC !!
                  const modifs = {
                    montantHT: HT,
                    TTVA: ctx.instance.tva,
                    showHT: ctx.instance.showHT,
                    TVA: TVA
                  }
                  depense.updateAttributes(modifs, (err, updatedDepense) => {
                    if (err) return reject(err)
                    return resolve();
                  })

                }))
              })

              Promise.all(promises).then(() => {
                next();
              }).catch((err) => {
                next(err);
              })

            } else {
              next()
            }
          })

        }
      }
    }
  });

}
