const loopback    = require('loopback');
const app = require('../../../../server/server');
const path = require('path');
const helper = require('../../../helper');
const photos = [];
const files = ['file'];
let moment = require('moment-timezone');

let historizeRemotes = require('../../../historizeRemotes');
const glob = require("glob");

module.exports = function(IntraEbuzBanque) {

  IntraEbuzBanque.remoteMethod(
    'getFromMonthYear', {
      accepts: [
        {
          arg: 'userId',
          type: 'any',
          required: true,
          http: function(ctx) {
            var req = ctx && ctx.req;
            var accessToken = req && req.accessToken;

            var userId = accessToken && accessToken.userId;

            if(!userId){
              var error = new Error("Aucun token n\'est spécifié");
              error.status = 401;
              error.code = "NO_TOKEN_DEFINED";
              error.body = 'Unauthorized';
              return error;
            }
            return userId;
          },
        },
        {
          arg: 'year',
          type: 'number',
          required: true
        },
        {
          arg: 'month',
          type: 'number',
          required: false
        },
      ],
      returns: {root: true, type: 'object'},
      http: {
        errorStatus: 400,
        'path': '/getFromMonthYear',
        verb: 'get'
      }
    }
  );

  IntraEbuzBanque.getFromMonthYear = function(userId, year, month, cb) {
    let query;
    if(month){
    query = `SELECT *, MONTH(date) as month, YEAR(date) as year FROM intra_eb_banque WHERE YEAR(date) = ${year} AND MONTH(date) = ${month} ORDER BY DAY(date)`
    } else {
      query = `SELECT *, MONTH(date) as month, YEAR(date) as year FROM intra_eb_banque WHERE YEAR(date) = ${year} ORDER BY MONTH(date)`
    }
    IntraEbuzBanque.getDataSource().connector.execute(query, null, function (err, res) {
      let result = [];
      if(res){
        result = Object.values(JSON.parse(JSON.stringify(res)));
      }
      return cb(err, result)
    })
  }
}
