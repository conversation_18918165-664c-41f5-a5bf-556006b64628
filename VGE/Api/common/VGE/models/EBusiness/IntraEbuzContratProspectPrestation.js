/**
 * Account Model. Extend standard Loopback User Model
 * Add One And One Relation to Parent AND/OR Child Model
 */

var loopback    = require('loopback');
var app = require('../../../../server/server');
var path = require('path');
var helper = require('../../../helper');
let moment = require('moment-timezone');

var photos = [];
var files = [];

module.exports = function(IntraEbuzContratProspectPrestation) {

  // OLD pour eviter la facturation auto sur les prospects
  // IntraEbuzContratProspectPrestation.observe('after save', function(ctx, next) {
  //   const IntraEbuzPlanTresoPrev = loopback.getModel('IntraEbuzPlanTresoPrev');
  //   const IntraEbuzContratProspect = loopback.getModel('IntraEbuzContratProspect');
  //
  //   if(ctx.instance.contrat_id){
  //     IntraEbuzContratProspect.findOne({where: {id: ctx.instance.contrat_id}}, function(err, contratProspect){
  //       // Calcule previProspect
  //       const type = (contratProspect.operation_id) ? 'operation' : 'client'; // cadre d'un prospect sur un contrat Cadre non pris en compte, il faut enregistrer le lcient_id dans le plan de tresorerie
  //       const idToSend = (contratProspect.operation_id) ? contratProspect.operation_id : contratProspect.client_id; // TODO + TARD !
  //       IntraEbuzPlanTresoPrev.calculeHonorairesYear(moment(contratProspect.debut, 'YYYY-MM-DD').year(), idToSend, type, function(err, maj){ // Attention ici ! avec le debut ! YEAR ! Il faudrait boucler sur les Années planchées ! diff entre fin et debut année
  //         return next()
  //       })
  //     })
  //   } else {
  //     next()
  //   }
  // });

  // Nouvelle version qui facture les prospects
  IntraEbuzContratProspectPrestation.observe('after save', function(ctx, next) {
    const IntraEbuzPlanTresoPrev = loopback.getModel('IntraEbuzPlanTresoPrev');
    const IntraEbuzContratProspect = loopback.getModel('IntraEbuzContratProspect');
    const IntraEbuzFacture = loopback.getModel('IntraEbuzFacture');


    if(ctx.instance.contrat_id){

      IntraEbuzContratProspect.findOne({where: {id: ctx.instance.contrat_id}}, function(err, contratProspect){
        if(err){return next(err)}

        // Calcule previProspect
        const type = (contratProspect.operation_id) ? 'operation' : 'client'; // cadre d'un prospect sur un contrat Cadre non pris en compte, il faut enregistrer le lcient_id dans le plan de tresorerie
        const idToSend = (contratProspect.operation_id) ? contratProspect.operation_id : contratProspect.client_id; // TODO + TARD !

        let year = (moment(contratProspect.debut, 'YYYY-MM-DD').isValid()) ? moment(contratProspect.debut, 'YYYY-MM-DD').year() :  moment(contratProspect.date, 'YYYY-MM-DD').year();

        if(ctx.instance.prev === 0){
          if(ctx.instance && ctx.instance.etat_id){
            switch (ctx.instance.etat_id) {
              case 2: {
                // ctx.instance.prev = 1; // Update 1 = Previ
                let queryUpd = 'UPDATE `intra_eb_contrat_prospect_prestation` SET `prev` = \'1\' WHERE `intra_eb_contrat_prospect_prestation`.`id` = '+ctx.instance.id+';';
                IntraEbuzFacture.getDataSource().connector.execute(queryUpd, null, function (err, prestaUpd) {
                  IntraEbuzContratProspectPrestation.return(ctx.instance, year, idToSend, type, function(err, result){
                    if(err){console.error(err); return next(err)}
                    return next(null, result)
                  })
                })
              }break;
              case 7: {
                // ctx.instance.prev = 2; // Update 2 = Signé
                let queryUpd = 'UPDATE `intra_eb_contrat_prospect_prestation` SET `prev` = \'2\' WHERE `intra_eb_contrat_prospect_prestation`.`id` = '+ctx.instance.id+';';
                IntraEbuzFacture.getDataSource().connector.execute(queryUpd, null, function (err, prestaUpd) {

                  IntraEbuzContratProspectPrestation.return(ctx.instance, year, idToSend, type, function(err, result){
                    if(err){console.error(err); return next(err)}
                    return next(null, result)
                  })
                })
              }break;
              default: {
                IntraEbuzPlanTresoPrev.calculeHonorairesYear(year, idToSend, type, function(err, maj) { // Attention ici ! avec le debut ! YEAR ! Il faudrait boucler sur les Années planchées ! diff entre fin et debut année
                  return next(err, maj);
                })
              }
            }
          } else {
            //ctx.instance.etat_id = 0 !
            IntraEbuzPlanTresoPrev.calculeHonorairesYear(year, idToSend, type, function(err, maj) { // Attention ici ! avec le debut ! YEAR ! Il faudrait boucler sur les Années planchées ! diff entre fin et debut année
              return next(err, maj);
            })
          }
        } else if(ctx.instance.prev === 1) {

          if(ctx.instance && ctx.instance.etat_id){
            switch (ctx.instance.etat_id) {
              case 7: {
                // ctx.instance.prev = 2; // Update 2 = Signé
                let queryUpd = 'UPDATE `intra_eb_contrat_prospect_prestation` SET `prev` = \'2\' WHERE `intra_eb_contrat_prospect_prestation`.`id` = '+ctx.instance.id+';';
                IntraEbuzFacture.getDataSource().connector.execute(queryUpd, null, function (err, prestaUpd) {
                  IntraEbuzContratProspectPrestation.return(ctx.instance, year, idToSend, type, function(err, result){
                    if(err){console.error(err); return next(err)}
                    return next(null, result)
                  })
                })
              }break;
              default: {

                IntraEbuzContratProspectPrestation.return(ctx.instance, year, idToSend, type, function(err, result){
                  if(err){console.error(err); return next(err)}
                  return next(null, result)
                })
              }
            }
          } else {
            //ctx.instance.etat_id = 0 !
            IntraEbuzContratProspectPrestation.return(ctx.instance, year, idToSend, type, function(err, result){
              if(err){console.error(err); return next(err)}
              return next(null, result)
            })
          }
        } else {
          IntraEbuzContratProspectPrestation.return(ctx.instance, year, idToSend, type, function(err, result){
            if(err){console.error(err); return next(err)}
            return next()
          })
        }
      })
    } else {
      next()
    }

  });

  IntraEbuzContratProspectPrestation.return = function(instance, year, idToSend, type, cb){

    const IntraEbuzPlanTresoPrev = loopback.getModel('IntraEbuzPlanTresoPrev');
    const IntraEbuzContratProspect = loopback.getModel('IntraEbuzContratProspect');
    const IntraEbuzFacture = loopback.getModel('IntraEbuzFacture');


    if(instance.nombrePrevisionnelEstime === 0){
      console.log('Nombre estimé 0, pas de facturation, return')
      return cb()
    }

    IntraEbuzFacture._factureContratProspectPrestation(instance, function(err, result){
      if(err){return cb(err)}

      // IF OK ---> etat_id = 7 = FACTURé !!
      if(result){
        const lignesIds = result.lignesIds;

        // DELETE where prestationId in (result.prestationsIds.join(','))
        // INSERT INTO intra_eb_contrat_prospect_prestation_ligne_facture

        if(lignesIds && lignesIds.length > 0){
          let queryDlt = 'DELETE FROM `intra_eb_contrat_prospect_prestation_ligne_facture` WHERE contrat_prospect_prestation_id = '+instance.id+';';
          IntraEbuzFacture.getDataSource().connector.execute(queryDlt, null, function (err, deleted) {
            if(err){console.error('ERROR ICIC !!',err); return cb(err)}

            const p = [];

            for(let ligneId of lignesIds){
              if(ligneId){
                p.push(new Promise((resolve, reject) => {
                  let queryUpd = 'INSERT INTO `intra_eb_contrat_prospect_prestation_ligne_facture` VALUES (null, '+instance.id+', '+ligneId+');';
                  IntraEbuzFacture.getDataSource().connector.execute(queryUpd, null, function (err, prestaligneInsrt) {
                    if(err){return reject(err)}
                    return resolve();
                  })
                }))
              }
            }

            Promise.all(p).then(function (r) {
              let queryUpd = 'UPDATE `intra_eb_contrat_prospect_prestation` SET `etat_id` = \'7\' WHERE `intra_eb_contrat_prospect_prestation`.`id` = '+instance.id+';';
              IntraEbuzFacture.getDataSource().connector.execute(queryUpd, null, function (err, prestaUpd2) {
                IntraEbuzPlanTresoPrev.calculeHonorairesYear(year, idToSend, type, function(err, maj) { // Attention ici ! avec le debut ! YEAR ! Il faudrait boucler sur les Années planchées ! diff entre fin et debut année
                  if(err){console.error(err); return cb(err)}
                  return cb()
                })
              })
            })

          })
        } else {
          return cb()
        }
      } else {
        return cb()
      }
    })
  }



  var beforeDeleteYear;
  var beforeDeleteContratId;
  var beforeDeleteClientId;
  var beforeDeleteOperationId;
  IntraEbuzContratProspectPrestation.observe('before delete', function(ctx, next) {
    const IntraEbuzContratProspect = loopback.getModel('IntraEbuzContratProspect');
    IntraEbuzContratProspectPrestation.findOne({where: {id: ctx.where.id}}, function(err, ligneToBeDeleted){
      IntraEbuzContratProspect.findOne({where: {id: ligneToBeDeleted.contrat_id}}, function(err, contratProspect) {
        beforeDeleteYear = moment(contratProspect.debut, 'YYYY-MM-DD').year()
        beforeDeleteContratId = contratProspect.id
        beforeDeleteClientId = contratProspect.client_id
        beforeDeleteOperationId = contratProspect.operation_id
        next();
      })
    })
  })

  IntraEbuzContratProspectPrestation.observe('after delete', function(ctx, next) {
    const IntraEbuzPlanTresoPrev = loopback.getModel('IntraEbuzPlanTresoPrev');

    if(beforeDeleteContratId){
      // Calcule previProspect
      const type = (beforeDeleteOperationId) ? 'operation' : 'client';
      const idToSend = (beforeDeleteOperationId) ? beforeDeleteOperationId : beforeDeleteClientId;
      IntraEbuzPlanTresoPrev.calculeHonorairesYear(beforeDeleteYear, idToSend, type, function(err, maj){
        beforeDeleteYear = null;
        beforeDeleteContratId = null;
        beforeDeleteClientId = null;
        beforeDeleteOperationId = null;
        return next()
      })
    } else {
      next()
    }

  });
}
