/**
 * Account Model. Extend standard Loopback User Model
 * Add One And One Relation to Parent AND/OR Child Model
 */

let loopback    = require('loopback');
let app = require('../../../../server/server');
let helper = require('../../../helper');
let mailer = require('../../../mailer');
let moment = require('moment-timezone');
moment().tz("Europe/Paris").utc(true);

let photos = ['photo'];
let files = ['file', 'file2', 'file3', 'file4', 'file5','email'];

let historizeRemotes = require('../../../historizeRemotes');

module.exports = function(IntraEbuzContratProspectCommercialHistorique) {

  historizeRemotes.historizeRemotes(IntraEbuzContratProspectCommercialHistorique, 'comment', {add:false, edit: true, delete:true});

  // IntraEbuzContratProspectCommercialHistorique.observe('loaded', function(ctx, next){
  //
  //   if(ctx && ctx.data) {
  //
  //     if(ctx.data.dest && ctx.data.dest !== ''){
  //       ctx.data.dest = JSON.parse(ctx.data.dest);
  //     }
  //     helper.replacePathFiles(ctx.data, photos, files);
  //   }
  //   next();
  // });

  // IntraEbuzContratProspectCommercialHistorique.observe('before save', function(ctx, next) {
  //   if (ctx.isNewInstance) {
  //     ctx.instance.creator_id = ctx.options.accessToken.userId;
  //     next()
  //   } else {
  //     next()
  //   }
  // })

  /**
   * Get relation & return after save
   */
  IntraEbuzContratProspectCommercialHistorique.observe('after save', function(ctx, next) {

    if(ctx.options && ctx.options.accessToken && ctx.options.accessToken.userId){

      let IntraEbuzCommercial = loopback.getModel('IntraEbuzContratProspect');
      let historyModel = loopback.getModel('IntraEbHistory');

      // var userId = ctx.options.accessToken.userId;
      helper.getPeopleFromId(ctx.options.accessToken.userId, function(err, user){
        if(err){next(err)}

        /**** SEND EMAILs ! ****/

        if(ctx.isNewInstance){

          if(ctx.instance.comment && (ctx.instance.responsables || ctx.instance.admins) && ctx.instance.typeActualisation === 'simple'){

            /****
             * Simple Actualisation
             */

            let responsables = null;
            if(typeof ctx.instance.responsables == 'string'){
              responsables = ctx.instance.responsables.trim();
              responsables = JSON.parse(responsables); // Array<Ids>
            }else{
              responsables = ctx.instance.responsables;
            }

            let admins = null;
            if(typeof ctx.instance.admins == 'string'){
              admins = ctx.instance.admins.trim();
              admins = JSON.parse(admins); // Array<Ids>
            }else{
              admins = ctx.instance.admins;
            }

            // console.log('responsables', responsables);

            IntraEbuzContratProspectCommercialHistorique.GetNeededForMail(ctx.instance.propositionId, ctx.instance.subject, responsables, admins, function(err, result){
              if(err){return next(err)}

              /*********************************
               * Ajout de destinataire si aucun !
               */
              if(!result.tos || result.tos.length === 0){
                // Ici email imposible a envoyer car aucun destinataire -> On recup un element de la copie cachée pour le mettre en destinataire
                if(result.admins && result.admins.length > 0){
                  result.tos.push(result.admins[0]);
                  result.admins.splice(0, 1);
                }
              }
              /*
               * END Ajout de destinataire si aucun !
               *********************************/


              let promises = [];

              let ejs = 'proposition/suivi.ejs';
              let options = {
                signature: user.signature
              };

              let tos = result.tos; // List of responsableRelation
              let adminsto = result.admins;

              let tosAdressNameObjectEMLArray = [];

              let toCCAdressNameObjectArray = [];
              let toCCAdressNameObjectEMLArray = [];

              let subject = result.subject;
              let entete = result.entete;

              /*  */
              if(ctx.instance.comment){
                entete += "<br/><br/>- Objet de l’actualisation : <strong>"+ctx.instance.comment+"</strong><br />";
              }

              var fromAdr = (ctx.instance.from) ? ctx.instance.from : null;

              options['entete'] = entete;
              if(ctx.instance.emailbody){
                options['body'] = ctx.instance.emailbody;
              }else{
                options['body'] = '';
              }

              // OK
              if(adminsto && adminsto.length > 0) {
                adminsto.forEach(function (people) {
                  if (people && people.email) {
                    toCCAdressNameObjectArray.push({
                      name: people.lastname + ' ' + people.firstname,
                      address: people.email
                    });

                    toCCAdressNameObjectEMLArray.push({
                      name: people.lastname + ' ' + people.firstname,
                      address: people.email
                    });
                  }
                })
              }

              let attachements = [];

              if(ctx.instance.file){
                if(Array.isArray(ctx.instance.file)){
                  ctx.instance.file.forEach(function(f){
                    attachements.push(f);
                  })
                } else {
                  attachements.push(ctx.instance.file);
                }
              }
              if(ctx.instance.file2) {
                attachements.push(ctx.instance.file2);
              }
              if(ctx.instance.file3) {
                attachements.push(ctx.instance.file3);
              }
              if(ctx.instance.file4) {
                attachements.push(ctx.instance.file4);
              }
              if(ctx.instance.file5) {
                attachements.push(ctx.instance.file5);
              }

              options['signature'] = (user.signatureVGE) ? user.signatureVGE : user.signature // Par défaut !

              let address = [];
              if(tos && tos.length > 0) {
                tos.forEach(function (to) {

                  if(to.people && to.people()){
                    const people = to.people();
                    if (people.lastname && people.firstname) {
                      address.push({
                        name: people.lastname +' '+people.firstname,
                        address: people.email
                      });

                      tosAdressNameObjectEMLArray.push({
                        name: people.lastname +' '+people.firstname,
                        address: people.email
                      })

                    } else {
                      address.push({
                        address: people.email
                      });

                      tosAdressNameObjectEMLArray.push({
                        address: people.email
                      })
                    }
                  } else {
                    // let people = to.people();
                    if (to.lastname && to.firstname) {
                      address.push({
                        name: to.lastname +' '+to.firstname,
                        address: to.email
                      });

                      tosAdressNameObjectEMLArray.push({
                        name: to.lastname +' '+to.firstname,
                        address: to.email
                      })

                    } else {
                      address.push({
                        address: to.email
                      });

                      tosAdressNameObjectEMLArray.push({
                        address: to.email
                      })

                    }
                  }
                });
              }

              // 1 Seul mail d'envoyé maintenant !!
              mailer.sendMail(address, [], toCCAdressNameObjectArray, subject, ejs, options, attachements, fromAdr, user, {entity: 'IntraEbuzContratProspectCommercialHistorique', id: ctx.instance.id}).then(function(){

                /* END  */
                mailer.toEML(ctx.instance.propositionId, tosAdressNameObjectEMLArray, [], toCCAdressNameObjectEMLArray, subject, ejs, options, attachements, fromAdr, 'emailsCommercial/proposition-', function(err, eml){
                  if(err){ return next(err);}

                  if(!ctx.instance.id){

                    console.log('!ctx.instance.id L149, ERROR 1151');
                    let error = new Error("l'instance n'a plus son ID de disponible, veuillez contacter immédiatement l'administrateur <EMAIL>, erreur N°1151");
                    error.status = 503;
                    error.code = "ERROR_1151_CTX_ID_UNKNOWN";
                    error.body = "l'instance n'a plus son ID de disponible, veuillez contacter immédiatement l'administrateur <EMAIL>, erreur N°1151";
                    let message = "l'instance n'a plus son ID de disponible, veuillez contacter immédiatement l'administrateur <EMAIL>, erreur N°1151<br/><br/>" + JSON.stringify(ctx.instance);
                    let options = {
                      message: message
                    };
                    mailer.sendMail(['<EMAIL>'],[],[],'VGE API : ERROR 1151 CTX ID unknown', 'base.ejs', options).then(function(){
                      return next(error);
                    });

                  }else {

                    IntraEbuzContratProspectCommercialHistorique.findOne({where: {id: ctx.instance.id}}, function (err, histo) {
                      if (err) {return next(err);}

                      let to = [];
                      tosAdressNameObjectEMLArray.forEach(function (t) {
                        to.push(t.address);
                      });

                      // Ici on defini l'histo principal qui doit être utilisé pour les relances auto
                      let attrtoPatch = {};


                      if(histo.rappelAuto == 1 && !histo.histoRef){
                        attrtoPatch = {email: eml.filenameToDb, to: to.join(','), histoRef: histo.id}
                      } else if(histo.rappelAuto == 1 && histo.histoRef){
                        attrtoPatch = {email: eml.filenameToDb, to: to.join(','), histoRef: histo.histoRef}
                      } else {
                        attrtoPatch = {email: eml.filenameToDb, to: to.join(',')}
                      }

                      histo.patchAttributes(attrtoPatch, function (err, histoSaved) {
                        if (err) {return next(err)}

                        /***
                         * Maj MDATE ---> Last Suivi created !
                         */
                        IntraEbuzCommercial.findById(ctx.instance.propositionId, function (err, proposition) {
                          if (err) {return next(err)}

                          // proposition.patchAttributes({mdate: moment()}, function (err, res) {
                          //   if (err) {return next(err)}
                          //
                          //   historyModel.historize({
                          //     type: 'suivi',
                          //     peopleId: (ctx.options && ctx.options.accessToken && ctx.options.accessToken.userId) ? ctx.options.accessToken.userId : null,
                          //     classObject: 'IntraDoHistorique',
                          //     idObject: sinistre.id,
                          //     idObjectOperation: sinistre.operation().id,
                          //     module: 'DOnet',
                          //     sinistreId: sinistre.id,
                          //     suiviId: histoSaved.id,
                          //     explain: 'Création d\'un suivi simple sur le sinistre avec comme sujet : ' + subject
                          //   }, function (err, res) {
                          //     if (err) {console.log(err);}
                              return next(null, histoSaved);
                            // });

                            // IntraDoHistorique.popRelations(ctx, function(err, ctx){
                            //   if(err){return next(err);}
                            //console.log('SAVE histo ')

                            // });
                          // })
                        });
                      })
                    });
                  }
                });
              }).catch(function(err){
                console.error(err);
                return next(err);
              })
            })

          }else if(ctx.instance.comment && (ctx.instance.destinataires || ctx.instance.destinatairesRoles || ctx.instance.admins) && ctx.instance.typeActualisation === 'complex'){

            /*******
             * FULL MAIL !
             */
              // console.log('instance', ctx.instance)

            var destinataires = null;
            if(typeof ctx.instance.destinataires == 'string'){
              destinataires = ctx.instance.destinataires.trim();
              destinataires = JSON.parse(destinataires); // Array<Ids>
            }else{
              destinataires = ctx.instance.destinataires;
            }

            var destinatairescc = null;
            if(typeof ctx.instance.destinatairescc == 'string'){
              destinatairescc = ctx.instance.destinatairescc.trim();
              destinatairescc = JSON.parse(destinatairescc); // Array<Ids>
            }else{
              destinatairescc = ctx.instance.destinatairescc;
            }

            var destinatairescopie = null;
            if(typeof ctx.instance.destinatairescopie == 'string'){
              destinatairescopie = ctx.instance.destinatairescopie.trim();
              destinatairescopie = JSON.parse(destinatairescopie); // Array<Ids>
            }else{
              destinatairescopie = ctx.instance.destinatairescopie;
            }

            var admins = null;
            if(typeof ctx.instance.admins == 'string'){
              admins = ctx.instance.admins.trim();
              admins = JSON.parse(admins); // Array<Ids>
            }else{
              admins = ctx.instance.admins;
            }


            if(typeof ctx.instance.destinataires == 'string'){
              destinataires = ctx.instance.destinataires;
            }


            var destinatairesRoles = null;
            if(typeof ctx.instance.destinatairesRoles == 'string'){
              destinatairesRoles = ctx.instance.destinatairesRoles.trim();
              destinatairesRoles = JSON.parse(destinatairesRoles); // Array<Ids>
            }else{
              destinatairesRoles = ctx.instance.destinatairesRoles;
            }

            var destinatairesRolescc = null;
            if(typeof ctx.instance.destinatairesRolescc == 'string'){
              destinatairesRolescc = ctx.instance.destinatairesRolescc.trim();
              destinatairesRolescc = JSON.parse(destinatairesRolescc); // Array<Ids>
            }else{
              destinatairesRolescc = ctx.instance.destinatairesRolescc;
            }

            var destinatairesRolescopie = null;
            if(typeof ctx.instance.destinatairesRolescopie == 'string'){
              destinatairesRolescopie = ctx.instance.destinatairesRolescopie.trim();
              destinatairesRolescopie = JSON.parse(destinatairesRolescopie); // Array<Ids>
            }else{
              destinatairesRolescopie = ctx.instance.destinatairesRolescopie;
            }


            IntraEbuzContratProspectCommercialHistorique.GetNeededForFullMail(ctx.instance.propositionId, destinataires, destinatairescc, destinatairescopie, admins, destinatairesRoles, destinatairesRolescc, destinatairesRolescopie, function(err, result){
              if(err) {console.error(err)}

              if(result.groupe && result.destinataires.length === 0 && result.admins.length === 0){
                console.error('ICI on a pas de destinataires, email non envoyé', result)
                return next();
              }


              /*********************************
               * Ajout de destinataire si aucun !
               */
              if(!result.destinataires || result.destinataires.length === 0){
                // Ici email imposible a envoyer car aucun destinataire -> On recup un element de la copie cachée pour le mettre en destinataire
                if(result.destinatairescc && result.destinatairescc.length > 0){
                  result.destinataires.push(result.destinatairescc[0]);
                  result.destinatairescc.splice(0, 1);
                } else if(!result.destinatairescc && result.destinatairescopie && result.destinatairescopie.length > 0){
                  // Ici email imposible a envoyer car aucun destinataire -> On recup un element de la copie pour le mettre en destinataire
                  result.destinataires.push(result.destinatairescopie[0]);
                  result.destinatairescopie.splice(0, 1);
                } else if(result.admins){
                  result.destinataires.push(result.admins[0]);
                  result.admins.splice(0, 1);
                }
              }
              /*
               * END Ajout de destinataire si aucun !
               *********************************/


              const promises = [];

              const ejs = 'proposition/suiviFull.ejs';
              let options = {};


              let subject = ctx.instance.subject;

              // options['signature'] = ctx.instance.signature; // Signature sender !
              options['signature'] = user[ctx.instance.signature]; // Signature sender -> Nouveau on envoi le nom de la variable de la signature !


              const destinataires = result.destinataires; // List of destinataires

              let listDestinataires = [];
              let destinatairesEml = []
              if(destinataires && destinataires.length > 0) {
                destinataires.forEach(function (destinataire) {
                  if(destinataire.people && destinataire.people()){
                    let people = destinataire.people();
                    if (people && people.email) {

                      if(destinataire.lastname && destinataire.firstname){
                        listDestinataires.push({
                          name: people.lastname + ' ' + people.firstname,
                          address: people.email
                        });

                        destinatairesEml.push({
                          name: people.lastname + ' ' + people.firstname,
                          address: people.email
                        });
                      } else {
                        listDestinataires.push({
                          address: people.email
                        });

                        destinatairesEml.push({
                          address: people.email
                        });
                      }
                    }
                  } else {
                    if (destinataire && destinataire.email) {
                      if(destinataire.lastname && destinataire.firstname){
                        listDestinataires.push({
                          name: destinataire.lastname + ' ' + destinataire.firstname,
                          address: destinataire.email
                        });
                        destinatairesEml.push({
                          name: destinataire.lastname + ' ' + destinataire.firstname,
                          address: destinataire.email
                        });
                      } else {
                        listDestinataires.push({
                          address: destinataire.email
                        });
                        destinatairesEml.push({
                          address: destinataire.email
                        });
                      }
                    }
                  }
                })
              }

              const destinatairescc = result.destinatairescc; // List of destinataires
              let listDestinatairescc = [];
              let destinatairesccEml = []
              if(destinatairescc && destinatairescc.length > 0) {
                destinatairescc.forEach(function (destinataire) {
                  let people = destinataire.people();
                  if (people && people.email) {
                    listDestinatairescc.push({
                      name: people.lastname + ' ' + people.firstname,
                      address: people.email
                    });

                    destinatairesccEml.push({
                      name: people.lastname + ' ' + people.firstname,
                      address: people.email
                    });
                  }
                })
              }

              const destinatairescopie = result.destinatairescopie; // List of destinataires
              let listDestinatairescopie = [];
              let destinatairescopieEml = []
              if(destinatairescopie && destinatairescopie.length > 0) {
                destinatairescopie.forEach(function (destinataire) {
                  let people = destinataire.people();
                  if (people && people.email) {
                    listDestinatairescopie.push({
                      name: people.lastname + ' ' + people.firstname,
                      address: people.email
                    });

                    destinatairescopieEml.push({
                      name: people.lastname + ' ' + people.firstname,
                      address: people.email
                    });

                  }
                })
              }

              // OK
              const adminsto = result.admins;
              if(adminsto && adminsto.length > 0) {
                adminsto.forEach(function (people) {
                  if (people && people.email) {
                    listDestinatairescopie.push({
                      name: people.lastname + ' ' + people.firstname,
                      address: people.email
                    });

                    destinatairescopieEml.push({
                      name: people.lastname + ' ' + people.firstname,
                      address: people.email
                    });

                  }
                })
              }

              options['entete'] = ctx.instance.emailheader;
              if(ctx.instance.emailbody){
                options['body'] = ctx.instance.emailbody;
              }else{
                options['body'] = '';
              }

              /*** FROM ***/
              /***
               * ELSE BLOCKED !!
               */
              options['expediteurEmail'] = ctx.instance.expediteurEmail;
              options['expediteurNom'] = ctx.instance.expediteurNom;

              // Problmeme de blocage Désactivé !
              // if(options['expediteurEmail'].indexOf(app.get('VGE')['mailer']['DOMAIN']) == -1){
              //   options['expediteurEmail'] = app.get('VGE')['mailer']['EMAIL_FROM'];
              // }

              let fromAdr = null;
              if(options['expediteurNom']){
                fromAdr = {
                  name: options['expediteurNom'],
                  address: options['expediteurEmail']
                }
              }else{
                fromAdr = {
                  name: app.get('VGE')['mailer']['EMAIL_FROM_NAME'],
                  address: options['expediteurEmail']
                };
              }

              if(ctx.instance.ftp) {
                options['ftps'] = [ctx.instance.ftp];
              }else{
                options['ftps'] = [];
              }

              let attachements = [];

              if(ctx.instance.file) {
                attachements.push(ctx.instance.file);
              }

              if(ctx.instance.file2) {
                attachements.push(ctx.instance.file2);
              }

              if(ctx.instance.file3) {
                attachements.push(ctx.instance.file3);
              }

              if(ctx.instance.file4) {
                attachements.push(ctx.instance.file4);
              }

              if(ctx.instance.file5) {
                attachements.push(ctx.instance.file5);
              }

              if(ctx.instance.files) {
                ctx.instance.files.forEach(function(att){
                  attachements.push(att.file);
                })
              }

              // console.log('listDestinataires',listDestinataires, 'listDestinatairescc',listDestinatairescc, 'listDestinatairescopie',listDestinatairescopie)

              mailer.sendMail(listDestinataires, listDestinatairescc, listDestinatairescopie, subject, ejs, options, attachements, fromAdr, user, {entity: 'IntraEbuzContratProspectCommercialHistorique', id: ctx.instance.id}).then(function(resultMailer){

                mailer.toEML(ctx.instance.propositionId, destinatairesEml, destinatairesccEml, destinatairescopieEml, subject, ejs, options, attachements, fromAdr, 'emailsCommercial/proposition-', function(err, eml){
                  if(err){ return next(err);}

                  if(!ctx.instance.id){

                    console.error('!ctx.instance.id L338, ERROR 1150');
                    let error = new Error("l'instance n'a plus son ID de disponible, veuillez contacter immédiatement l'administrateur <EMAIL>, erreur N°1150");
                    error.status = 503;
                    error.code = "ERROR_1150_CTX_ID_UNKNOWN";
                    error.body = "l'instance n'a plus son ID de disponible, veuillez contacter immédiatement l'administrateur <EMAIL>, erreur N°1150";
                    let message = "l'instance n'a plus son ID de disponible, veuillez contacter immédiatement l'administrateur <EMAIL>, erreur N°1150<br/><br/>" + JSON.stringify(ctx.instance);
                    let options = {
                      message: message
                    };
                    mailer.sendMail(['<EMAIL>'],[],[],'VGE API : ERROR 1150 CTX ID unknown', 'base.ejs', options).then(function(){
                      return next(error);
                    });

                  }else {

                    IntraEbuzContratProspectCommercialHistorique.findOne({where: {id: ctx.instance.id}}, function (err, histofound) {
                      if (err) {return next(err);}

                      let to = [];
                      destinatairesEml.forEach(function (t) {
                        to.push(t.address);
                      });

                      let attrtoPatch = {};
                      if(histofound.rappelAuto == 1 && !histofound.histoRef){
                        attrtoPatch = {email: eml.filenameToDb, to: to.join(','), histoRef: histofound.id}
                      } else if(histofound.rappelAuto == 1 && histofound.histoRef){
                        attrtoPatch = {email: eml.filenameToDb, to: to.join(','), histoRef: histofound.histoRef}
                      } else {
                        attrtoPatch = {email: eml.filenameToDb, to: to.join(',')}
                      }

                      histofound.patchAttributes(attrtoPatch, function(err, histo){
                        if(err){ return next(err);}

                        // IntraDoSinistre.findById(ctx.instance.sinistreId, function(err, sinistre){
                        //   if(err){return next(err)}
                        //   sinistre.patchAttributes({mdate:  moment()}, function(err, resPatchAttr){
                        //     if(err){return next(err)}
                        //
                        //     historyModel.historize({
                        //       type: 'suivi',
                        //       peopleId: (ctx.options && ctx.options.accessToken && ctx.options.accessToken.userId) ? ctx.options.accessToken.userId : null,
                        //       classObject: 'IntraDoHistorique',
                        //       idObject: sinistre.id,
                        //       idObjectOperation: sinistre.operation().id,
                        //       module: 'DOnet',
                        //       sinistreId: sinistre.id,
                        //       suiviId: histo.id,
                        //       explain: 'Création d\'un suivi complet sur le sinistre avec comme sujet : ' + subject
                        //     }, function (err, res) {
                        //       if (err) {
                        //         console.log(err);
                        //       }
                              return next(null, histo);
                            // });

                            // IntraDoHistorique.popRelations(ctx, function(err, ctx){
                            //   if(err){return next(err);}
                            //console.log('SAVE histo ')
                            // });
                          // })
                        // });

                      });
                    });
                  }
                });
              }).catch(function(err){

                let error = new Error("Probleme d'envoi de mail", err.message);
                error.status = 403;
                error.code = "ERR_SENDMAIL";
                error.body = "Errur lié à l'envoi de mail via mailgun";

                return next(error);
              });
            });
          }else{
            IntraEbuzContratProspectCommercialHistorique.findOne({where:{id: ctx.instance.id}}, function(err, histo){
              if(err){ return next(err);}
              // IntraDoSinistre.findById(ctx.instance.sinistreId, function(err, sinistre){
              //   if(err){return next(err)}
              //   sinistre.patchAttributes({mdate:  moment()}, function(err, res){
              //     if(err){return next(err)}

                  return next(null, histo);
                // });
              // });
            });
          }
        }else{
          IntraEbuzContratProspectCommercialHistorique.popRelations(ctx, function(err, ctx){
            if(err){return next(err);}

            historyModel.historize({
              type:'edit',
              peopleId: (ctx.options && ctx.options.accessToken && ctx.options.accessToken.userId) ? ctx.options.accessToken.userId : null,
              classObject: 'IntraEbuzContratProspectCommercialHistorique',
              idObject: ctx.instance.id,
              module:'eBusiness',
              explain:'Edition d\'un suivi'
            }, function(err, res){
              if(err){console.log(err);}
              return next();
            });

          })
        }
      });
    }else{
      return next();
    }
  });

  IntraEbuzContratProspectCommercialHistorique.popRelations = function(ctx, cb){

    ctx.instance.people(function (err, people) {
      ctx.instance.__data.people = people;
        /**
         * Retour avec PATH
         * @type {string[]}
         */
        helper.replacePathFiles(ctx.instance.__data, photos, files);
        return cb(err, ctx);
    });
  }



  IntraEbuzContratProspectCommercialHistorique.GetNeededForMail = function(idProposition, subject, responsablesChoosen=[], admins=[], cb){

    let IntraEbuzContratProspect = loopback.getModel('IntraEbuzContratProspect');
    let historyModel = loopback.getModel('IntraEbHistory');
    const IntraDoOperation = loopback.getModel('IntraEbuzOperation');
    const People = loopback.getModel('People');

    IntraEbuzContratProspect.findOne({where:{id:idProposition}, include:['operation']}, function(err, proposition){
      People.getAdmins(function(err, adminsfromDB){
        if(err){return cb(err);}

        const operation = proposition.operation();

        const tos = [];

        // console.log(responsablesChoosen, proposition);

        // Ici on prends l'id de la personne et non la relation ...
        if(responsablesChoosen && responsablesChoosen.length > 0){
          responsablesChoosen.forEach(function(el){
            if(proposition.responsablecompta_id && proposition.responsablecompta_id === parseInt(el)){tos.push(proposition.comptable()); }
            if(proposition.responsablecompta2_id  && proposition.responsablecompta2_id === parseInt(el)){tos.push(proposition.comptable2()); }
            if(proposition.responsablecompta3_id  && proposition.responsablecompta3_id === parseInt(el)){tos.push(proposition.comptable3()); }
            if(proposition.responsablecompta4_id  && proposition.responsablecompta4_id === parseInt(el)){tos.push(proposition.comptable4()); }
            if(proposition.responsablecompta5_id  && proposition.responsablecompta5_id === parseInt(el)){tos.push(proposition.comptable5()); }
          })
        }
        /// Pas de relation ici, juste l'id USER
        const adminsTos = [];
        if(admins){
          admins.forEach((adm) => {
            let admin = adminsfromDB.find(function(e){return e.id == adm});
            if(admin){
              adminsTos.push(admin); // On Add un people !!!!
            }
          })
        }

        const enteteFinal = IntraEbuzContratProspectCommercialHistorique.buildEntete(proposition, operation);
        const subjectFinal = IntraEbuzContratProspectCommercialHistorique.buildSubject(proposition, operation) + ((subject) ? ' - ' + subject : '');

        const result = {
          entete : enteteFinal,
          subject: subjectFinal,
          tos: tos,
          admins: adminsTos
        }

        return cb(err, result)

      });
    });
  }

  IntraEbuzContratProspectCommercialHistorique.GetNeededForFullMail = function(idProposition, destinataires=[], destinatairescc=[], destinatairescopie=[], admins=[], destinatairesRoles=[], destinatairesRolescc=[], destinatairesRolescopie=[],  cb){ // Mail Complet avec toutes les infos

    let IntraEbuzCommercial = loopback.getModel('IntraEbuzContratProspect');
    const IntraDoOperation = loopback.getModel('IntraEbuzOperation');
    const people = loopback.getModel('People');

    // console.log('GetNeededForFullMail', 'destinataires',destinataires, 'destinatairescc',destinatairescc, 'destinatairescopie',destinatairescopie, 'admins', admins)

    people.getAdmins(function(err, adminsfromDB){

      IntraEbuzCommercial.findOne({where:{id:idProposition}}, function(err, proposition){
        if(err){return cb(err);}
        IntraDoOperation.findOne({where:{id:proposition.operation_id}},function(err, operation){
          if(err){return cb(err);}

          let groupe = false;

          // Relations ICI, id = id relation non id people !
          const destinatairesTos = [];
          if(destinataires && destinataires.length > 0){
            destinataires.forEach(function(el){
              if(proposition.responsablecompta_id && proposition.responsablecompta_id === parseInt(el)){destinatairesTos.push(proposition.comptable()); }
              if(proposition.responsablecompta2_id  && proposition.responsablecompta2_id === parseInt(el)){destinatairesTos.push(proposition.comptable2()); }
              if(proposition.responsablecompta3_id  && proposition.responsablecompta3_id === parseInt(el)){destinatairesTos.push(proposition.comptable3()); }
              if(proposition.responsablecompta4_id  && proposition.responsablecompta4_id === parseInt(el)){destinatairesTos.push(proposition.comptable4()); }
              if(proposition.responsablecompta5_id  && proposition.responsablecompta5_id === parseInt(el)){destinatairesTos.push(proposition.comptable5()); }
            })
          }

          // console.log('liste des responsables dispos ', responsables)

          // console.log("destinatairescc", destinatairescc)
          const destinatairesccTos = [];
          if(destinatairescc && destinatairescc.length > 0) {
            destinatairescc.forEach(function (el) {

              if(proposition.responsablecompta_id && proposition.responsablecompta_id === parseInt(el)){destinatairesccTos.push(proposition.comptable()); }
              if(proposition.responsablecompta2_id  && proposition.responsablecompta2_id === parseInt(el)){destinatairesccTos.push(proposition.comptable2()); }
              if(proposition.responsablecompta3_id  && proposition.responsablecompta3_id === parseInt(el)){destinatairesccTos.push(proposition.comptable3()); }
              if(proposition.responsablecompta4_id  && proposition.responsablecompta4_id === parseInt(el)){destinatairesccTos.push(proposition.comptable4()); }
              if(proposition.responsablecompta5_id  && proposition.responsablecompta5_id === parseInt(el)){destinatairesccTos.push(proposition.comptable5()); }
            })
          }

          // console.log("destinatairescopie", destinatairescopie)
          const destinatairescopieTos = [];
          if(destinatairescopie && destinatairescopie.length > 0){
            destinatairescopie.forEach(function(el){

              if(proposition.responsablecompta_id && proposition.responsablecompta_id === parseInt(el)){destinatairescopieTos.push(proposition.comptable()); }
              if(proposition.responsablecompta2_id  && proposition.responsablecompta2_id === parseInt(el)){destinatairescopieTos.push(proposition.comptable2()); }
              if(proposition.responsablecompta3_id  && proposition.responsablecompta3_id === parseInt(el)){destinatairescopieTos.push(proposition.comptable3()); }
              if(proposition.responsablecompta4_id  && proposition.responsablecompta4_id === parseInt(el)){destinatairescopieTos.push(proposition.comptable4()); }
              if(proposition.responsablecompta5_id  && proposition.responsablecompta5_id === parseInt(el)){destinatairescopieTos.push(proposition.comptable5()); }

            })
          }

          /// Pas de ralation ici, juste l'id USER
          const adminsTos = [];
          if(admins){
            admins.forEach((adm) => {
              let admin = adminsfromDB.find(function(e){return e.id == adm});
              if(admin){
                adminsTos.push(admin); // On Add un people !!!!
              }
            })
          }

          // if(destinatairesRoles){
          //   groupe = true;
          //   destinatairesRoles.forEach((role) => {
          //     responsables.forEach(function(relation){
          //       if(relation.role_id == role){
          //         destinatairesTos.push(relation); // On Add une relation
          //       }
          //     });
          //   })
          // }
          //
          // if(destinatairesRolescc){
          //   groupe = true;
          //   destinatairesRolescc.forEach((role) => {
          //     responsables.forEach(function(relation){
          //       if(relation.role_id == role){
          //         destinatairesccTos.push(relation); // On Add une relation
          //       }
          //     });
          //   })
          // }
          //
          // if(destinatairesRolescopie){
          //   groupe = true;
          //   destinatairesRolescopie.forEach((role) => {
          //     responsables.forEach(function(relation){
          //       if(relation.role_id == role){
          //         destinatairescopieTos.push(relation); // On Add une relation
          //       }
          //     });
          //   })
          // }

          const result = {
            destinataires: destinatairesTos,
            destinatairescc: destinatairesccTos,
            destinatairescopie: destinatairescopieTos,
            admins: adminsTos,
            groupe: groupe
          }

          return cb(null, result)

        });
      });
    })
  }


  IntraEbuzContratProspectCommercialHistorique.buildSubject = function(sinistre, operation){
    const IntraEbuzCommercial = loopback.getModel('IntraEbuzContratProspect');
    return IntraEbuzCommercial.buildSubject(sinistre, operation);
  }

  IntraEbuzContratProspectCommercialHistorique.buildEntete = function(sinistre, operation){
    const IntraEbuzCommercial = loopback.getModel('IntraEbuzContratProspect');
    return IntraEbuzCommercial.buildEntete(sinistre, operation);
  }

  IntraEbuzContratProspectCommercialHistorique.observe('before delete', function(ctx, next){
    try{
      helper.deletePathFiles(ctx.instance.__data, photos, files);
      next();
    }catch (e) {
      next();
    }
  })

  IntraEbuzContratProspectCommercialHistorique.remoteMethod(
    'getRappelsAuto', {
      description: 'Get Rappels autos',
      accepts: [
        {
          arg: 'userId',
          type: 'any',
          required: true,
          http: function(ctx) {
            var req = ctx && ctx.req;
            var accessToken = req && req.accessToken;

            var userId = accessToken && accessToken.userId;

            if(!userId){
              var error = new Error("Aucun token n\'est spécifié");
              error.status = 401;
              error.code = "NO_TOKEN_DEFINED";
              error.body = 'Unauthorized';
              return error;
            }

            return userId;
          },
          description: 'Do not supply this argument, it is automatically extracted ' +
            'from request headers.'
        }
      ],
      returns: {
        arg: 'DOs',
        type: 'array',
        root: true,
        description: 'The response body contains historiques with rappels auto and sinistre'
      },
      http: {
        errorStatus: 401,
        'path': '/getRappelsAuto',
        verb: 'get'
      }
    }
  );

  IntraEbuzContratProspectCommercialHistorique.getRappelsAuto = function(userId, cb) {
    return IntraEbuzContratProspectCommercialHistorique.find({
      where: {
        rappelAuto: 1
      },
      include: [
        {
          relation: "proposition",
          scope: {
            include: ["operation"]
          }
        }
      ],
      order: ['nextRappelAuto ASC', 'commercial_id ASC']
    })
  }


}
