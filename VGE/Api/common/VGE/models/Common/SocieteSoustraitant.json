{"name": "SocieteSoustraitant", "options": {"strict": false, "forceId": false, "replaceOnPUT": true, "idInjection": false, "mysql": {"table": "societe_soustraitant"}}, "properties": {"id": {"type": "number", "id": 1, "required": false, "generated": true, "length": null, "precision": 10, "scale": 0, "mysql": {"columnName": "id", "dataType": "int", "dataLength": null, "dataPrecision": 10, "dataScale": 0, "nullable": "N"}, "updateOnly": false}, "societeId": {"type": "number", "required": true, "length": null, "precision": 10, "scale": 0, "mysql": {"columnName": "societe_id", "dataType": "int", "dataLength": null, "dataPrecision": 10, "dataScale": 0, "nullable": "N"}}, "sstraitantId": {"type": "number", "required": true, "length": null, "precision": 10, "scale": 0, "mysql": {"columnName": "sstraitant_id", "dataType": "int", "dataLength": null, "dataPrecision": 10, "dataScale": 0, "nullable": "N"}}, "roleId": {"type": "number", "required": true, "length": null, "precision": 10, "scale": 0, "mysql": {"columnName": "role_id", "dataType": "int", "dataLength": null, "dataPrecision": 10, "dataScale": 0, "nullable": "N"}}}, "validations": [], "relations": {}, "scope": {"include": []}, "acls": [{"accessType": "*", "principalType": "ROLE", "principalId": "$everyone", "permission": "DENY"}, {"accessType": "*", "principalType": "ROLE", "principalId": "admin", "permission": "ALLOW"}, {"accessType": "*", "principalType": "ROLE", "principalId": "super-admin", "permission": "ALLOW"}, {"accessType": "*", "principalType": "ROLE", "principalId": "$authenticated", "permission": "ALLOW"}], "methods": {}}