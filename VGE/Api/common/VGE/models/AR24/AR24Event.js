let loopback    = require('loopback');
let app = require('../../../../server/server');
let path = require('path');
let helper = require('../../../helper');

let photos = [];
let files = [];


module.exports = function(AR24Event) {

  AR24Event.observe('loaded', function(ctx, next) {

    if(ctx && ctx.data) {
      helper.replacePathFiles(ctx.data, photos, files);
      next();

    }else{

      next();
    }
  })

  AR24Event.observe('after save', function(ctx, next) {
    if (ctx.isNewInstance) {

      next();
    } else {
      // edit

      next();
    }


  })

}
