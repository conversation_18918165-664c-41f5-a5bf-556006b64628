/**
 * Account Model. Extend standard Loopback User Model
 * Add One And One Relation to Parent AND/OR Child Model
 */

let loopback    = require('loopback');
let app = require('../../../../server/server');
let path = require('path');
let helper = require('../../../helper');

let historizeRemotes = require('../../../historizeRemotes');

module.exports = function(IntraDoExtension) {

  historizeRemotes.historizeRemotes(IntraDoExtension);

}
