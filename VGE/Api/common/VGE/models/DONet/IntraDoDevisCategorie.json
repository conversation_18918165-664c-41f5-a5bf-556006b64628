{"name": "IntraDoDevisCategorie", "options": {"strict": false, "forceId": false, "replaceOnPUT": true, "idInjection": false, "mysql": {"table": "intra_do_devis_categorie"}}, "properties": {"id": {"type": "number", "id": 1, "required": false, "generated": true, "length": null, "precision": 10, "scale": 0, "mysql": {"columnName": "id", "dataType": "int", "dataLength": null, "dataPrecision": 10, "dataScale": 0, "nullable": "N"}, "updateOnly": false}, "libelle": {"type": "string", "required": true, "length": 255, "precision": null, "scale": null, "mysql": {"columnName": "libelle", "dataType": "<PERSON><PERSON><PERSON>", "dataLength": 255, "dataPrecision": null, "dataScale": null, "nullable": "N"}}, "couleur": {"type": "string", "required": true, "length": 50, "precision": null, "scale": null, "mysql": {"columnName": "couleur", "dataType": "<PERSON><PERSON><PERSON>", "dataLength": 50, "dataPrecision": null, "dataScale": null, "nullable": "N"}}, "ordre": {"type": "number", "required": true, "length": null, "precision": 10, "scale": 0, "mysql": {"columnName": "ordre", "dataType": "int", "dataLength": null, "dataPrecision": 10, "dataScale": 0, "nullable": "N"}}}, "validations": [], "relations": {}, "_scope": {"include": []}, "scope": {"include": []}, "acls": [{"accessType": "*", "principalType": "ROLE", "principalId": "$everyone", "permission": "DENY"}, {"accessType": "*", "principalType": "ROLE", "principalId": "admin", "permission": "ALLOW"}, {"accessType": "*", "principalType": "ROLE", "principalId": "super-admin", "permission": "ALLOW"}, {"accessType": "*", "principalType": "ROLE", "principalId": "$authenticated", "permission": "ALLOW"}], "methods": {}}