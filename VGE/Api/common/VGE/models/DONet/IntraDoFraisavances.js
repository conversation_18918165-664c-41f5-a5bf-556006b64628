/**
 * Account Model. Extend standard Loopback User Model
 * Add One And One Relation to Parent AND/OR Child Model
 */

let loopback    = require('loopback');
let app = require('../../../../server/server');
let path = require('path');
let helper = require('../../../helper');

let historizeRemotes = require('../../../historizeRemotes');

module.exports = function(IntraDoFraisavances) {

  // historizeRemotes.historizeRemotes(IntraDoFraisavances, 'libelle');

  IntraDoFraisavances.remoteMethod(
    'getListExpertises', {
      description: 'Get Frais avances with sinistres + operation',
      accepts: [
        {
          arg: 'userId',
          type: 'any',
          required: true,
          http: function(ctx) {
            var req = ctx && ctx.req;
            var accessToken = req && req.accessToken;

            var userId = accessToken && accessToken.userId;

            if(!userId){
              var error = new Error("Aucun token n\'est spécifié");
              error.status = 401;
              error.code = "NO_TOKEN_DEFINED";
              error.body = 'Unauthorized';
              return error;
            }

            return userId;
          },
          description: 'Do not supply this argument, it is automatically extracted ' +
            'from request headers.'
        }
      ],
      returns: {
        arg: 'DOs',
        type: 'array',
        root: true,
        description: 'The response body contains Frais avances'
      },
      http: {
        errorStatus: 401,
        'path': '/getListFraisavances',
        verb: 'get'
      }
    }
  );

  IntraDoFraisavances.getListFraisavances = function(userId, cb) {

    var queryFrais = 'SELECT intra_do_sinistre.*, ' +
      'intra_do_frais_avances.id as idIndemnite, ' +
      'intra_do_frais_avances.libelle as libelleIndemnite,' +
      'intra_do_frais_avances.date as dateIndemnite,' +
      'intra_do_frais_avances.montant as montantIndemnite,' +
      'intra_do_frais_avances.ftp as ftpIndemnite,' +
      'intra_operation.id as idOperation, ' +
      'intra_operation.libelle as libelleOperation, ' +
      'intra_operation.dossierftp as dossierftp, ' +
      'societes.nom as client, ' +
      'intra_do_frais_avances.type as typeIndemnite FROM `intra_do_sinistre` ' +
      ' INNER JOIN `intra_do_frais_avances` ON intra_do_sinistre.id = intra_do_frais_avances.idsinistre' +
      ' INNER JOIN `intra_operation` ON intra_do_sinistre.operation_id = intra_operation.id'+
      ' INNER JOIN `societes` ON intra_operation.idsociete = societes.id' +
      ' WHERE (`isdo` = 1)  AND (`intra_do_sinistre`.`isVGE` = 1 )  AND (`intra_operation`.`isVGE` = 1 ) AND (`intra_do_sinistre`.`enattente` = 0 ) order by client ASC, intra_do_sinistre.year ASC , dateIndemnite DESC';

    var finalArray = {};
    IntraDoFraisavances.getDataSource().connector.execute(queryFrais, null, function(err, fraisavances){

      if(fraisavances && fraisavances.length > 0){
        fraisavances.forEach(function(fraisavance){

          if(!finalArray[fraisavance.client]){
            finalArray[fraisavance.client] = {ops: {}, nbOps:1, total:fraisavance.montantIndemnite};
          }else{
            finalArray[fraisavance.client]['nbOps'] += 1;
            finalArray[fraisavance.client]['total'] += fraisavance.montantIndemnite;
          }

          if(!finalArray[fraisavance.client]['ops'][fraisavance.libelleOperation]){
            finalArray[fraisavance.client]['ops'][fraisavance.libelleOperation] = {years: {}, total:fraisavance.montantIndemnite};
          }else{
            finalArray[fraisavance.client]['ops'][fraisavance.libelleOperation]['total'] += fraisavance.montantIndemnite;
          }

          if(!finalArray[fraisavance.client]['ops'][fraisavance.libelleOperation]['years'][fraisavance.year]){
            finalArray[fraisavance.client]['ops'][fraisavance.libelleOperation]['years'][fraisavance.year] = {indemns: [], nbIndem:1, total: fraisavance.montantIndemnite};
          }else{
            finalArray[fraisavance.client]['ops'][fraisavance.libelleOperation]['years'][fraisavance.year]['total'] += fraisavance.montantIndemnite;
            finalArray[fraisavance.client]['ops'][fraisavance.libelleOperation]['years'][fraisavance.year]['nbIndem'] += 1;
          }

          finalArray[fraisavance.client]['ops'][fraisavance.libelleOperation]['years'][fraisavance.year]['indemns'].push(fraisavance);

        });
      }


      cb(null, finalArray);
    });
  };




}
