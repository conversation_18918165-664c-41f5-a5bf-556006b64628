{"name": "IntraDoFtp", "options": {"strict": false, "forceId": false, "replaceOnPUT": true, "idInjection": false, "mysql": {"table": "intra_do_ftp"}}, "properties": {"id": {"type": "number", "id": 1, "required": false, "generated": true, "length": null, "precision": 10, "scale": 0, "mysql": {"columnName": "id", "dataType": "int", "dataLength": null, "dataPrecision": 10, "dataScale": 0, "nullable": "N"}, "updateOnly": false}, "name": {"type": "string", "required": true, "length": 1024, "precision": null, "scale": null, "mysql": {"columnName": "name", "dataType": "<PERSON><PERSON><PERSON>", "dataLength": 1024, "dataPrecision": null, "dataScale": null, "nullable": "N"}}, "url": {"type": "string", "required": false, "length": 1024, "precision": null, "scale": null, "mysql": {"columnName": "url", "dataType": "<PERSON><PERSON><PERSON>", "dataLength": 1024, "dataPrecision": null, "dataScale": null, "nullable": "Y"}}, "type": {"type": "number", "required": true, "length": null, "precision": 3, "scale": 0, "mysql": {"columnName": "type", "dataType": "tinyint", "dataLength": null, "dataPrecision": 3, "dataScale": 0, "nullable": "N"}}, "parentId": {"type": "number", "required": false, "length": null, "precision": 10, "scale": 0, "mysql": {"columnName": "parent_id", "dataType": "int", "dataLength": null, "dataPrecision": 10, "dataScale": 0, "nullable": "Y"}}, "level": {"type": "number", "required": false, "length": null, "precision": 3, "scale": 0, "mysql": {"columnName": "level", "dataType": "tinyint", "dataLength": null, "dataPrecision": 3, "dataScale": 0, "nullable": "Y"}}, "ordre": {"type": "number", "required": true, "length": null, "precision": 10, "scale": 0, "mysql": {"columnName": "ordre", "dataType": "int", "dataLength": null, "dataPrecision": 10, "dataScale": 0, "nullable": "N"}}, "sinistreId": {"type": "number", "required": true, "length": null, "precision": 10, "scale": 0, "mysql": {"columnName": "sinistre_id", "dataType": "int", "dataLength": null, "dataPrecision": 10, "dataScale": 0, "nullable": "N"}}, "extra": {"type": "string", "required": true, "length": 50, "precision": null, "scale": null, "mysql": {"columnName": "extra", "dataType": "<PERSON><PERSON><PERSON>", "dataLength": 50, "dataPrecision": null, "dataScale": null, "nullable": "N"}}}, "validations": [], "relations": {}, "scope": {"include": []}, "acls": [{"accessType": "*", "principalType": "ROLE", "principalId": "$everyone", "permission": "DENY"}, {"accessType": "*", "principalType": "ROLE", "principalId": "admin", "permission": "ALLOW"}, {"accessType": "*", "principalType": "ROLE", "principalId": "super-admin", "permission": "ALLOW"}, {"accessType": "*", "principalType": "ROLE", "principalId": "$authenticated", "permission": "ALLOW"}], "methods": {}}