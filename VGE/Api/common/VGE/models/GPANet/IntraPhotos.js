/**
 * Account Model. Extend standard Loopback User Model
 * Add One And One Relation to Parent AND/OR Child Model
 */

const loopback    = require('loopback');
const app = require('../../../../server/server');
const path = require('path');
const helper = require('../../../helper');
const mailer = require('../../../mailer');
const striptags = require('striptags');
const photos = ['photo'];
const files = [];

let historizeRemotes = require('../../../historizeRemotes');

module.exports = function(IntraPhotos) {

  // historizeRemotes.historizeRemotes(IntraPhotos);

  IntraPhotos.observe('loaded', function(ctx, next){

    if(ctx && ctx.data) {

      helper.replacePathFiles(ctx.data, photos, files);

    }
    next();
  });

  /**
   * Get relation & return after save
   */
  IntraPhotos.observe('after save', function(ctx, next) {

    if(ctx.isNewInstance){

      IntraPhotos.findOne({where:{id: ctx.instance.id}, include:['createur', 'reclamation']}, function(err, file){


        if(ctx.options && ctx.options.accessToken && ctx.options.accessToken.userId) {
          helper.getPeopleFromId(ctx.options.accessToken.userId, function (err, currentUser) {
            if (err) {next(err);}

            /**
             * Operation getResponsablesEmailSend
             */
            /**
             * reclamation responsables entreprise
             */
            let reclamation = file.reclamation();
            reclamation.operation(function(err, operation){

              let subject = 'Opération ' + operation.libelle + ' - Réclamation N°' + reclamation.num + ' ' + striptags(reclamation.description) + ', Ajout d\'une image';
              var options = {
                operation: operation,
                reclamation: reclamation,
                acteur: currentUser.firstname + ' ' + currentUser.lastname
              };

              let promises = [];

              operation.getResponsablesEmailSend(function(err, responsables){

                responsables.forEach(function(resp){
                  let people = resp.people();

                  if(people && people.email){
                    options['nom'] = people.lastname + ' ' + people.firstname;
                    promises.push(mailer.sendMail({name:people.lastname + ' ' + people.firstname, address: people.email}, [], [], subject, 'reclamation/ajoutimage.ejs', options, [], null, currentUser, {entity: 'IntraPhotos', id: ctx.instance.id}));
                  }
                });

                Promise.all(promises).then(function(){
                  IntraPhotos.returnNext(ctx, next);
                }).catch(function(err){
                  next(err);
                })

              });
            });
          })
        } else {
          IntraPhotos.returnNext(ctx, next);
        }
      });
    }else{
      IntraPhotos.returnNext(ctx, next);
    }
  });

  IntraPhotos.returnNext = function(ctx, cb) {
    ctx.instance.createur(function (err, createur) {
      ctx.instance.__data.createur = createur;
      /**
       * Retour avec PATH
       * @type {string[]}
       */
      helper.replacePathFiles(ctx.instance.__data, photos, files);

      cb();
    });
  }

  IntraPhotos.observe('before delete', function(ctx, next){
    helper.deletePathFiles(ctx.instance.__data, photos, files);
    next();
  })


}
