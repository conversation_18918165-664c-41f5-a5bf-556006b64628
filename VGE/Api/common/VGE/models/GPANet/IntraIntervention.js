/**
 * Account Model. Extend standard Loopback User Model
 * Add One And One Relation to Parent AND/OR Child Model
 */
var loopback    = require('loopback');
var app = require('../../../../server/server');
var path = require('path');
var moment = require('moment-timezone');
var helper = require('../../../helper');
var mailer = require('../../../mailer');

module.exports = function(IntraIntervention) {


  IntraIntervention.observe('after save', function(ctx, next) {

    if(ctx.isNewInstance) {

      const PeopleModel = app.models.People;
      const modelHistorique = loopback.getModel('IntraHistorique');
      let historyModel = loopback.getModel('IntraEbHistory');

      const intervenants = ctx.instance.peopleIntervenants;


      if(ctx.instance && ctx.instance.dontcreatemail){
        return next();
      }

      IntraIntervention.findOne({where:{id: ctx.instance.id}}, function(err, intervention){
        if(err){next(err)}

        intervention.reclamation({include:['operation']}, function(err, reclamation){
          if(err){next(err)}

            const operation = reclamation.operation();

            intervention.demandeur(function(err, demandeur){
              if(err){next(err)}

              const statut = 6; // Demande intervention CODE DI

              reclamation.updateAttributes({
                dateetat: moment(),
                etat_id: statut,
              }, function(err, reclam){
                if(err){next(err);}

                let stringIntervention = '';

                if (intervention.description) {
                  stringIntervention += 'Description : ' + intervention.description;
                }

                if (intervention.commentaire) {
                  stringIntervention += ((stringIntervention != '') ? '<br/>' : '') + 'Commentaire : ' + intervention.commentaire + '<br/>';
                }

                const datas = {
                  'date' : moment(),
                  'etatId' : statut,
                  'userId' : intervention.auteurId,
                  'observations' : stringIntervention,
                  'reclamationId' : reclamation.id,
                  'interventionId' : intervention.id,
                };

                // console.log('datas Historique ',datas);

                modelHistorique.create(datas, function(err, historique){
                  if(err){next(err);}

                  // console.log('Historique persisted', historique)

                  /*** Send MAILS ! ***/

                  let subject = 'Reclamation N°'+reclamation.num +' ' + operation.libelle ;

                  const promises = [];
                  intervenants.forEach(function(el){ // el : id

                    const p = new Promise(function (resolve, reject) {

                      PeopleModel.findOne({where:{id: el}}, function(err, people){
                        if(err){reject(err)}

                        var options = {
                          nom: people.lastname +' '+people.firstname,
                          operation: operation,
                          reclamation: reclamation,
                          demandeur: demandeur
                        };

                        mailer.sendMail({name:people.lastname + ' ' + people.firstname, address: people.email}, [], [], subject + ' demande une intervention de votre société', 'reclamation/demandeintervention.ejs', options, [], null, demandeur, {entity: 'IntraIntervention', id: ctx.instance.id}).then(function(){
                          return resolve();
                        }).catch(function(err){
                          return reject(err);
                        })

                      })

                    });
                    promises.push(p);
                  })

                  Promise.all(promises).then(function(){

                    historyModel.historize({
                      type:'intervention',
                      peopleId: (ctx.options && ctx.options.accessToken && ctx.options.accessToken.userId) ? ctx.options.accessToken.userId : null,
                      classObject: 'IntraIntervention',
                      idObject: reclamation.id,
                      idObjectOperation: operation.id,
                      module:'GPAnet',
                      explain:'Demande d\'une intervention : ' + subject
                    }, function(err, res){
                      if(err){console.log(err);}
                      /* Done !*/
                      next()
                    });



                  }).catch(function(err){
                    next(err);
                  });
                  /*** END Send MAILS ! ***/

                });
              });
            })
        })
      });
      }else{
        next();
      }

  });

}
