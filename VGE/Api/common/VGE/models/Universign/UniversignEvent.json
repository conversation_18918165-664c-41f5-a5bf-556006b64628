{"name": "UniversignEvent", "options": {"strict": false, "forceId": false, "replaceOnPUT": true, "idInjection": false, "mysql": {"table": "universign_events"}}, "properties": {"id": {"type": "number", "id": 1, "required": false, "generated": true, "length": null, "precision": 10, "scale": 0, "mysql": {"columnName": "id", "dataType": "int", "dataLength": null, "dataPrecision": 10, "dataScale": 0, "nullable": "N"}, "updateOnly": false}, "date": {"type": "date", "required": true, "length": null, "precision": null, "scale": null, "mysql": {"columnName": "date", "dataType": "datetime", "dataLength": null, "dataPrecision": null, "dataScale": null, "nullable": "N"}}, "event": {"type": "string", "required": false, "precision": null, "scale": null, "mysql": {"columnName": "event", "dataType": "text", "dataLength": 65535, "dataPrecision": null, "dataScale": null, "nullable": "Y"}}, "universign_id": {"type": "number", "required": false, "length": null, "precision": 10, "scale": 0, "mysql": {"columnName": "universign_id", "dataType": "int", "dataLength": null, "dataPrecision": 10, "dataScale": 0, "nullable": "N"}}}, "validations": [], "relations": {}, "scope": {}, "acls": [{"accessType": "*", "principalType": "ROLE", "principalId": "$everyone", "permission": "DENY"}, {"accessType": "*", "principalType": "ROLE", "principalId": "admin", "permission": "ALLOW"}, {"accessType": "*", "principalType": "ROLE", "principalId": "super-admin", "permission": "ALLOW"}, {"accessType": "*", "principalType": "ROLE", "principalId": "$authenticated", "permission": "ALLOW"}], "methods": {}}