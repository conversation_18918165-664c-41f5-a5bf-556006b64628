/**
 * Account Model. Extend standard Loopback User Model
 * Add One And One Relation to Parent AND/OR Child Model
 */

const loopback    = require('loopback');
const app = require('../../../../server/server');
let moment = require('moment-timezone');
const helper = require('../../../helper');
const fs = require('fs');
const glob = require("glob")
const emlformat = require('eml-format');
const mailer = require('../../../mailer');

const photos = [];
const files = ['filebilan'];

let rootPath = app.dataSources['containerFile'].settings.root || app.get('containerFile')['root'];

let historizeRemotes = require('../../../historizeRemotes');
const cacheSystem = require("../../../cache");

module.exports = function(IntraDoSinistreVGS) {

  historizeRemotes.historizeRemotes(IntraDoSinistreVGS, 'referencevgs');

  IntraDoSinistreVGS.observe('loaded', function(ctx, next) {

    if(ctx && ctx.data){

      if(!ctx.data['year'] || ctx.data['year'] == '0000'){
        var r = new RegExp('DO ([0-9]{4})\.([0-9]+)', 'gi')
        var arrayR = r.exec(ctx.data.referencevge);
        if(ctx.data.referencevge && arrayR !== null){
          ctx.data['year'] = arrayR[1];
        }else{
          if(ctx.data && ctx.data.date){
            var date = moment(ctx.data.date, 'YYYY-MM-DD');
            ctx.data['year'] = date.format('YYYY');
          }else{
            ctx.data['year'] = 1000;
          }
        }
      }

      if(ctx.data.state !== null){
        ctx.data['stateLibelle'] = (ctx.data.state == 0) ? 'En cours' : 'Cloturé';
      }

      helper.replacePathFiles(ctx.data, photos, files);

    }

    next();
  });

  IntraDoSinistreVGS.observe('before save', function(ctx, next) {
    if(ctx) {
      if (ctx.isNewInstance && ctx.instance) {
        ctx.instance.isVGS = 1;
        ctx.instance.isVGE = 1;
        ctx.instance.isVGI = 0;
        ctx.instance.source = 'VGS';
      }
    }
    next();
  });

  IntraDoSinistreVGS.observe('after save', function(ctx, next) {

    if(ctx.options && ctx.options.accessToken && ctx.options.accessToken.userId){

      let IntraDoSinistreVGS = loopback.getModel('IntraDoSinistreVGS');
      let IntraDoSinistre = loopback.getModel('IntraDoSinistre');
      let IntraDoHistoriqueVGS = loopback.getModel('IntraDoHistoriqueVGS');
      let IntraDoHistorique = loopback.getModel('IntraDoHistorique');
      let IntraDoOperation = loopback.getModel('IntraDoOperation');
      const People = loopback.getModel('People');

      // var userId = ctx.options.accessToken.userId;
      helper.getPeopleFromId(ctx.options.accessToken.userId, function(err, user){
        if(err){next(err)}

        /**** SEND EMAILs ! ****/

        if(ctx.isNewInstance){
          if(ctx.instance.admins){


            People.getAdmins(function(err, adminsfromDB){
              if(err){return next(err);}
              IntraDoSinistreVGS.findOne({where:{id: ctx.instance.id}},function(err, sinistre){
                if(err){return next(err);}
                const adminsTos = [];

                let admins = null;
                if(typeof ctx.instance.admins == 'string'){
                  admins = ctx.instance.admins.trim();
                  admins = JSON.parse(admins); // Array<Ids>
                }else{
                  admins = ctx.instance.admins;
                }

                if(admins){
                  admins.forEach((adm) => {
                    let admin = adminsfromDB.find(function(e){return e.id == adm});
                    if(admin){
                      adminsTos.push(admin); // On Add un people !!!!
                    }
                  })
                }

                IntraDoOperation.findOne({where: {id: ctx.instance.operationId}}, function(err, operation){
                  if(err){ return next(err);}

                  let promises = [];

                  let ejs = 'vgs/ajoutchantier.ejs';
                  let options = {
                    signature: user.signature
                  };

                  let subject = 'TXnet - Une demande de devis VGS a été demandé pour le sinistre référence VGE :'+sinistre.referencevge + ', Référence VGS : '+sinistre.referencevgs + ', opération '+ operation.libelle;

                  /*  */
                  let entete = "<br/><br/>- Une demande de devis VGS a été demandé pour le sinistre reférénce VGE : <strong>"+sinistre.referencevge+"</strong> sur l'opération "+ operation.libelle+"<br />";

                  var fromAdr = (ctx.instance.from) ? ctx.instance.from : null;

                  options['entete'] = entete;
                  if(ctx.instance.emailbody){
                    options['body'] = ctx.instance.emailbody;
                  }else{
                    options['body'] = '';
                  }

                  // if(ctx.instance.ftps) {
                  //   options['ftps'] = ctx.instance.ftps;
                  // }else{
                  //   options['ftps'] = [];
                  // }

                  if(ctx.instance.ftp) {
                    options['ftps'] = [ctx.instance.ftp];
                  }else{
                    options['ftps'] = [];
                  }

                  // OK
                  let address = [];
                  let tosAdressNameObjectEMLArray = [];
                  if(adminsTos && adminsTos.length > 0) {
                    adminsTos.forEach(function (people) {
                      if (people && people.email) {
                        address.push({
                          name: people.lastname + ' ' + people.firstname,
                          address: people.email
                        });

                        tosAdressNameObjectEMLArray.push({
                          name: people.lastname + ' ' + people.firstname,
                          address: people.email
                        });
                      }
                    })
                  }

                  let attachements = [];

                  options['signature'] = user.signature;

                  // 1 Seul mail d'envoyé maintenant !!
                  promises.push(
                      mailer.sendMail(address,  [], [], subject, ejs, options, attachements, fromAdr, user)
                  );

                  Promise.all(promises).then(function(){

                    /* END  */
                    mailer.toEML(sinistre.id, tosAdressNameObjectEMLArray, [], [], subject, ejs, options, attachements, fromAdr, 'emailsChantier/chantier-', function(err, eml){
                      if(err){ return next(err);}

                      sinistre.updateAttributes(
                        {
                          'isVGS': 1,
                        }, function(err, sinistreUpdt){
                          if(err){ return next(err);}

                          operation.updateAttributes({
                            'isVGS': 1
                          }, function(err, opUpdt){
                            if(err){ return next(err);}

                            IntraDoHistoriqueVGS.create({
                              date: moment(),
                              observations: '',
                              etat_id: 0,
                              userId: ctx.options.accessToken.userId,
                              user_id: ctx.options.accessToken.userId,
                              sinistreId: sinistreUpdt.id,
                              type: 0,
                              email: eml.filenameToDb,
                              to: address.map((e) => e.address).join(', '),
                              auto: 1,
                              countRappel: 0,
                              rappelAuto: 0,
                              comment: 'Ajout du chantier via VGE depuis le sinistre '+ sinistre.referencevge,
                              state: 0,
                              id_etat_suivi: 6 // classique
                            }, function(err, histoVGS){
                              if(err){console.error(err);}


                              IntraDoSinistre.findOne({where:{id: ctx.instance.sinistreIdVGE}},function(err, sinistreVGE){
                                if(err){return next(err);}

                                sinistreVGE.updateAttributes(
                                  {
                                    'referencevgs': sinistre.referencevgs,
                                    'isVGS': 1,
                                  }, function(err, sinistreVGEUpdt){
                                    // ADD IntraDoHistorique ! --> sinistreIdVGE
                                    IntraDoHistorique.create({
                                      date: moment(),
                                      observations: '',
                                      etat_id: 0,
                                      userId: ctx.options.accessToken.userId,
                                      user_id: ctx.options.accessToken.userId,
                                      sinistreId: sinistreVGE.id,
                                      type: 0,
                                      email: eml.filenameToDb,
                                      to: address.map((e) => e.address).join(', '),
                                      auto: 1,
                                      countRappel: 0,
                                      rappelAuto: 0,
                                      comment: 'Ajout d\'un chantier via demande de devis VGS, référence VGS : ' + sinistre.referencevgs,
                                      state: 0,
                                      id_etat_suivi: 6 // classique
                                    }, function(err, histoVGE){

                                      cacheSystem.unvalidateCacheByType('sinistre');
                                      cacheSystem.unvalidateCacheByType('operation');

                                      return next();
                                    })
                                  });
                              })
                            })
                          })
                        });
                    });
                  }).catch(function(err){
                    return next(err);
                  })
                });
              });
            })
          } else {
            // NO MAIL
            return next();
          }
        } else {
          // NO MAIL
          return next();
        }
      })
    } else {
      return next();
    }

  });


}
