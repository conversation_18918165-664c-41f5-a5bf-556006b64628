{"name": "IntraMultiPhotosVGS", "options": {"strict": false, "forceId": false, "replaceOnPUT": true, "idInjection": false, "mysql": {"table": "intra_multi_photos"}}, "properties": {"id": {"type": "number", "id": 1, "required": false, "generated": true, "length": null, "precision": 10, "scale": 0, "mysql": {"columnName": "id", "dataType": "int", "dataLength": null, "dataPrecision": 10, "dataScale": 0, "nullable": "N"}, "updateOnly": false}, "photo": {"type": "string", "required": true, "length": 255, "precision": null, "scale": null, "mysql": {"columnName": "photo", "dataType": "<PERSON><PERSON><PERSON>", "dataLength": 255, "dataPrecision": null, "dataScale": null, "nullable": "N"}}, "date": {"type": "date", "required": true, "length": null, "precision": null, "scale": null, "mysql": {"columnName": "date", "dataType": "datetime", "dataLength": null, "dataPrecision": null, "dataScale": null, "nullable": "N"}}, "peopleId": {"type": "number", "required": false, "length": null, "precision": 10, "scale": 0, "mysql": {"columnName": "people_id", "dataType": "int", "dataLength": null, "dataPrecision": 10, "dataScale": 0, "nullable": "N"}}, "sinistreId": {"type": "number", "required": false, "length": null, "precision": 10, "scale": 0, "mysql": {"columnName": "sinistre_id", "dataType": "int", "dataLength": null, "dataPrecision": 10, "dataScale": 0, "nullable": "N"}}}, "validations": [], "relations": {"createur": {"type": "belongsTo", "model": "People", "foreignKey": "people_id"}, "sinistre": {"type": "belongsTo", "model": "IntraMultiSinistreVGS", "foreignKey": "sinistre_id"}}, "scope": {"include": [{"relation": "<PERSON>ur", "scope": {"fields": ["id", "firstname", "lastname", "email"]}}]}, "acls": [{"accessType": "*", "principalType": "ROLE", "principalId": "$everyone", "permission": "DENY"}, {"accessType": "*", "principalType": "ROLE", "principalId": "admin", "permission": "ALLOW"}, {"accessType": "*", "principalType": "ROLE", "principalId": "super-admin", "permission": "ALLOW"}, {"accessType": "*", "principalType": "ROLE", "principalId": "$authenticated", "permission": "ALLOW"}], "methods": {}}