{"name": "IntraDevDevelopment", "plural": "IntraDevDevelopments", "base": "PersistedModel", "idInjection": true, "options": {"validateUpsert": true, "mysql": {"table": "intra_dev_development"}}, "properties": {"urgent": {"type": "number", "required": true, "mysql": {"dataType": "TINYINT(1)"}}, "description": {"type": "string", "required": true, "mysql": {"dataType": "TEXT"}}, "pj": {"type": "string", "required": false, "mysql": {"dataType": "VARCHAR", "length": 1024}}, "pj2": {"type": "string", "required": false, "mysql": {"dataType": "VARCHAR", "length": 1024}}, "screenshot": {"type": "string", "required": false, "mysql": {"dataType": "VARCHAR", "length": 1024}}, "screenshot2": {"type": "string", "required": false, "mysql": {"dataType": "VARCHAR", "length": 1024}}, "libelle": {"type": "string", "required": true, "mysql": {"dataType": "VARCHAR", "length": 255}}, "dateCreation": {"type": "date", "defaultFn": "now", "required": false, "mysql": {"dataType": "DATETIME"}}, "avancement": {"type": "number", "required": true, "default": 0, "mysql": {"dataType": "INT", "length": 3}, "min": 0, "max": 100}, "type": {"type": "string", "required": true, "mysql": {"dataType": "ENUM", "enum": ["Bug", "Évolution", "<PERSON><PERSON><PERSON>"]}}, "etat": {"type": "string", "required": true, "mysql": {"dataType": "ENUM", "enum": ["En étude", "En dév", "Développé", "en attente de livraison", "livré"]}}, "createur_id": {"type": "number", "required": false, "mysql": {"dataType": "INT"}}, "datelivraisonEst": {"required": false, "type": "date", "mysql": {"dataType": "DATETIME"}}, "dateLivr": {"required": false, "type": "date", "mysql": {"dataType": "DATETIME"}}, "date": {"type": "date", "mysql": {"dataType": "DATETIME"}}, "section": {"type": "string", "required": true, "mysql": {"dataType": "ENUM", "enum": ["VGE - DOnet", "VGE - DOstat", "VGE - GPAnet", "VGE - eBusiness", "VGE - eBoarding", "VGI - 1Gstat", "VGI - 1Gnet", "VGI - REZnet", "VGS - TXnet", "VGI - HealthBoard", "Global - Gestion utilisateurs", "Global - Gestion Sociétés", "Global - Gestion Immeubles", "Autres"]}}, "devis": {"type": "string", "required": false, "mysql": {"dataType": "VARCHAR", "length": 1024}}}, "validations": [], "relations": {"createur": {"type": "belongsTo", "model": "People", "foreignKey": "createur_id"}, "historiques": {"type": "hasMany", "model": "IntraDevHistorique", "foreignKey": "development_id"}, "files": {"type": "hasMany", "model": "IntraDevFile", "foreignKey": "development_id"}, "responsable": {"type": "belongsTo", "model": "People", "foreignKey": "responsable_id"}, "responsable2": {"type": "belongsTo", "model": "People", "foreignKey": "responsable2_id"}, "responsable3": {"type": "belongsTo", "model": "People", "foreignKey": "responsable3_id"}, "responsable4": {"type": "belongsTo", "model": "People", "foreignKey": "responsable4_id"}, "responsable5": {"type": "belongsTo", "model": "People", "foreignKey": "responsable5_id"}}, "scope": {"include": [{"relation": "<PERSON>ur", "scope": {"fields": ["id", "firstname", "lastname", "email", "isVGE", "isVGI", "isVGS", "image"]}}, {"relation": "responsable", "scope": {"fields": ["id", "firstname", "lastname", "email", "image"]}}, {"relation": "responsable2", "scope": {"fields": ["id", "firstname", "lastname", "email", "image"]}}, {"relation": "responsable3", "scope": {"fields": ["id", "firstname", "lastname", "email", "image"]}}, {"relation": "responsable4", "scope": {"fields": ["id", "firstname", "lastname", "email", "image"]}}, {"relation": "responsable5", "scope": {"fields": ["id", "firstname", "lastname", "email", "image"]}}, "historiques", "files"]}, "acls": [], "methods": {}}