const app = require('../../../server/server');
const exportPDF = require('../../exportPDFEbuz');
const moment = require('moment-timezone');
moment.locale('fr');
const helper = require("../../helperEbuz");
const sharp = require('sharp');

const convertImageHTTPtoPath = require('../../exportPDF').convertImageHTTPtoPath;

const rootPath = app.dataSources['containerImage'].settings.root;
const httpPath = app.get("VGE").PATHS.image.fullpath;

const { CanvasRenderService } = require('chartjs-node-canvas');
const chartJsPluginLabels = require('chartjs-plugin-labels');

/*  EXPORT ExportFacture */
module.exports.ExportPlanTresorerie = async function(year, planTresorerie, debug = 0, res, landscape=true, sendToRes=true, saveToFile=false, folderToSave=null, typeFile = 'PDF', cb=null){ // Res = res du call !

  // var root = app.dataSources['containerFile'].settings.root;
  // var pathEML = root + '/' + 'DOnet' + '/' + 'synoptique' + '/' + operation.id + '/';
  // fs.existsSync(pathEML) || fs.mkdirSync(pathEML);

  const filename = `PlanTresorerie-${year}.pdf`;

  /***
   * Traitement des images
   */
  var promises = [];

  var imglogoVGE = null;
  promises.push(
    sharp(rootPath+'/'+'logo.png')
      // .resize(null, 40)
      .png()
      .toBuffer().then(function(dataLogo) {
      return imglogoVGE =  `data:image/png;base64,${dataLogo.toString('base64')}`;
    }).catch(function(err) { console.log(err);return err; })
  );


  /***
   * END - Traitement des images
   */

  return Promise.all(promises).then(async function(){

    const months = [];
    for(let i=1; i<= 12; i++){
      months.push({
        lib: moment(`${year}-${i}`, 'YYYY-M').format('MMM-YY'),
        month: i,
        year: year
      })
    }

    let options = {
      vgeLogo: imglogoVGE,
      planTresorerie: planTresorerie,
      year: year,
      moment: moment,
      months: months,
      currentDate: moment().format('DD/MM/YYYY'),
      currentYear: year,
    };

    if(debug){
      exportPDF.showHTML('intraEbuizOperation/planTresorerie.ejs', filename, options, res, function(err, result){ // DEBUG Show HTML
        return cb(err, result);
      });
    }else{
      return await exportPDF.exportPDF('intraEbuizOperation/planTresorerie.ejs', filename, options, res, landscape, sendToRes, saveToFile, folderToSave, typeFile, password = null, cb);
    }
  })
};

