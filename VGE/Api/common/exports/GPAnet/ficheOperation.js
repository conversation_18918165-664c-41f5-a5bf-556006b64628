const app = require('../../../server/server');
const exportPDF = require('../../exportPDF');
const moment = require('moment-timezone');
moment.locale('fr');

const sharp = require('sharp');

const { CanvasRenderService } = require('chartjs-node-canvas');
const convertImageHTTPtoPath = require('../../exportPDF').convertImageHTTPtoPath;
const normalizePath = require('../../helper').normalizePath
const rootPath = app.dataSources['containerImage'].settings.root || app.get('containerImage')['root'];
const httpPath = app.get("VGE").PATHS.image.fullpath;

const ExcelJS = require('exceljs');
const accounting = require('accounting');
const exportXLSX = require('../../exportXLSX');
const helper = require("../../helper");

module.exports.ExportXLSXFicheOperation = async function(operation ,debug = 0, res, landscape= true, sendToRes=true, saveToFile=false, folderToSave=null, typeFile="PDF", cb=null){

  var filename = 'Export-fiche-operation-GPAnet-' + normalizePath(operation.operation.libelle) + '.xlsx'

  var promises = [];

  var imglogoVGE = null;
  promises.push(
    sharp(rootPath+'/'+'logo.png')
      .resize(null, 40)
      .png()
      .toBuffer().then(function(dataLogo) {
      return imglogoVGE =  `data:image/png;base64,${dataLogo.toString('base64')}`;
    }).catch(function(err) { console.log(err);return err; })
  );

  var chartsOptions = {
    type:'doughnut',
    data: {},
    options : {
      responsive: true,
      maintainAspectRatio: false,
      width: 420,
      height: 250,

      /* demi CIRCLE */
      circumference:  Math.PI,
      rotation: -Math.PI,

      /* CIRCLE */
      circumference: 2 * Math.PI,
      rotation: -Math.PI / 2,

      legend: {
        display: false,
        position: 'bottom',
        fontSize: 10,
        align:'left',
        fullWidth: true,
        reverse: true,
        padding: 5,

      },
      title:{
        display:true,
        text: 'Graphique des sinistres'
      },
      layout: {
        padding: {
          left: 5,
          right: 5,

          top: 5,
          bottom: 5
        }
      },
      plugins: {
        labels: [
          {
            render: 'percentage',
            fontColor:'#ffffff',
            arc: false,
            position: 'border'
          }
        ]
      }
    }
  }

  var Global = { data: { labels: [], datasets: [] }};
  var GPA = { data: { labels: [], datasets: [] }};
  var GBF = { data: { labels: [], datasets: [] }};

  if(operation.GLOBAL){
    Global.data.labels = [];
    Global.data.datasets[0] = {data: [], backgroundColor: [], borderWidth: 1, label: ''};

    operation.GLOBAL.details.forEach((detail) => {
      Global.data.labels.push(detail.libelle)
      Global.data.datasets[0].data.push(detail.nbEtat);
      Global.data.datasets[0].backgroundColor.push(detail.couleur);
    });
  }

  if(operation.GBF){
    GBF.data.labels = [];
    GBF.data.datasets[0] = {data: [], backgroundColor: [], borderWidth: 1, label: ''};

    operation.GBF.details.forEach((detail) => {
      GBF.data.labels.push(detail.libelle)
      GBF.data.datasets[0].data.push(detail.nbEtat);
      GBF.data.datasets[0].backgroundColor.push(detail.couleur);
    });
  }

  if(operation.GPA){
    GPA.data.labels = [];
    GPA.data.datasets[0] = {data: [], backgroundColor: [], borderWidth: 1, label: ''};

    operation.GPA.details.forEach((detail) => {
      GPA.data.labels.push(detail.libelle)
      GPA.data.datasets[0].data.push(detail.nbEtat);
      GPA.data.datasets[0].backgroundColor.push(detail.couleur);
    });
  }

  /* Base Canvas */
  const width = 420; //px
  const height = 350; //px
  const canvasRenderService = new CanvasRenderService(width, height, (ChartJS) => {

  });

  chartsOptions.data = Global.data;
  chartsOptions.options.title.text = 'Réclamations générales';
  var GLOBALCanvas = null;
  promises.push(
    canvasRenderService.renderToBuffer(chartsOptions).then((res) => {
      GLOBALCanvas = `data:image/png;base64,${res.toString('base64')}`;
    }).catch(function(err){ console.log(err)})
  );

  chartsOptions.data = GPA.data;
  chartsOptions.options.title.text = 'Réclamations GPA';
  var GPACanvas = null;
  promises.push(
    canvasRenderService.renderToBuffer(chartsOptions).then((res) => {
      GPACanvas = `data:image/png;base64,${res.toString('base64')}`;
    }).catch(function(err){ console.log(err)})
  );

  chartsOptions.data = GBF.data;
  chartsOptions.options.title.text = 'Réclamations GBF';
  var GBFCanvas = null;
  promises.push(
    canvasRenderService.renderToBuffer(chartsOptions).then((res) => {
      GBFCanvas = `data:image/png;base64,${res.toString('base64')}`;
    }).catch(function(err){ console.log(err)})
  );

  /***
   * END - Traitement des images
   */

  return Promise.all(promises).then(async function(){

    /** EXL here ! **/
    console.log('Création Export fiche GPAnet' + typeFile, ((folderToSave) ? folderToSave + '/' : '') + filename)

    const workbook = new ExcelJS.Workbook();

    workbook.creator = 'Vieira Global Expertise';
    workbook.created = new Date();
    workbook.modified = new Date();

    const titre = `Fiche GPAnet ${operation.operation.libelle}`

    let worksheet;
    let resHeader;
    let rowIndex;

    worksheet = exportXLSX.addWorksheet(workbook, titre, 'Graphiques');
    resHeader = exportXLSX.addHeader(workbook, worksheet, imglogoVGE, titre, 'L1');
    worksheet = resHeader.worksheet;
    rowIndex = resHeader.newRowIndex;

    // graphs
    heightImageLine = 14;

    let rowIndexLineImage1 = rowIndex;
    const image1 = workbook.addImage({
      base64: GLOBALCanvas,
      extension: 'png',
    });
    worksheet.addImage(image1, `A${rowIndexLineImage1}:D${rowIndexLineImage1+heightImageLine}`);
    // Legendes
    rowIndexLineImage1 += heightImageLine + 1;

    operation.GLOBAL.details.forEach(function(detail){
      exportXLSX.writeMergedCells(worksheet, `A${rowIndexLineImage1}`, `A${rowIndexLineImage1}`, `${detail.nbEtat}`, Object.assign({}, {alignment: { wrapText: false, horizontal: "center", vertical: "middle", }}, exportXLSX.getBackgroundColor(detail.couleur, "#ffffff")));
      exportXLSX.writeMergedCells(worksheet, `B${rowIndexLineImage1}`, `D${rowIndexLineImage1}`, `${detail.libelle} (${(detail.nbEtat / operation.GLOBAL.nb * 100).toFixed(2)}}`, Object.assign({}, {alignment: { wrapText: false, horizontal: "left", vertical: "middle", }}));
      rowIndexLineImage1+=1;
    })

    let rowIndexLineImage2 = rowIndex;
    const image2 = workbook.addImage({
      base64: GPACanvas,
      extension: 'png',
    });
    worksheet.addImage(image2, `E${rowIndexLineImage2}:H${rowIndexLineImage2+heightImageLine}`);
    rowIndexLineImage2 += heightImageLine + 1;
    // Legendes

    operation.GLOBAL.details.forEach(function(detail){
      exportXLSX.writeMergedCells(worksheet, `E${rowIndexLineImage2}`, `E${rowIndexLineImage2}`, `${detail.nbEtat}`, Object.assign({}, {alignment: { wrapText: false, horizontal: "center", vertical: "middle", }}, exportXLSX.getBackgroundColor(detail.couleur, "#ffffff")));
      exportXLSX.writeMergedCells(worksheet, `F${rowIndexLineImage2}`, `H${rowIndexLineImage2}`, `${detail.libelle} (${(detail.nbEtat / operation.GPA.nb * 100).toFixed(2)}}`, Object.assign({}, {alignment: { wrapText: false, horizontal: "left", vertical: "middle", }}));
      rowIndexLineImage2+=1;
    })

    let rowIndexLineImage3 = rowIndex;
    const image3 = workbook.addImage({
      base64: GBFCanvas,
      extension: 'png',
    });
    worksheet.addImage(image3, `I${rowIndexLineImage3}:L${rowIndexLineImage3+heightImageLine}`);
    rowIndexLineImage3 += heightImageLine + 1;
    // Legendes

    operation.GLOBAL.details.forEach(function(detail){
      exportXLSX.writeMergedCells(worksheet, `I${rowIndexLineImage3}`, `I${rowIndexLineImage3}`, `${detail.nbEtat}`, Object.assign({}, {alignment: { wrapText: false, horizontal: "center", vertical: "middle", }}, exportXLSX.getBackgroundColor(detail.couleur, "#ffffff")));
      exportXLSX.writeMergedCells(worksheet, `J${rowIndexLineImage3}`, `L${rowIndexLineImage3}`, `${detail.libelle} (${(detail.nbEtat / operation.GBF.nb * 100).toFixed(2)}}`, Object.assign({}, {alignment: { wrapText: false, horizontal: "left", vertical: "middle", }}));
      rowIndexLineImage3+=1;
    })


    worksheet = exportXLSX.addWorksheet(workbook, titre, 'Tableau de suivi');
    resHeader = exportXLSX.addHeader(workbook, worksheet, imglogoVGE, 'Tableau de suivi des réclamations après réception');
    worksheet = resHeader.worksheet;
    rowIndex = resHeader.newRowIndex;


    /* TAB DATAS */
    let columns = [];
    columns.push('N°')
    columns.push('Mode')
    columns.push('Date de Création')
    columns.push('Description')
    columns.push('Localisation')
    columns.push('Lot')
    columns.push('Intervenant')
    columns.push('Observations')
    columns.push('Date Demandée')
    columns.push('Date prévue')
    columns.push('État')
    columns.push('Créé par')


    rowIndex += 2;

    let allDatas = []; // Data par element et colonne
    let allStyles = []; // Styles par data et colonne

    operation.reclamations.forEach(function(reclamation, index) {

      /**
       * Datas
       */
      let datas = [];
      datas.push(reclamation.num)
      datas.push(reclamation.mode)
      datas.push(moment(reclamation.date, 'YYYY-MM-DD').format('DD/MM/YYYY'))
      datas.push(exportXLSX.HTMLtoText(reclamation.description))
      datas.push(exportXLSX.HTMLtoText(reclamation.localisation))
      datas.push(reclamation.lot)

      let interv = '';
      if(reclamation.interventions()){
        reclamation.interventions().forEach(function(interventions){
          interventions.intervenants().forEach(function(intervenant){
          if(intervenant.people()){
            interv += `${intervenant.people().firstname} ${intervenant.people().lastname} ${(intervenant.people().societes() && intervenant.people().societes().length > 0) ? ( intervenant.people().societes()[0].nom ) : ''} <br/>`
          }
          })
        })
      }
      datas.push(exportXLSX.HTMLtoText(interv))

      let comm = '';
      reclamation.commentaires().forEach(function(lastCommentaire){
       comm += lastCommentaire.comment;
      })
      if(reclamation.historiques() && reclamation.historiques().length > 0){
        comm += `<br/> ${reclamation.historiques()[0].observations}`
      }
      datas.push(exportXLSX.HTMLtoText(comm))

      datas.push(moment(reclamation.pourle, 'YYYY-MM-DD').format('DD/MM/YYYY'));

      let datePrevu = '';
      if(reclamation.interventions() && reclamation.interventions()[reclamation.interventions().length -1]){
          datePrevu = moment(reclamation.interventions()[reclamation.interventions().length -1].dateprevue, 'YYYY-MM-DD').format('DD/MM/YYYY')
      }
      datas.push(datePrevu);

      let statut = '';
      if(reclamation.historiques() && reclamation.historiques().length > 0 && reclamation.historiques()[0] && reclamation.historiques()[0].etat()){
          statut += `${reclamation.historiques()[0].etat().libelle} ${(reclamation.historiques()[0].date) ? ('<br/>( ' + moment(reclamation.historiques()[0].date, 'YYYY-MM-DD').format('DD/MM/YYYY') + ')') : ''}`
      }
      datas.push(exportXLSX.HTMLtoText(statut));

      datas.push((reclamation.createur() && reclamation.createur().societes() && reclamation.createur().societes().length > 0) ? reclamation.createur().societes()[0].nom : '');


      allDatas.push(datas)

      /***
       * Styles
       */
      let styles = [];

      styles.push(exportXLSX.getAlignementStyle(false)) // N°
      styles.push(exportXLSX.getAlignementStyle(false)); // mode
      styles.push(exportXLSX.getAlignementStyle(false)); // date crea
      styles.push(exportXLSX.getAlignementStyle(true)); // desc
      styles.push(exportXLSX.getAlignementStyle(true)); // loc
      styles.push(exportXLSX.getAlignementStyle(true)); // lot

      styles.push(exportXLSX.getAlignementStyle(true)); // interv
      styles.push(exportXLSX.getAlignementStyle(true)); // obs


      let styleDateDemand;
      if(reclamation.historiques() && reclamation.historiques().length > 0 && reclamation.historiques()[0] && reclamation.historiques()[0].etat()){
        // background: reclamation.historiques()[0].etat().couleur ;
        // color: black;
        styleDateDemand = Object.assign({}, exportXLSX.getAlignementStyle(true), exportXLSX.getBackgroundColor(reclamation.historiques()[0].etat().couleur))
      } else {
        styleDateDemand = Object.assign({}, exportXLSX.getAlignementStyle(true))
      }
      styles.push(styleDateDemand); /* Date Demandée */
      styles.push(styleDateDemand); /* Date prévue */
      styles.push(styleDateDemand); /* État */
      styles.push(styleDateDemand); /* Créé par */

      allStyles.push(styles)
    })

    const startArrayIndex = rowIndex;
    const result = exportXLSX.writeTable(worksheet, `A${rowIndex}`, columns, allDatas, allStyles)
    worksheet = result.worksheet;
    rowIndex = result.newIndex;

    exportXLSX.addFullBorders(worksheet, `A${startArrayIndex}`, `L${rowIndex}`)
    exportXLSX.addBorders(worksheet, `A${rowIndex}`, `L${rowIndex}`, 'medium')
    exportXLSX.addBorders(worksheet, `A${startArrayIndex}`, `L${rowIndex}`, 'medium')

    rowIndex += 2; // + 1 ligne

    exportXLSX.writeMergedCells(worksheet, `A${rowIndex}`, `L${rowIndex}`,  `Date de réception : ${moment(operation.operation.datereception, 'YYYY-MM-DD').format('DD/MM/YYYY')}`);rowIndex++
    exportXLSX.writeMergedCells(worksheet, `A${rowIndex}`, `L${rowIndex}`,  `Total des fiches crées : ${operation.GPA.nb + operation.GBF.nb}`);rowIndex++
    exportXLSX.writeMergedCells(worksheet, `A${rowIndex}`, `L${rowIndex}`,  `Liste des intervenants :`);rowIndex++

    operation.operation.responsablesRelations().forEach(function(responsable){
      if(responsable.people()){
        const peop = `${responsable.role().nom} : ${(responsable.people().firstname) ? responsable.people().firstname : ''} ${(responsable.people().lastname) ? responsable.people().lastname : ''} ${(responsable.people().societes() && responsable.people().societes().length > 0) ? '(' + responsable.people().societes()[0].nom + ')' : ''}`;
        exportXLSX.writeMergedCells(worksheet, `A${rowIndex}`, `L${rowIndex}`,  `${peop}`);rowIndex++
      }
    })

    rowIndex++
    exportXLSX.writeMergedCells(worksheet, `A${rowIndex}`, `L${rowIndex}`,  `Listing général des états des réclamations`);rowIndex++


    const beginTab = rowIndex;
    worksheet = exportXLSX.writeCell(worksheet, `A${rowIndex}`, 'NB', Object.assign({}, exportXLSX.getAlignementStyle()));
    worksheet = exportXLSX.writeCell(worksheet, `B${rowIndex}`, '%', Object.assign({}, exportXLSX.getAlignementStyle()));
    worksheet = exportXLSX.writeCell(worksheet, `C${rowIndex}`, 'Couleur', Object.assign({}, exportXLSX.getAlignementStyle()));
    worksheet = exportXLSX.writeCell(worksheet, `D${rowIndex}`, 'Rôle', Object.assign({}, exportXLSX.getAlignementStyle()));
    rowIndex++;


    operation.GLOBAL.details.forEach(function(detail){

      worksheet = exportXLSX.writeCell(worksheet, `A${rowIndex}`, `${detail.nbEtat}`, Object.assign({}, exportXLSX.getAlignementStyle()));
      worksheet = exportXLSX.writeCell(worksheet, `B${rowIndex}`, `${(detail.nbEtat/operation.GLOBAL.nb * 100).toFixed(2)}`, Object.assign({}, exportXLSX.getAlignementStyle()));
      worksheet = exportXLSX.writeCell(worksheet, `C${rowIndex}`, ``, Object.assign({}, exportXLSX.getAlignementStyle(), exportXLSX.getBackgroundColor(detail.couleur)));
      worksheet = exportXLSX.writeCell(worksheet, `D${rowIndex}`, `${detail.libelle}`, Object.assign({}, exportXLSX.getAlignementStyle()));
      rowIndex++;

    })

    exportXLSX.addFullBorders(worksheet, `A${beginTab}`, `D${rowIndex-1}`)
    exportXLSX.addBorders(worksheet, `A${beginTab}`, `D${rowIndex-1}`, 'medium')


    const buffer = await workbook.xlsx.writeBuffer();

    // Always SAVE !
    // const save = await helper.saveFile("XSLX", filename, buffer, folderToSave);

    if(sendToRes === true){
      // Send XLSX File !!
      helper.sendXLSX(res, buffer, filename) // date is auto
    }else{
      return (cb) ? cb(null, /*save.path +*/ filename) : /*save.path +*/ filename;
    }


  })

}


/*  EXPORT Synoptique */
module.exports.FicheOperation = async function(operation ,debug = 0, res, landscape= true, sendToRes=true, saveToFile=false, folderToSave=null, typeFile="PDF", cb=null){ // Res = res du call !

  // var root = app.dataSources['containerFile'].settings.root;
  // var pathEML = root + '/' + 'DOnet' + '/' + 'synoptique' + '/' + operation.id + '/';
  // fs.existsSync(pathEML) || fs.mkdirSync(pathEML);

  var filename = 'Export-fiche-operation-GPAnet-' + normalizePath(operation.operation.libelle) + '.pdf'

  /***
   * Traitement des images
   */
  var promises = [];


  var imglogoVGE = null;
  promises.push(
    sharp(rootPath+'/'+'logo.png')
      .resize(null, 40)
      .png()
      .toBuffer().then(function(dataLogo) {
      return imglogoVGE =  `data:image/png;base64,${dataLogo.toString('base64')}`;
    }).catch(function(err) { console.log(err);return err; })
  );

  var chartsOptions = {
    type:'doughnut',
    data: {},
    options : {
      responsive: true,
      maintainAspectRatio: false,
      width: 420,
      height: 250,

      /* demi CIRCLE */
      circumference:  Math.PI,
      rotation: -Math.PI,

      /* CIRCLE */
      circumference: 2 * Math.PI,
      rotation: -Math.PI / 2,

      legend: {
        display: false,
        position: 'bottom',
        fontSize: 10,
        align:'left',
        fullWidth: true,
        reverse: true,
        padding: 5,

      },
      title:{
        display:true,
        text: 'Graphique des sinistres'
      },
      layout: {
        padding: {
          left: 5,
          right: 5,

          top: 5,
          bottom: 5
        }
      },
      plugins: {
        labels: [
          {
            render: 'percentage',
            fontColor:'#ffffff',
            arc: false,
            position: 'border'
          }
        ]
      }
    }
  }

  var Global = { data: { labels: [], datasets: [] }};
  var GPA = { data: { labels: [], datasets: [] }};
  var GBF = { data: { labels: [], datasets: [] }};

  if(operation.GLOBAL){
    Global.data.labels = [];
    Global.data.datasets[0] = {data: [], backgroundColor: [], borderWidth: 1, label: ''};

    operation.GLOBAL.details.forEach((detail) => {
      Global.data.labels.push(detail.libelle)
      Global.data.datasets[0].data.push(detail.nbEtat);
      Global.data.datasets[0].backgroundColor.push(detail.couleur);
    });
  }

  if(operation.GBF){
    GBF.data.labels = [];
    GBF.data.datasets[0] = {data: [], backgroundColor: [], borderWidth: 1, label: ''};

    operation.GBF.details.forEach((detail) => {
      GBF.data.labels.push(detail.libelle)
      GBF.data.datasets[0].data.push(detail.nbEtat);
      GBF.data.datasets[0].backgroundColor.push(detail.couleur);
    });
  }

  if(operation.GPA){
    GPA.data.labels = [];
    GPA.data.datasets[0] = {data: [], backgroundColor: [], borderWidth: 1, label: ''};

    operation.GPA.details.forEach((detail) => {
      GPA.data.labels.push(detail.libelle)
      GPA.data.datasets[0].data.push(detail.nbEtat);
      GPA.data.datasets[0].backgroundColor.push(detail.couleur);
    });
  }

  /* Base Canvas */
  const width = 420; //px
  const height = 350; //px
  const canvasRenderService = new CanvasRenderService(width, height, (ChartJS) => {

  });

  chartsOptions.data = Global.data;
  chartsOptions.options.title.text = 'Réclamations générales';
  var GLOBALCanvas = null;
  promises.push(
    canvasRenderService.renderToBuffer(chartsOptions).then((res) => {
      GLOBALCanvas = `data:image/png;base64,${res.toString('base64')}`;
    }).catch(function(err){ console.log(err)})
  );

  chartsOptions.data = GPA.data;
  chartsOptions.options.title.text = 'Réclamations GPA';
  var GPACanvas = null;
  promises.push(
    canvasRenderService.renderToBuffer(chartsOptions).then((res) => {
      GPACanvas = `data:image/png;base64,${res.toString('base64')}`;
    }).catch(function(err){ console.log(err)})
  );

  chartsOptions.data = GBF.data;
  chartsOptions.options.title.text = 'Réclamations GBF';
  var GBFCanvas = null;
  promises.push(
    canvasRenderService.renderToBuffer(chartsOptions).then((res) => {
      GBFCanvas = `data:image/png;base64,${res.toString('base64')}`;
    }).catch(function(err){ console.log(err)})
  );

  /***
   * END - Traitement des images
   */

  return Promise.all(promises).then(function(){

    let options = {
      operation: operation,
      currentDate: moment().format('DD/MM/YYYY'),
      moment: moment,
      vgeLogo: imglogoVGE,
      GLOBALCanvas: GLOBALCanvas,
      GPACanvas: GPACanvas,
      GBFCanvas: GBFCanvas
    }

    if(debug){
      exportPDF.showHTML('intraOperation/exportOperation.ejs', filename, options, res, function(err, result){ // DEBUG Show HTML
        return cb(err, result);
      });
    }else{
      exportPDF.exportPDF('intraOperation/exportOperation.ejs', filename, options, res, landscape, sendToRes, saveToFile, folderToSave, typeFile, password=null, cb);
    }
  })
}

