const app = require('../../../server/server');
const exportPDF = require('../../exportPDF');
const moment = require('moment-timezone');
moment.locale('fr');
const sharp = require('sharp');
const convertImageHTTPtoPath = require('../../exportPDF').convertImageHTTPtoPath;
const normalizePath = require('../../helper').normalizePath
const rootPath = app.dataSources['containerImage'].settings.root || app.get('containerImage')['root'];
const httpPath = app.get("VGE").PATHS.image.fullpath;

const helper = require('../../helper');

const { CanvasRenderService } = require('chartjs-node-canvas');
const chartJsPluginLabels = require('chartjs-plugin-labels');

const ExcelJS = require("exceljs");
const accounting = require("accounting");
const exportXLSX = require('../../exportXLSX');
const fs = require("fs");

module.exports.ExportXLSXBilanFinancier = async function(sinistre, operation, client=null, debug = 0, res, landscape=true, sendToRes=true, saveToFile=false, folderToSave=null, typeFile="XLSX", cb=null) { // Res = res du call !

  /***
   * Traitement des images
   */
  const promises = [];

  const mainPhoto = (operation.photo) ? operation.photo : null;
  let imgmainPhoto = null;
  if(mainPhoto){
    var realPathImage = convertImageHTTPtoPath(mainPhoto);
    promises.push(
      sharp(realPathImage)
        .resize(1040, 500, {
          kernel: sharp.kernel.lanczos3,
          fit: 'contain',
          background: { r: 255, g: 255, b: 255, alpha: 0 }}
        )
        .png()
        .toBuffer().then(function(data) {
        return imgmainPhoto = `data:image/png;base64,${data.toString('base64')}`;
      }).catch(function(err) { console.log(err);return err; })
    );
  }


  const logoClient = (operation.client().image) ? operation.client().image : null;
  let imglogoClient = null;
  if(logoClient){
    const realPathImage = convertImageHTTPtoPath(logoClient);
    promises.push(
      sharp(realPathImage)
        .resize(null, 40, {
          kernel: sharp.kernel.lanczos3,
          fit: 'contain',
          background: { r: 255, g: 255, b: 255, alpha: 0 }}
        )
        .png()
        .toBuffer().then(function(data) {
        return imglogoClient =  `data:image/png;base64,${data.toString('base64')}`;
      }).catch(function(err) { console.log(err);return err; })
    );
  }

  let imglogoClientFond = null;
  if(client){
    const logoClientFond = (client.image) ? client.image : null;
    if(logoClientFond){
      var realPathImage = convertImageHTTPtoPath(logoClientFond);
      promises.push(
        sharp(realPathImage)
          .resize(null, 40, {
            kernel: sharp.kernel.lanczos3,
            fit: 'contain',
            background: { r: 255, g: 255, b: 255, alpha: 0 }}
          )
          .png()
          .toBuffer().then(function(data) {
          return imglogoClientFond =  `data:image/png;base64,${data.toString('base64')}`;
        }).catch(function(err) { console.log(err);return err; })
      );
    }
  }

  let imglogoVGE = null;
  promises.push(
    sharp(rootPath+'/'+'logo-vge.png')
      // .resize(null, 40)
      .png()
      .toBuffer().then(function(dataLogo) {
      return imglogoVGE =  `data:image/png;base64,${dataLogo.toString('base64')}`;
    }).catch(function(err) { console.log(err);return err; })
  );

  /***
   * END - Traitement des images
   */





  return Promise.all(promises).then(async function(){

    let filename = helper.getExportFilename('bilanFinancier', typeFile, false, null, null, sinistre.id);
    if(!folderToSave){
      folderToSave = operation.libelle
    }
    console.log('Création Export Bilan financier' + typeFile, ((folderToSave) ? folderToSave + '/' : '') + filename)

    // DOC : https://github.com/exceljs/exceljs

    const workbook = new ExcelJS.Workbook();

    workbook.creator = 'Vieira Global Expertise';
    workbook.created = new Date();
    workbook.modified = new Date();

    const titre = `Bilan financier ${operation.libelle} - ${sinistre.referencevge}`


    let devis = sinistre.devis();
    let categoriesDevis = devis.map((d) =>  d.categorie()).filter((d) => d);

    sinistre['totalFactureHT'] = 0;
    sinistre['totalFactureTTC'] = 0;
    sinistre['totalAFacturerHT'] = 0;
    sinistre['totalAFacturerTTC'] = 0;

    if (devis && devis.length > 0) {
      devis.forEach((dev) => {

        sinistre['totalAFacturerHT'] += dev.montantdevis;
        sinistre['totalAFacturerTTC'] += dev.montantTTCdevis;

        dev['totalfactureHT'] = 0;
        dev['totalfactureTTC'] = 0;

        const factures =  dev.factures();

        if(factures && factures.length > 0){
          factures.forEach((facture) => {
            dev['totalfactureHT'] += facture.montantHT;
            dev['totalfactureTTC'] += facture.montantTTC;

            sinistre['totalFactureHT'] += facture.montantHT;
            sinistre['totalFactureTTC'] += facture.montantTTC;

          });
        } else {
          dev['totalfactureHT'] = 0;
          dev['totalfactureTTC'] = 0;
        }
      });
    }


    let worksheet;
    let resHeader;
    let rowIndex;

    worksheet = exportXLSX.addWorksheet(workbook, titre, 'Présentation');
    resHeader = exportXLSX.addHeader(workbook, worksheet, imglogoVGE, titre);

    if(imgmainPhoto){
      const main = workbook.addImage({
        base64: imgmainPhoto,
        extension: 'png',
      });
      worksheet.addImage(main, 'C3:L38');
    }

    exportXLSX.writeMergedCells(worksheet, `A40`, `N40`, `${operation.libelle}`, {font: {size: 16, bold: true, color: {argb: 'FF84C6BC' }}, alignment: { wrapText: false, horizontal: "center", vertical: "middle", }});

    /* TAB STATS */

    worksheet = exportXLSX.addWorksheet(workbook, titre, 'Bilan financier');
    resHeader = exportXLSX.addHeader(workbook, worksheet, imglogoVGE, titre);
    worksheet = resHeader.worksheet;
    rowIndex = resHeader.newRowIndex;

    rowIndex += 2;
    worksheet = exportXLSX.writeFullCells(worksheet, `A${rowIndex}`, `D${rowIndex}`, 'N° de dossier : '+ sinistre.referencevge, {font: {size: 12, bold: true}, alignment: { wrapText: true, horizontal: "left", vertical: "middle" }});
    worksheet = exportXLSX.writeFullCells(worksheet, `E${rowIndex}`, `N${rowIndex}`, exportXLSX.HTMLtoText(sinistre.description), {font: {size: 12, bold: true}, alignment: { wrapText: true, horizontal: "left", vertical: "middle" }});
    // Ajuster la hauteur de la ligne automatiquement
    worksheet.getRow(rowIndex).height = 100;

    /* TAB INDEMNITES */
    rowIndex += 2;

// En-tête pour les indemnités
    worksheet = exportXLSX.writeFullCells(worksheet, `A${rowIndex}`, `N${rowIndex}`, `INDEMNITÉS`, {font: {size: 14, bold: true}, alignment: { wrapText: false, horizontal: "center", vertical: "middle" }});
    rowIndex += 2;

// Colonnes pour les indemnités

    worksheet = exportXLSX.writeFullCells(worksheet, `A${rowIndex}`, `N${rowIndex}`, `Le bilan financier s'établit comme suit :`, {font: {size: 12, bold: true}, alignment: { wrapText: false, horizontal: "left", vertical: "middle" }});
    rowIndex += 1;

    let allIndemniteData = [];
    let allIndemniteStyles = [];
    let totalIndemniteHT = 0;
    let totalIndemniteTTC = 0;


// Parcourir les indemnités

    const indemnites = sinistre.indemnites();

    if (indemnites && indemnites.length > 0) {
      indemnites.forEach(function(indemnite) {
        const montantHT = indemnite.montant || 0;
        const montantTTC = montantHT * (1 + (indemnite.TVA || 19.6) / 100);

        totalIndemniteHT += montantHT;
        totalIndemniteTTC += montantTTC;

        let indemniteData = [
          {text: exportXLSX.HTMLtoText(indemnite.libelle), colspan : 6},
          {text: accounting.formatNumber(montantHT, 2, " ", ","), colspan : 8},
        ];

        let indemniteStyles = Object.assign({},
          exportXLSX.getAlignementStyle(false, 'left', 'middle'),
          exportXLSX.getFont(11, false)
        )

        allIndemniteData.push(indemniteData);
        allIndemniteStyles.push(indemniteStyles);
      });

      // Écrire le tableau des indemnités
      const startIndemniteIndex = rowIndex;
      const resIndemnite = exportXLSX.writeTable(worksheet, `A${rowIndex}`, [], allIndemniteData, allIndemniteStyles);
      worksheet = resIndemnite.worksheet;
      rowIndex = resIndemnite.newIndex;

      // Ajouter le total des indemnités
      worksheet = exportXLSX.write2Columns(
        worksheet,
        `A${rowIndex}`,
        `G${rowIndex}`,
        `N${rowIndex}`,
        'TOTAL INDEMNITÉS',
        `${accounting.formatNumber(totalIndemniteTTC, 2, " ", ",")} €`,
        exportXLSX.getThemeGreenBackWhiteFont(12, true, true, true)
      );

      // Ajouter les bordures
      exportXLSX.addFullBorders(worksheet, `A${startIndemniteIndex}`, `N${rowIndex}`);
    } else {
      worksheet = exportXLSX.writeFullCells(worksheet, `A${rowIndex}`, `N${rowIndex}`, `Aucune indemnité`, {
        font: {size: 11, italic: true},
        alignment: { wrapText: false, horizontal: "center", vertical: "middle" }
      });
    }

    rowIndex += 2;

// En-tête pour les devis
    worksheet = exportXLSX.writeFullCells(worksheet, `A${rowIndex}`, `N${rowIndex}`, `DEVIS`, {font: {size: 14, bold: true}, alignment: { wrapText: true, horizontal: "center", vertical: "middle" }});
    rowIndex += 2;

// Colonnes pour les devis

    // Le bilan financier s'établit comme suit :

    let columnsDevis = [
      {text: 'Entreprises concernées', colspan: 6},
      'Montant HT €',
      'Montant TTC €',
      'Devis',
      'Expert',
      {text: 'Indemnités liées', colspan : 2},
      'Travaux',
      'Règlement',
    ];

    const indemnitesSinistre = sinistre.indemnites();
    const indemniteCount = indemnitesSinistre.reduce((acc, cur) => acc + cur.montant, 0);

    // Fonction pour écrire un groupe de devis
    const writeDevisGroup = (devisList, groupTitle) => {
      if (!devisList || devisList.length === 0) return;

      const startDevisIndex = rowIndex;

      worksheet = exportXLSX.writeFullCells(worksheet, `A${rowIndex}`, `N${rowIndex}`, groupTitle, exportXLSX.getThemeGreenBackWhiteFont(14, true));
      rowIndex += 1;

      let allDevisData = [];
      let allDevisStyles = [];
      let totalHT = 0;
      let totalTTC = 0;
      let totalExpert = 0;

      devisList.forEach(function(devis) {
        let montantHT = 0;
        let montantTTC = 0;

        if (devis.total && devis.pourcentage) {
          montantHT = devis.total * (devis.pourcentage / 100);
          montantTTC = devis.total * (devis.pourcentage / 100) * (1 + (devis.TVA / 100));
        } else {
          montantHT = devis.montantdevis;
          montantTTC = devis.montantTTCdevis;
        }

        if(devis.TTC){
          totalExpert += montantTTC
        } else {
          totalExpert += montantHT
        }

        totalHT += montantHT;
        totalTTC += montantTTC;

        // Construction du texte des indemnités liées
        let indemnitesText = '';
        const indemnitesRelation = devis.indemnitesRelation();
        if (Array.isArray(indemnitesRelation) && indemnitesRelation.length > 0) {
          const indemnitesSinistre = sinistre.indemnites();
          indemnitesText = indemnitesRelation.map(indem => {
            const indemnite = indemnitesSinistre.find(el => el.id === indem.id_indemnite);
            if (indemnite) {
              return `${indemnite.libelle} du ${moment(indemnite.date).format('DD/MM/YYYY')} ` +
                `(${accounting.formatNumber(indemnite.montant, 2, " ", ",")} €, ` +
                `${devis.indemnise ? 'indemnisé' : 'non indemnisé'})`;
            }
            return '';
          })
            .filter(text => text !== '')
            .join('\n');
        }



        let devisData = [
          {text: exportXLSX.HTMLtoText(devis.libelledevis), colspan : 6},
          {text: accounting.formatNumber(montantHT, 2, " ", ","), colspan: 1, style: {
              ...exportXLSX.getAlignementStyle(true, 'center', 'middle'),
              font: {
                ...exportXLSX.getFont(11, !devis.TTC).font
              }
            }},
          {text: accounting.formatNumber(montantTTC, 2, " ", ","), colspan: 1, style: {
              ...exportXLSX.getAlignementStyle(true, 'center', 'middle'),
              font: {
                ...exportXLSX.getFont(11, devis.TTC).font
              }
            }},

          ((devis.filedevis) ? {
            text: (devis.filedevis) ? 'Devis' : '',
            hyperlink: devis.filedevis || null,
            style: {
              font: {
                color: { argb: 'FF0000FF' }, // Couleur bleue pour le lien
                underline: true
              },
              alignment: {
                horizontal: 'center',
                vertical: 'middle'
              }
            }
          } : '' ),

          {text: (devis.TTC) ? accounting.formatNumber(devis.montantTTCdevis, 2, " ", ",") : accounting.formatNumber(devis.montantdevis, 2, " ", ","), colspan: 1, style: {
              ...exportXLSX.getAlignementStyle(true, 'center', 'middle'),
              font: {
                ...exportXLSX.getFont(11, true).font
              }
            } },
          {text: exportXLSX.HTMLtoText(indemnitesText), colspan : 2},
          (devis.commentdevis) ? exportXLSX.HTMLtoText(devis.commentdevis) : '',
          (devis.reglement) ? exportXLSX.HTMLtoText(devis.reglement) : '',
        ];

        let devisStyles = columnsDevis.map(() =>
          Object.assign({},
            exportXLSX.getAlignementStyle(true, 'center', 'middle'),
            exportXLSX.getFont(11, false)
          )
        );

        allDevisData.push(devisData);
        allDevisStyles.push(devisStyles);
      });

      // Écrire le tableau des devis
      // const startDevisIndex = rowIndex;
      const resDevis = exportXLSX.writeTable(worksheet, `A${rowIndex}`, columnsDevis, allDevisData, allDevisStyles);
      worksheet = resDevis.worksheet;
      rowIndex = resDevis.newIndex;

      // Ajouter le total du groupe
      worksheet = exportXLSX.write2Columns(
        worksheet,
        `A${rowIndex}`,
        `G${rowIndex}`,
        `N${rowIndex}`,
        `TOTAL ${groupTitle}`,
        `${accounting.formatNumber(totalExpert, 2, " ", ",")} €`,
        exportXLSX.getThemeGreenBackWhiteFont(12, true, true)
      );

      // Ajouter les bordures
      exportXLSX.addFullBorders(worksheet, `A${startDevisIndex}`, `N${rowIndex}`);
      rowIndex += 2;

      return { totalHT, totalTTC, totalExpert };
    };

// Écrire les devis par catégorie
    let totalGeneralHT = 0;
    let totalGeneralTTC = 0;
    let totalGeneralExpert = 0;

    Object.keys(sinistre.devisByCategorie.categories).forEach(function(categorie) {
      const devisList = sinistre.devisByCategorie.categories[categorie].devis;
      const totals = writeDevisGroup(devisList, categorie);
      totalGeneralHT += totals.totalHT;
      totalGeneralTTC += totals.totalTTC;
      totalGeneralExpert += totals.totalExpert;
    });

// Écrire les devis sans catégorie
    const autresDevisSinistre = sinistre.devis().filter((d) => !d.categorie_id);
    if (autresDevisSinistre.length > 0) {
      const totals = writeDevisGroup(autresDevisSinistre, 'Devis sans catégorie');
      totalGeneralHT += totals.totalHT;
      totalGeneralTTC += totals.totalTTC;
      totalGeneralExpert += totals.totalExpert;
    }

// Ajouter le total général des devis
    rowIndex += 1;

    const colTotalRow = rowIndex;

    worksheet = exportXLSX.write2Columns(
      worksheet,
      `A${rowIndex}`,
      `G${rowIndex}`,
      `N${rowIndex}`,
      'Coût total',
      `${accounting.formatNumber(totalGeneralExpert, 2, " ", ",")} €`,
      exportXLSX.getThemeGreenBackWhiteFont(12, true, true)
    );



    // Ajouter le total général des devis
    rowIndex += 1;
    worksheet = exportXLSX.write2Columns(
      worksheet,
      `A${rowIndex}`,
      `G${rowIndex}`,
      `N${rowIndex}`,
      'Delta Financier',
      `${accounting.formatNumber(indemniteCount - totalGeneralExpert, 2, " ", ",")} €`,

      {
        font: {size: 12, bold: true, color: {argb: (indemniteCount - totalGeneralExpert < 0) ? 'FFFF0000' : 'FF008000' }},
        fill : null,
        alignment: {wrapText: false, horizontal: 'center', vertical: 'middle' }
      }
    );

    exportXLSX.addFullBorders(worksheet, `A${colTotalRow}`, `N${rowIndex}`);


    const buffer = await workbook.xlsx.writeBuffer();

    // Always SAVE !
    const save = await helper.saveFile("XSLX", filename, buffer, folderToSave);

    if(sendToRes === true){
      // Send XLSX File !!
      helper.sendXLSX(res, buffer, filename)
    }else{
      return (cb) ? cb(null, save.path + filename) : save.path + filename;
    }

  })
}

/*  EXPORT PDF Synoptique de l'immeuble */
module.exports.ExportPDFBilanFinancier = async function(sinistre, operation, client=null, debug = 0, res, landscape=true, sendToRes=true, saveToFile=false, folderToSave=null, typeFile="PDF", password=null, cb=null){ // Res = res du call !

  // var root = app.dataSources['containerFile'].settings.root;
  // var pathEML = root + '/' + 'DOnet' + '/' + 'synoptique' + '/' + operation.id + '/';
  // fs.existsSync(pathEML) || fs.mkdirSync(pathEML);

  let filename = helper.getExportFilename('bilanFinancier', typeFile, false, null, null, sinistre.id);
  if(!folderToSave){
    folderToSave = operation.libelle
  }
  console.log('Création Export Bilan financier' + typeFile, ((folderToSave) ? folderToSave + '/' : '') + filename)

  /***
   * Traitement des images
   */
  const promises = [];

  const mainPhoto = (operation.photo) ? operation.photo : null;
  let imgmainPhoto = null;
  if(mainPhoto){
    var realPathImage = convertImageHTTPtoPath(mainPhoto);
    promises.push(
      sharp(realPathImage)
        .resize(1400, 1100, {
          kernel: sharp.kernel.lanczos3,
          fit: 'contain',
          background: { r: 255, g: 255, b: 255, alpha: 0 }}
        )
        .png()
        .toBuffer().then(function(data) {
        return imgmainPhoto = `data:image/png;base64,${data.toString('base64')}`;
      }).catch(function(err) { console.log(err);return err; })
    );
  }


  const logoClient = (operation.client().image) ? operation.client().image : null;
  let imglogoClient = null;
  if(logoClient){
    const realPathImage = convertImageHTTPtoPath(logoClient);
    promises.push(
      sharp(realPathImage)
        .resize(null, 40, {
          kernel: sharp.kernel.lanczos3,
          fit: 'contain',
          background: { r: 255, g: 255, b: 255, alpha: 0 }}
        )
        .png()
        .toBuffer().then(function(data) {
        return imglogoClient =  `data:image/png;base64,${data.toString('base64')}`;
      }).catch(function(err) { console.log(err);return err; })
    );
  }

  let imglogoClientFond = null;
  if(client){
    const logoClientFond = (client.image) ? client.image : null;
    if(logoClientFond){
      var realPathImage = convertImageHTTPtoPath(logoClientFond);
      promises.push(
        sharp(realPathImage)
          .resize(null, 40, {
            kernel: sharp.kernel.lanczos3,
            fit: 'contain',
            background: { r: 255, g: 255, b: 255, alpha: 0 }}
          )
          .png()
          .toBuffer().then(function(data) {
          return imglogoClientFond =  `data:image/png;base64,${data.toString('base64')}`;
        }).catch(function(err) { console.log(err);return err; })
      );
    }
  }

  let imglogoVGE = null;
  promises.push(
    sharp(rootPath+'/'+'logo-vge.png')
      .resize(null, 40)
      .png()
      .toBuffer().then(function(dataLogo) {
      return imglogoVGE =  `data:image/png;base64,${dataLogo.toString('base64')}`;
    }).catch(function(err) { console.log(err);return err; })
  );

  /***
   * END - Traitement des images
   */



  return Promise.all(promises).then(async function(){

    let devis = sinistre.devis();
    let categoriesDevis = devis.map((d) =>  d.categorie()).filter((d) => d);

    sinistre['totalFactureHT'] = 0;
    sinistre['totalFactureTTC'] = 0;
    sinistre['totalAFacturerHT'] = 0;
    sinistre['totalAFacturerTTC'] = 0;

    if (devis && devis.length > 0) {
      devis.forEach((dev) => {

        sinistre['totalAFacturerHT'] += dev.montantdevis;
        sinistre['totalAFacturerTTC'] += dev.montantTTCdevis;

        dev['totalfactureHT'] = 0;
        dev['totalfactureTTC'] = 0;

        const factures =  dev.factures();

        if(factures && factures.length > 0){
          factures.forEach((facture) => {
            dev['totalfactureHT'] += facture.montantHT;
            dev['totalfactureTTC'] += facture.montantTTC;

            sinistre['totalFactureHT'] += facture.montantHT;
            sinistre['totalFactureTTC'] += facture.montantTTC;

          });
        } else {
          dev['totalfactureHT'] = 0;
          dev['totalfactureTTC'] = 0;
        }
      });
    }

    const nbFacturesmax = Math.max(...devis.map((d) => d.factures().length));

    const colspan = 11 + nbFacturesmax;

    let options = {
      operation: operation,
      sinistre: sinistre,
      devis: devis,
      currentDate: moment().format('DD/MM/YYYY'),
      mainPhoto: imgmainPhoto,
      categoriesDevis: categoriesDevis,
      vgeLogo: imglogoVGE,
      clientLogo: imglogoClient,
      moment: moment,
      nbFacturesmax: nbFacturesmax,
      colspan: colspan,
      clientFondLogo: imglogoClientFond,
    };

    options['viewport'] = {
      width: 2000,
      height: 1600,
      deviceScaleFactor: 1,
      isLandscape: 1
    }

    if(debug){
      exportPDF.showHTML('intraDoOperation/bilanFinancier.ejs', filename, options, res, function(err, result){ // DEBUG Show HTML
        return cb(err, result);
      });
    }else{
      return await exportPDF.exportPDF('intraDoOperation/bilanFinancier.ejs', filename, options, res, landscape, sendToRes, saveToFile, folderToSave, typeFile, password, cb);
    }
  })
};

