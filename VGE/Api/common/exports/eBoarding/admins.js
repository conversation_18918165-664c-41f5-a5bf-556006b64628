
/*  EXPORT Synoptique PDF */
const moment = require("moment-timezone");
const exportPDF = require('../../exportPDF');
moment.locale('fr');
const sharp = require('sharp');
const accounting = require('accounting');
const app = require("../../../server/server");
const helper = require("../../helper");
const {convertImageHTTPtoPath} = require("../../exportPDF");
const rootPath = app.dataSources['containerImage'].settings.root || app.get('containerImage')['root'];
const httpPath = app.get("VGE").PATHS.image.fullpath;

module.exports.ExportAdminsPDF = async function(datas, debug = 0, res, landscape=true, sendToRes=true, saveToFile=false, folderToSave=null, typeFile="PDF", password=null, cb=null) { // Res = res du call !

  const filename = 'Export-Admins-' + '-' + moment().format('YYYY-MM-DD') + '.pdf';

  const promises = [];

  let imglogoVGE = null;
  promises.push(
    sharp(rootPath+'/'+'logo.png')
      .resize(null, 40)
      .png()
      .toBuffer().then(function(dataLogo) {
      return imglogoVGE = `data:image/png;base64,${dataLogo.toString('base64')}`;
    }).catch(function(err) { console.log(err);return err; })
  );


  // Photos !!!

  const sz = 25;
  const imgsAdmin = {};
  for(let admin of datas.admins){

    if(admin && admin.image){
      let realPathImage = rootPath+'/'+admin.image;
      promises.push(
        sharp(realPathImage)
          .resize(sz, sz, {
            kernel: sharp.kernel.lanczos3,
            fit: 'contain',
            background: { r: 255, g: 255, b: 255, alpha: 0 }}
          )
          .png()
          .toBuffer().then(function(data) {
          imgsAdmin[admin.id] = `data:image/png;base64,${data.toString('base64')}`;
          return imgsAdmin[admin.id];
        }).catch(function(err) { console.log(err);return err; })
      );
    }
  }

  return Promise.all(promises).then(async function(){

    let options = {
      operationList: datas.operationList,
      nbEncours: datas.nbEncours,
      nbTermines: datas.nbTermines,
      nbCTX: datas.nbCTX,
      nbOP: datas.nbOP,
      roles: datas.roles,
      mainRoleExpert: datas.mainRoleExpert,
      mainRoleGestionnaire: datas.mainRoleGestionnaire,

      ttxExpert: datas.ttxExpert,
      ttxGestionnaire: datas.ttxGestionnaire,

      imgsAdmin: imgsAdmin,
      sz:sz,
      adminOperationRoleExpert: datas.adminOperationRoleExpert,
      adminOperationRoleGestionnaire: datas.adminOperationRoleGestionnaire,

      doStatAdminById: datas.doStatAdminById,
      currentDate: moment().format('DD/MM/YYYY'),
      vgeLogo: imglogoVGE,

      moment: moment,
    };

    if(debug){
      exportPDF.showHTML('intraEbAdmins/byOperations.ejs', filename, options, res, function(err, result){ // DEBUG Show HTML
        return cb(err, result);
      });
    }else{
      return await exportPDF.exportPDF('intraEbAdmins/byOperations.ejs', filename, options, res, landscape, sendToRes, saveToFile, folderToSave, typeFile, password, cb);
    }
  })

}


module.exports.ExportTableAdminsPDF = async function(datas, debug = 0, res, landscape=true, sendToRes=true, saveToFile=false, folderToSave=null, typeFile="PDF", password=null, cb=null) { // Res = res du call !


  let filename;

  if(datas.filter && datas.filter.admin){
    filename = 'Export-Table-Admin-' + helper.normalizePath(datas.filter.admin) + ((datas.role && datas.role.libelle) ? ('-'+helper.normalizePath(datas.role.libelle)) : '') + '.pdf';
  } else {
    filename = 'Export-Table-Admins' + ((datas.role && datas.role.libelle) ? ('-'+helper.normalizePath(datas.role.libelle)) : '')  + '.pdf';
  }

  const promises = [];

  let imglogoVGE = null;
  promises.push(
    sharp(rootPath+'/'+'logo.png')
      .resize(null, 40)
      .png()
      .toBuffer().then(function(dataLogo) {
      return imglogoVGE = `data:image/png;base64,${dataLogo.toString('base64')}`;
    }).catch(function(err) { console.log(err);return err; })
  );

  return Promise.all(promises).then(async function(){

    let options = {
      adminOperationRole: datas.adminOperationRole,
      filter: datas.filter,
      role: datas.role,
      currentDate: moment().format('DD/MM/YYYY'),
      vgeLogo: imglogoVGE,
      moment: moment,
    };

    if(debug){
      exportPDF.showHTML('intraEbAdmins/byAdmin.ejs', filename, options, res, function(err, result){ // DEBUG Show HTML
        return cb(err, result);
      });
    }else{
      return await exportPDF.exportPDF('intraEbAdmins/byAdmin.ejs', filename, options, res, landscape, sendToRes, saveToFile, folderToSave, typeFile, password, cb);
    }
  })


}
