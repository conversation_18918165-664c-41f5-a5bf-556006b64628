var loopback    = require('loopback');
const app = require('../../../../server/server');
const moment = require('moment-timezone');
moment().tz("Europe/Paris").utc(true);
moment.locale('fr');

var People = app.models.People;
var mailer = require('../../../mailer');

module.exports.notifyendContrat = async function(date = null, debug = null){
  return new Promise(async (resolve, reject) => {
    let IntraEbContrat = loopback.getModel('IntraEbuzContrat');

    if(date){
      date = moment(date, 'YYYY-MM-DD').add(2, "months").format('YYYY-MM-DD')
    } else {
      date = moment().add(2, "months").format('YYYY-MM-DD')
    }

    let filter = {
      where: {
        fin: date
      },
      include: ['operation']
    };

    IntraEbContrat.find(filter, function(err, contrats){
      if(err) return reject(err);

      if(contrats.length === 0){
        return resolve();
      }

      People.getAdminsEbusiness(async function(err, admins){
        if(err){return reject(err);}

        let tos = [];
        admins.forEach(function(admin){
          if(admin.email) {
            if (admin.firstname && admin.lastname) {
              tos.push({address: admin.email, name: admin.firstname + ' ' + admin.lastname});
            } else {
              tos.push(admin.email);
            }
          }
        });

        let infos = module.exports.buildMailNotifyEndContrat(contrats);

        let subject = infos.subject;
        let options = {
          body: infos.body,
          signature: null,
          ftps:null
        }

        await mailer.sendMail(tos, [], [], subject, 'contrat/suivi.ejs',options).then(() => {
          resolve()
        }).catch(err => {
          reject(err)
        });
      })
    })

  });
}

module.exports.buildMailNotifyEndContrat = function(contrats){
  let body = "Ce mail a pour but de prévenir de l'arrivée à échéance sur ce(s) contrat(s) d'ici 2 mois <br/><br/>";
  contrats.forEach((contrat) => {
    body += "- Le contrat : '"+contrat.libelle+"' opération " + (contrat.operation().libelle) + " expire le "+moment(contrat.fin, 'YYYY-MM-DD').format('DD/MM/YYYY')+" <br/>";
  })

  subject = 'Arrivée à échéance des ce(s) contrat(s) dans 2 mois';

  return {body: body, subject: subject};
}
