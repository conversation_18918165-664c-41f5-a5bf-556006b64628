var loopback    = require('loopback');
const app = require('../../../../server/server');
const moment = require('moment-timezone');
moment().tz("Europe/Paris").utc(true);
moment.locale('fr');


// Call Facturation 2023 pour maj les sinistres de 2022 !!
// Bloquer le start date de création de facture !!
module.exports.autogenFactures = async function(debug = null){
  return new Promise(async function(resolve, reject){
    let IntraDoSinistre = loopback.getModel('IntraDoSinistre');

    const addAnnuite = 1;

    // query : SELECT intra_do_sinistre.id, intra_do_sinistre.referenceVGE, intra_operation.libelle, intra_operation.id, intra_do_sinistre.nbAnneeFac, intra_do_sinistre.year as year , 2023 - YEAR(intra_do_sinistre.year) as nbannuites FROM `intra_do_sinistre` INNER JOIN intra_operation ON intra_do_sinistre.operation_id = intra_operation.id WHERE intra_do_sinistre.state = 0 AND intra_operation.isdo = 1 AND intra_do_sinistre.ga = 0 AND intra_do_sinistre.isVGE = 1 AND intra_operation.statutdo = 0 AND `nbAnneeFac` > 0 AND year < 2023 AND (2023 - intra_do_sinistre.year) > 0 ORDER BY `intra_do_sinistre`.`year` DESC
    const queryannuite =
      ' UPDATE intra_do_sinistre INNER JOIN intra_operation ON intra_do_sinistre.operation_id = intra_operation.id ' +
      ' SET intra_do_sinistre.nbAnneeFac = 2023 - intra_do_sinistre.year ' +
      ' WHERE intra_operation.isdo = 1' +
      ' AND intra_do_sinistre.isVGE = 1' +
      ' AND intra_do_sinistre.ga = 0' +
      ' AND intra_operation.statutdo = 0' +
      ' AND `nbAnneeFac` > 0' +
      ' AND (state = 0 OR (state = 1 AND (dateCloture IS NOT NULL OR dateCloture < \'2023-01-01\'))) '
      ' AND year < 2023' +
      ' AND (2023 - intra_do_sinistre.year) > 0;'
    //Update annuités anciens dossiers ouverts !

    // Update DEBUG
    // UPDATE intra_do_sinistre INNER JOIN intra_operation ON intra_do_sinistre.operation_id = intra_operation.id
    // SET intra_do_sinistre.nbAnneeFac = 2023 - intra_do_sinistre.year
    // WHERE intra_do_sinistre.state = 0
    // AND intra_operation.isdo = 1
    // AND intra_do_sinistre.isVGE = 1
    // AND intra_do_sinistre.ga = 0
    // AND intra_operation.statutdo = 0
    // AND operation_id = 330 -- ADD ICIC EXTRA POUR IMMEUBLE
    // AND `nbAnneeFac` > 0
    // AND year < 2023
    // AND (2023 - intra_do_sinistre.year) > 0;


    await IntraDoSinistre.getDataSource().connector.execute(queryannuite, null, async (resQueryAnnuite) => {

      // VIEW
      // SELECT intra_do_sinistre.id, intra_do_sinistre.referenceVGE, intra_do_sinistre.date, intra_operation.libelle, intra_operation.id, intra_do_sinistre.nbAnneeFac, intra_do_sinistre.year as year ,(2023 - intra_do_sinistre.year) > 0  , 2023 - intra_do_sinistre.year as nbannuites
      // FROM `intra_do_sinistre`
      // INNER JOIN intra_operation ON intra_do_sinistre.operation_id = intra_operation.id
      // INNER JOIN societes ON intra_operation.idsociete = societes.id
      // WHERE intra_do_sinistre.state = 0
      // AND intra_operation.id = 408 -- Palatis
      // AND intra_operation.isdo = 1
      // AND intra_do_sinistre.ga = 0
      // AND intra_do_sinistre.isVGE = 1
      // AND intra_operation.statutdo = 0
      // AND `nbAnneeFac` > 0
      // AND year < 2023
      // HAVING nbannuites > 0
      // ORDER BY `intra_do_sinistre`.`referenceVGE` ASC

      const query = ' SELECT DISTINCT(intra_do_sinistre.id) ' +
                    ' FROM `intra_do_sinistre` ' +
                    ' INNER JOIN intra_operation ON intra_do_sinistre.operation_id = intra_operation.id ' +
                    ' INNER JOIN societes ON intra_operation.idsociete = societes.id ' +
                    ' WHERE year < 2023 ' +
                    ' AND intra_operation.isdo = 1 ' +

                    // ' AND intra_operation.id = 35 ' + // SIGMA
                    // ' AND intra_operation.id = 339 ' + // 330 -> 1200 bassin à flots ; 339 ->2ancelle
                    // ' AND operation_id = 408 ' + // 408 le palatis
                    // ' AND operation_id = 292 ' + // 292 le tempo

                    ' AND intra_do_sinistre.isVGE = 1 ' +
                    ' AND intra_operation.statutdo = 0 ' +
                    ' AND intra_do_sinistre.ga = 0 ' +
                    ' AND `nbAnneeFac` > 0 ' + // ne pas prendre ceux qui ne se facturent pas
                    ' AND (2023 - intra_do_sinistre.year) > 0 ' + // 2023 current Year !!
                    ' AND (state = 0 OR (state = 1 AND (dateCloture IS NOT NULL OR dateCloture < \'2023-01-01\'))) ' // Cond pour prendre les factures des isnistres cloturés avant l'année YYYY
                    ' ORDER BY `intra_do_sinistre`.`id` ASC'

      // const query = 'SELECT DISTINCT(intra_do_sinistre.id) FROM `intra_do_sinistre` INNER JOIN intra_operation ON intra_do_sinistre.operation_id = intra_operation.id WHERE intra_do_sinistre.state = 0 AND year < 2023 AND intra_operation.isdo = 1 AND intra_do_sinistre.isVGE = 1 AND intra_operation.statutdo = 0 AND `nbAnneeFac` > 0 AND (2022 - YEAR(intra_do_sinistre.date)) > 0 ORDER BY `intra_do_sinistre`.`id` ASC'
      // query view : SELECT DISTINCT(intra_do_sinistre.id),intra_do_sinistre.id, intra_do_sinistre.referenceVGE, intra_operation.libelle, intra_operation.id, intra_do_sinistre.nbAnneeFac, YEAR(intra_do_sinistre.date) as year , 2022 - YEAR(intra_do_sinistre.date) as nbannuites FROM `intra_do_sinistre` INNER JOIN intra_operation ON intra_do_sinistre.operation_id = intra_operation.id WHERE intra_do_sinistre.state = 0 AND year < 2023 AND intra_operation.isdo = 1 AND intra_do_sinistre.isVGE = 1 AND intra_operation.statutdo = 0 AND `nbAnneeFac` > 0 HAVING nbannuites > 0 ORDER BY `intra_do_sinistre`.`id` ASC

      IntraDoSinistre.getDataSource().connector.execute(query, null, async (err, listIds) => {
        if(err){return reject(err)}

        const ids = Object.values(JSON.parse(JSON.stringify(listIds))).map((e) => e.id);
        if(ids && ids.length > 0){
          await IntraDoSinistre.find({where:{id:{'inq':ids}}}).then(async (sinistres) => {
            if(err){return reject(err)}
            // const promises = [];
            for(let sinistre of sinistres){

              // promises.push(
              //   new Promise((resolve1, reject1) => {
                  if(debug){
                    const op = (typeof sinistre.operation === 'function') ? sinistre.operation() : sinistre.operation;
                    console.log(`Ope ${op.libelle} - ${op.id} - sinId: ${sinistre.id} - ${sinistre.referencevge} doit générer une facture, on maj le mdate`)
                    await sinistre.updateAttribute('mdate', moment(sinistre.mdate, 'YYYY-MM-DD HH:mm:ss').add(1, 'minute').format('YYYY-MM-DD HH:mm:ss')).then((sinistreSaved) => { // On incrémente pas !
                      // if(err){reject1(err)}
                      // return resolve1(sinistreSaved);
                    })
                  } else {
                    await sinistre.updateAttribute('mdate', moment(sinistre.mdate, 'YYYY-MM-DD HH:mm:ss').add(1, 'minute').format('YYYY-MM-DD HH:mm:ss')).then((sinistreSaved) => { // On incrémente pas !
                    // await sinistre.updateAttribute('nbAnneeFac', sinistre.nbAnneeFac + addAnnuite).then((sinistreSaved) => { // On incrémente OK !
                      // if(err){reject1(err)}
                      // return resolve1(sinistreSaved);
                    })
                  }
                // })
              // )
            }

            return resolve();

            // Promise.all(promises).then(() => {
            //   return resolve();
            // }).catch((err) => {
            //   return reject(err)
            // })
          });
        } else {
          return resolve();
        }

      });
    })
  });
}

module.exports.generateFactures2023 = async function(debug = null){
  return new Promise(async function(resolve, reject){

    // Pas pour le moment !!
    return resolve();


    let IntraDoSinistre = loopback.getModel('IntraDoSinistre');
    let IntraEbLigneFacture = loopback.getModel('IntraEbuzLigneFacture');

    const sinistres = [
      {"sinistreId": 2516, "nfc": "", "opid":301},
      {"sinistreId": 2858, "nfc": "NFC", "opid":309},
      {"sinistreId": 3158, "nfc": "NFC", "opid":309},
      {"sinistreId": 1014, "nfc": "", "opid":131},
      {"sinistreId": 1022, "nfc": "", "opid":131},
      {"sinistreId": 2700, "nfc": "", "opid":292},
      {"sinistreId": 2753, "nfc": "", "opid":292},
      {"sinistreId": 2752, "nfc": "", "opid":292},
      {"sinistreId": 2556, "nfc": "", "opid":313},
      {"sinistreId": 2667, "nfc": "", "opid":313},
      {"sinistreId": 2618, "nfc": "", "opid":322},
      {"sinistreId": 2648, "nfc": "", "opid":322},
      {"sinistreId": 2748, "nfc": "", "opid":322},
      {"sinistreId": 2779, "nfc": "", "opid":73},
      {"sinistreId": 2098, "nfc": "", "opid":273},
      {"sinistreId": 2420, "nfc": "", "opid":273},
      {"sinistreId": 2074, "nfc": "", "opid":192},
      {"sinistreId": 1983, "nfc": "", "opid":193},
      {"sinistreId": 2486, "nfc": "", "opid":306},
      {"sinistreId": 2538, "nfc": "", "opid":306},
      {"sinistreId": 2083, "nfc": "", "opid":269},
      {"sinistreId": 2484, "nfc": "", "opid":269},
      {"sinistreId": 141, "nfc": "", "opid":16},
      {"sinistreId": 1276, "nfc": "", "opid":16},
      {"sinistreId": 2394, "nfc": "", "opid":16},
      {"sinistreId": 2525, "nfc": "NFC", "opid":16},
      {"sinistreId": 2544, "nfc": "", "opid":16},
      {"sinistreId": 2717, "nfc": "", "opid":16},
      {"sinistreId": 2761, "nfc": "NFC", "opid":16},
      {"sinistreId": 2312, "nfc": "", "opid":278},
      {"sinistreId": 2315, "nfc": "", "opid":278},
      {"sinistreId": 2294, "nfc": "NFC", "opid":278},
      {"sinistreId": 2298, "nfc": "", "opid":278},
      {"sinistreId": 2305, "nfc": "", "opid":278},
      {"sinistreId": 3127, "nfc": "", "opid":405},
      {"sinistreId": 3038, "nfc": "", "opid":359},
      {"sinistreId": 2397, "nfc": "NFC", "opid":294},
      {"sinistreId": 2437, "nfc": "", "opid":294},
      {"sinistreId": 168, "nfc": "", "opid":84},
      {"sinistreId": 1216, "nfc": "", "opid":84},
      {"sinistreId": 2709, "nfc": "", "opid":329},
      {"sinistreId": 2884, "nfc": "", "opid":329},
      {"sinistreId": 3104, "nfc": "", "opid":329},
      {"sinistreId": 2703, "nfc": "", "opid":10},
      {"sinistreId": 2064, "nfc": "", "opid":140},
      {"sinistreId": 57, "nfc": "", "opid":88},
      {"sinistreId": 2760, "nfc": "", "opid":345},
      {"sinistreId": 2762, "nfc": "", "opid":345},
      {"sinistreId": 2888, "nfc": "", "opid":345},
      {"sinistreId": 2721, "nfc": "NFC", "opid":340},
    ]

    const ids = []
    sinistres.forEach((s) => {
      ids.push(s.sinistreId);
    })

    const idsFree = []
    sinistres.forEach((s) => {
      if(s.nfc === 'NFC'){
        idsFree.push(s.sinistreId);
      }
    })

    const addAnnuite = 1;

    // On supprime le statut terminé, et on redéfini le nbAnnée Fac !
    // query : SELECT intra_do_sinistre.id, intra_do_sinistre.referenceVGE, intra_operation.libelle, intra_operation.id, intra_do_sinistre.nbAnneeFac, YEAR(intra_do_sinistre.date) as year , 2022 - YEAR(intra_do_sinistre.date) as nbannuites FROM `intra_do_sinistre` INNER JOIN intra_operation ON intra_do_sinistre.operation_id = intra_operation.id WHERE intra_do_sinistre.state = 0 AND intra_operation.isdo = 1 AND intra_do_sinistre.isVGE = 1 AND intra_operation.statutdo = 0 AND `nbAnneeFac` > 0 AND year < 2022 HAVING nbannuites > 1 ORDER BY `intra_do_sinistre`.`date` DESC
    const queryannuite = 'UPDATE intra_do_sinistre SET intra_do_sinistre.nbAnneeFac = 2022 - YEAR(intra_do_sinistre.date), state = 0 WHERE id IN('+ids.join(',')+');'
    //Update annuités anciens dossiers ouverts !
    IntraDoSinistre.getDataSource().connector.execute(queryannuite, null, async (resQueryAnnuite) => {
      if(ids && ids.length > 0){
        IntraDoSinistre.find({where:{id:{'inq':ids}}}, async function(err, sinistres){
          if(err){return reject(err)}
          for(let sinistre of sinistres){
            await sinistre.updateAttribute('nbAnneeFac', sinistre.nbAnneeFac + addAnnuite).then((sinistreSaved) => {
              // Find ligne.id

            }).catch((err) => {console.error(err)})
          }

          // On maj une date de cloture !
          const queryannuite = 'UPDATE intra_do_sinistre SET state = 1, dateCloture = 2022-06-01 WHERE id IN('+ids.join(',')+');'
          //Update annuités anciens dossiers ouverts !
          await IntraDoSinistre.getDataSource().connector.execute(queryannuite, null, async () => {

            const queryFree = 'SELECT intra_eb_ligne_facture.id as ligneId FROM `intra_eb_ligne_facture` INNER JOIN intra_eb_facture as facture ON facture.id = intra_eb_ligne_facture.facture_id WHERE intra_eb_ligne_facture.sinistre_id IN('+ids.join(',')+');';
            await IntraDoSinistre.getDataSource().connector.execute(queryFree, null, async (err, resQueryFree) => {
              const idsLignesFree = Object.values(JSON.parse(JSON.stringify(resQueryFree)));

              for(let id of idsLignesFree){
                const ligne = await IntraEbLigneFacture.findOne({where: {id: id.ligneId}});
                if(ligne){
                  ligne.reduc = 100;
                  await ligne.updateAttributes(ligne).then((ligneSaved) => {

                  }).catch((err) => {console.error(err)})
                }
              }

              return resolve();

            })
          })
        });
      } else {
        return resolve();
      }
    })
  })
}
