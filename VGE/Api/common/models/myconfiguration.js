
var RemoteRouting = require('loopback-remote-routing');

module.exports = function(Myconfiguration) {

    RemoteRouting(Myconfiguration, {only: [
        '@findall',
        '@edit',
        '@create',
        '@findpublicparameters'
    ]});


    Myconfiguration.remoteMethod(
        'edit', {
            description: 'Edit a parameter',
            accepts: [
                {
                    arg: 'idParameter',
                    type: 'String',
                    required: true,
                    http: {source: 'path'}
                },
                {
                    arg: 'options',
                    type: 'object',
                    required: true,
                    http: {source: 'body'}
                }
            ],
            returns: {
                arg: 'Myparameter',
                type: 'object',
                root: true,
                description: 'The response body contains Parameter updated'
            },
            http: {
                errorStatus: 401,
                path: '/:idParameter/edit',
                verb: 'post'
            }
        }
    );

    Myconfiguration.edit = function(idParameter, options, cb) {

        this.findOne({
            where: {id: idParameter}
        }, function(err, parameter) {

            if(err){
                return cb(err);
            }

            var attributesAuthorized = ['value'];

            var valuesTomodif = {};

            attributesAuthorized.forEach(function (field) {
                /* Si on a la valeur dans le tableau et que la valeur envoyée est différente de celle définie dans account -> */
                //console.log('Champ', field,'Valeur envoyée', options[field], 'valeur parent', account[field], 'diff : ', account[field] != options[field])

                if (options[field] && parameter[field] != options[field]) {
                    valuesTomodif[field] = options[field];
                }
            });

            if(Object.keys(valuesTomodif).length > 0){
                parameter.updateAttributes(valuesTomodif, function(err,param) {
                    if (err) {
                        return cb(err);
                    } else {
                        return cb(null, param);
                    }
                });

            }else{
                return cb(null, parameter);
            }
        });

    }


    /**
     * @api {get} http(s)://[SERVER]:[PORT]/api/v0.1/myconfigurations/findall Get all parameters
     * @apiName Get global parameters
     * @apiGroup Administrator
     *
     * @apiHeader {String} authorization="Bearer pJlRYVS8BX0Q1BlYGPWCD6QDDyUmYbNK" Token Admin User
     *
     * @apiDescription Return list of global parameters
     *
     * @apiExample Postman call exemple :
     * POST /api/v0.1/myconfigurations/findall HTTP/1.1
     * Host: localhost:3000
     * Authorization: Bearer pJlRYVS8BX0Q1BlYGPWCD6QDDyUmYbNK
     * Cache-Control: no-cache
     * Postman-Token: b911b922-3c7d-2d33-8242-168d177ac4dc
     *
     * @apiSuccessExample {json} Success-Response:
     * [
     * {
     *  "key": "FREE_SUBSCRIPTION",
     *  "value": true,
     *  "description": "Est ce qu'il y a un abonnement gratuit pour les nouveaux enfants enregistrés ?",
     *  "id": "5841799359989d255c3ee848"
     * },
     * {
     *  "key": "TIME_FREE_SUBSCRIPTION",
     *  "value": 60,
     *  "description": "Temps en jours d'abonnement gratuit lors de l'inscription de l'enfant",
     *  "id": "5841799359989d255c3ee849"
     * },
     * {
     *  "key": "FREE_SUBSCRIPTION_NAME",
     *  "value": "Abonnement Gratuit d'essai de 60 jours",
     *  "description": "Nom de l'abonnement gratuit lors de l'inscription de l'enfant",
     *  "id": "58480cc5cf9387167c9255f8"
     * }
     * ]
     */
    Myconfiguration.remoteMethod(
        'findall', {
            description: 'Return list of parameters (Admin only)',
            returns: {
                arg: 'Myparameters',
                type: "Array",
                root: true,
                description: 'The response body contains array of parameters'
            },
            http: {
                errorStatus: 422,
                verb: 'get',
                path: '/findall'
            },
            notes: "Return 422 Http code if invalid"
        }
    );

    Myconfiguration.findall = function(cb) {

        this.find({
            order: 'group ASC'
        }, function(err, items){
            if(err){return cb(err)}

            return cb(null, items);

        });
    }



    /**
     * @api {get} http(s)://[SERVER]:[PORT]/api/v0.1/myconfigurations/findpublicparameters Get all public parameters
     * @apiName Get global parameters
     * @apiGroup Parameters
     *
     * @apiHeader {String} authorization="Bearer pJlRYVS8BX0Q1BlYGPWCD6QDDyUmYbNK" Token for Application
     *
     * @apiDescription Return list of public parameters
     *
     * @apiExample Postman call exemple :
     * POST /api/v0.1/myconfigurations/findall HTTP/1.1
     * Host: localhost:3000
     * Authorization: Bearer pJlRYVS8BX0Q1BlYGPWCD6QDDyUmYbNK
     * Cache-Control: no-cache
     * Postman-Token: b911b922-3c7d-2d33-8242-168d177ac4dc
     *
     * @apiSuccessExample {json} Success-Response:
     * [
     * {
     *  "key": "FREE_SUBSCRIPTION",
     *  "value": true,
     *  "description": "Est ce qu'il y a un abonnement gratuit pour les nouveaux enfants enregistrés ?",
     *  "id": "5841799359989d255c3ee848"
     * },
     * {
     *  "key": "FREE_SUBSCRIPTION_NAME",
     *  "value": "Abonnement Gratuit d'essai de 60 jours",
     *  "description": "Nom de l'abonnement gratuit lors de l'inscription de l'enfant",
     *  "id": "58480cc5cf9387167c9255f8"
     * }
     * ]
     */
    Myconfiguration.remoteMethod(
        'findpublicparameters', {
            description: 'Return list of public parameters',
            returns: {
                arg: 'Myparameters',
                type: "Array",
                root: true,
                description: 'The response body contains array of parameters'
            },
            http: {
                errorStatus: 422,
                verb: 'get',
                path: '/findpublicparameters'
            },
            notes: "Return 422 Http code if invalid"
        }
    );

    Myconfiguration.findpublicparameters = function(cb) {

        this.find({
            where: {expose: true},
            order: 'group ASC'
        }, function(err, items){
            if(err){return cb(err)}

            return cb(null, items);

        });
    }

};
