const loopback    = require('loopback');
const app = require('../server/server');
const path = require('path');
const striptags = require('striptags');
const fs = require('fs');
const moment = require('moment-timezone');
const puppeteer = require('puppeteer');
const cors = require('cors');
const accounting = require('accounting');
const helper = require('./helper');


accounting.settings = {
  currency: {
    symbol : "€",   // default currency symbol is '$'
    format: "%v%s", // controls output: %s = symbol, %v = value/number (can be object: see below)
    decimal : ",",  // decimal point separator
    thousand: " ",  // thousands separator
    precision : 2   // decimal places
  },
  number: {
    precision : 0,  // default precision on numbers is 0
    thousand: " ",
    decimal : ","
  }
}

const rootPath = app.dataSources['containerImage'].settings.root || app.get('containerImage')['root'];
const httpPath = app.get("VGE").PATHS.image.fullpath;
const chromiumPath = app.get("puppeteersChromiumBrowser").chromiumPath;

/**
 *
 * @param ejsPath
 * @param filename
 * @param options
 * @param res
 * @param landscape
 * @param sendToRes
 * @param saveToFile
 * @param folderToSave
 * @param cb
 */
module.exports.exportPDF = async function(ejsPath, filename, options, res, landscape=true, sendToRes=true, saveToFile=false, folderToSave=null, typeFile="PDF", password = null, cb){
  return new Promise(async function(resolve, reject){
    let browser;

    const puppeteerConfig = {
      headless: true, // Revenons à l'ancienne syntaxe pour tester
      ignoreDefaultArgs: ["--disable-extensions"],
      args: [
        '--disable-gpu',
        '--disable-dev-shm-usage',
        '--disable-setuid-sandbox',
        '--no-sandbox',
        '--no-zygote'
      ]
    };

    try {
      if(chromiumPath){
        browser = await puppeteer.launch({
          ...puppeteerConfig,
          executablePath: chromiumPath
        });
      } else {
        browser = await puppeteer.launch(puppeteerConfig);
      }

      let page = await browser.newPage();

      let viewport;

      if(options.viewport && options.viewport.width && options.viewport.height){
        viewport = options.viewport;
      } else {
        viewport = {
          width: 794,
          height: 1122,
          deviceScaleFactor: 1
        };
      }

      await page.setViewport(viewport);

      let renderer = loopback.template(path.resolve(__dirname, '../server/views/pdf/'+ejsPath));

      options['PROJECT_NAME'] =  app.get('VGE')['PROJECT_NAME'];
      options['URL_WWW'] = app.get('VGE')['URL_WWW'];
      options['APP_AUTHOR'] = app.get('VGE')['APP_AUTHOR'];
      options['APP_AUTHOR_EMAIL'] = app.get('VGE')['APP_AUTHOR_EMAIL'];
      options['APP_AUTHOR_SITE'] = app.get('VGE')['APP_AUTHOR_SITE'];
      options['moment'] = moment;
      options['accounting'] = accounting;
      options['secure'] = password !== null;

      const htmlBody = renderer(options);
      await page.setContent(htmlBody, {
        waitUntil: ['networkidle0', 'domcontentloaded']
      });

      // Attendre que tout le contenu soit chargé
      await page.evaluate(() => new Promise((resolve) => {
        if (document.readyState === 'complete') {
          resolve();
        } else {
          window.addEventListener('load', resolve);
        }
      }));

      let pdf = await page.pdf({
        format: 'A4',
        landscape: landscape,
        printBackground: true,
        preferCSSPageSize: true,
        margin: { // Ajouter des marges si nécessaire
          top: '20px',
          right: '20px',
          bottom: '20px',
          left: '20px'
        }
      });

      await browser.close();

      // Always SAVE PDF !!
      const save = await helper.saveFile("PDF", filename, pdf, folderToSave, password);

      if(sendToRes === true){
        if(password){
          pdf = fs.readFileSync(save.path + filename);
        }
        helper.sendPDF(res, pdf, filename);
      } else {
        return resolve((cb) ? cb(null, save.path + filename) : save.path + filename);
      }

    } catch (error) {
      if (browser) {
        await browser.close();
      }
      reject(error);
    }
  });
};

module.exports.showHTML = function(ejsPath, filename, options, res){

  const renderer = loopback.template(path.resolve(__dirname, '../server/views/pdf/'+ejsPath));

  options['PROJECT_NAME'] =  app.get('VGE')['PROJECT_NAME'];
  options['URL_WWW'] = app.get('VGE')['URL_WWW'];
  options['APP_AUTHOR'] = app.get('VGE')['APP_AUTHOR'];
  options['APP_AUTHOR_EMAIL'] = app.get('VGE')['APP_AUTHOR_EMAIL'];
  options['APP_AUTHOR_SITE'] = app.get('VGE')['APP_AUTHOR_SITE'];
  options['moment'] = moment;
  options['accounting'] = accounting;

  try{
    const htmlBody = renderer(options);
    res.set({'Content-Type': 'text/html; charset=utf-8'})
    res.send(htmlBody)

  } catch(err){

    console.error(err);

  }
};

module.exports.convertImageHTTPtoPath = function(imgSrc){
  // ex : http://localhost/eGestion/fileEndpoint/images/intra_do_operation/alphapark.jpg to h:....
  const realPathImg = imgSrc.replace(httpPath, rootPath+'/');
  return realPathImg;
};
