// ================================================================================================
// 	File Name: users.scss
// 	Description: Page content different types of users page layouts SCSS.
// 	----------------------------------------------------------------------------------------------
// 	Item Name: Stack - Responsive Admin Theme
// 	Version: 3.2
// 	Author: PIXINVENT
// 	Author URL: http://www.themeforest.net/user/pixinvent
// ================================================================================================


// Core variables and mixins overrides
@import "../bootstrap/functions";
@import "../core/variables/variables";
@import "../bootstrap/variables";

// Overrides user variable
@import "../core/variables/components-variables";
.profile-card-with-stats {
    .btn-float {
        padding: 8px 14px 4px 14px;
    }
}

.profile-card-with-cover {
    .card-profile-image {
        position: absolute;
        top: 130px;
        width: 100%;
        text-align: center;
        img.img-border {
            border: 5px solid #fff;
        }
    }
    .profile-card-with-cover-content {
        padding-top: 4rem;
    }
}

#user-profile {
    .profile-with-cover {
        .profil-cover-details {
            position: absolute;
            margin-top: 210px;
            .profile-image {
                img.img-border {
                    border: 5px solid #fff;
                }
            }
            .card-title {
                color: $white;
                text-shadow: 1px 1px 4px $gray-800;
            }
        }
    }
    .navbar-profile {
        margin-left: 130px;
    }
}

#users-contacts{
    .delete, .favorite {
        i{
            font-size: 1.25rem;
            cursor: pointer;
        }
    }
    .favorite{
        &.active{
            color: $warning;
        }
    }
}
.app-content{
    .sidebar-toggle{
        position: absolute;
        cursor: pointer;
        margin-top: 4px;
    }
    .content-overlay{
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        position: absolute;
        display: block;
        z-index: 2;
        visibility: hidden;
        &.show{
            visibility: visible;
            background-color: rgba(0, 0, 0, 0.6);
        }
    }
}
@media only screen and (max-width: 992px) {
    .app-content{
        .bug-list-search{
            form{
                margin-left: 3rem;
            }
        }
        .sidebar-left{
            transform: translateX(-100%);
            transition: 300ms ease all;
            display: none;
            &.show{
                display: block;
                position: fixed;
                top: 56px;
                width: 300px;
                z-index: 1036;
                left: 0;
                transform: translateX(0%);
                height: calc(100% - 56px);
                transition: 300ms ease all;
                overflow-y: scroll;
                .card{
                    margin-bottom: 0;
                }
            }
        }
    }
}
/* @media only screen and (max-width: 767px) {
    .app-content{
        .sidebar-left{
            &.show{
                z-index: 1032;
            }
        }
    }
} */