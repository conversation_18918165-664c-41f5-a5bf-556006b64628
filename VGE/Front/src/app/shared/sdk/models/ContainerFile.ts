/* tslint:disable */

declare var Object: any;
export interface ContainerFileInterface {
  "id"?: number;
}

export class ContainerFile implements ContainerFileInterface {
  "id": number;
  constructor(data?: ContainerFileInterface) {
    Object.assign(this, data);
  }
  /**
   * The name of the model represented by this $resource,
   * i.e. `ContainerFile`.
   */
  public static getModelName() {
    return "ContainerFile";
  }
  /**
  * @method factory
  * <AUTHOR>
  * @license MIT
  * This method creates an instance of ContainerFile for dynamic purposes.
  **/
  public static factory(data: ContainerFileInterface): ContainerFile{
    return new ContainerFile(data);
  }
  /**
  * @method getModelDefinition
  * <AUTHOR>
  * @license MIT
  * This method returns an object that represents some of the model
  * definitions.
  **/
  public static getModelDefinition() {
    return {
      name: 'ContainerFile',
      plural: 'ContainerFiles',
      path: 'ContainerFiles',
      idName: 'id',
      properties: {
        "id": {
          name: 'id',
          type: 'number'
        },
      },
      relations: {
      }
    }
  }
}
