/* tslint:disable */

declare var Object: any;
export interface IntraDoDevisCategorieInterface {
  "id"?: number;
  "libelle": string;
  "couleur": string;
  "ordre": number;
}

export class IntraDoDevisCategorie implements IntraDoDevisCategorieInterface {
  "id": number;
  "libelle": string;
  "couleur": string;
  "ordre": number;
  constructor(data?: IntraDoDevisCategorieInterface) {
    Object.assign(this, data);
  }
  /**
   * The name of the model represented by this $resource,
   * i.e. `IntraDoDevisCategorie`.
   */
  public static getModelName() {
    return "IntraDoDevisCategorie";
  }
  /**
  * @method factory
  * <AUTHOR>
  * @license MIT
  * This method creates an instance of IntraDoDevisCategorie for dynamic purposes.
  **/
  public static factory(data: IntraDoDevisCategorieInterface): IntraDoDevisCategorie{
    return new IntraDoDevisCategorie(data);
  }
  /**
  * @method getModelDefinition
  * <AUTHOR>
  * @license MIT
  * This method returns an object that represents some of the model
  * definitions.
  **/
  public static getModelDefinition() {
    return {
      name: 'IntraDoDevisCategorie',
      plural: 'IntraDoDevisCategories',
      path: 'IntraDoDevisCategories',
      idName: 'id',
      properties: {
        "id": {
          name: 'id',
          type: 'number'
        },
        "libelle": {
          name: 'libelle',
          type: 'string'
        },
        "couleur": {
          name: 'couleur',
          type: 'string'
        },
        "ordre": {
          name: 'ordre',
          type: 'number'
        },
      },
      relations: {
      }
    }
  }
}
