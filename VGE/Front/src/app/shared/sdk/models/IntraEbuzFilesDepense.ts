/* tslint:disable */
import {
  IntraEbuzDepense,
  People
} from '../index';

declare var Object: any;
export interface IntraEbuzFilesDepenseInterface {
  "id"?: number;
  "file": string;
  "date": Date;
  "depense_id"?: number;
  "people_id"?: number;
  depense?: IntraEbuzDepense;
  createur?: People;
}

export class IntraEbuzFilesDepense implements IntraEbuzFilesDepenseInterface {
  "id": number;
  "file": string;
  "date": Date;
  "depense_id": number;
  "people_id": number;
  depense: IntraEbuzDepense;
  createur: People;
  constructor(data?: IntraEbuzFilesDepenseInterface) {
    Object.assign(this, data);
  }
  /**
   * The name of the model represented by this $resource,
   * i.e. `IntraEbuzFilesDepense`.
   */
  public static getModelName() {
    return "IntraEbuzFilesDepense";
  }
  /**
  * @method factory
  * <AUTHOR>
  * @license MIT
  * This method creates an instance of IntraEbuzFilesDepense for dynamic purposes.
  **/
  public static factory(data: IntraEbuzFilesDepenseInterface): IntraEbuzFilesDepense{
    return new IntraEbuzFilesDepense(data);
  }
  /**
  * @method getModelDefinition
  * <AUTHOR> Ledun
  * @license MIT
  * This method returns an object that represents some of the model
  * definitions.
  **/
  public static getModelDefinition() {
    return {
      name: 'IntraEbuzFilesDepense',
      plural: 'IntraEbuzFilesDepenses',
      path: 'IntraEbuzFilesDepenses',
      idName: 'id',
      properties: {
        "id": {
          name: 'id',
          type: 'number'
        },
        "file": {
          name: 'file',
          type: 'string'
        },
        "date": {
          name: 'date',
          type: 'Date'
        },
        "depense_id": {
          name: 'depense_id',
          type: 'number'
        },
        "people_id": {
          name: 'people_id',
          type: 'number'
        },
      },
      relations: {
        depense: {
          name: 'depense',
          type: 'IntraEbuzDepense',
          model: 'IntraEbuzDepense',
          relationType: 'belongsTo',
                  keyFrom: 'depense_id',
          keyTo: 'id'
        },
        createur: {
          name: 'createur',
          type: 'People',
          model: 'People',
          relationType: 'belongsTo',
                  keyFrom: 'people_id',
          keyTo: 'id'
        },
      }
    }
  }
}
