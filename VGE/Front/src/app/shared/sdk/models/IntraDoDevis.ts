/* tslint:disable */
import {
  IntraDoDevisCategorie,
  IntraDoExpertise,
  IntraDoDevisIndemnite,
  IntraDoDevisFacture,
  IntraDoSinistre,
  Societes
} from '../index';

declare var Object: any;
export interface IntraDoDevisInterface {
  "id"?: number;
  "datedevis"?: Date;
  "ftpdevis"?: string;
  "filedevis"?: string;
  "annexeFile1"?: string;
  "annexeFile2"?: string;
  "libelledevis"?: string;
  "montantdevis"?: number;
  "montantTTCdevis"?: number;
  "TTC"?: number;
  "TVA"?: number;
  "pourcentage"?: number;
  "total"?: number;
  "montantEconomiste"?: number;
  "commentdevis"?: string;
  "reglement"?: string;
  "idsinistre": number;
  "idsociete"?: number;
  "statut": string;
  "indemnise"?: number;
  "categorie_id"?: number;
  categorie?: IntraDoDevisCategorie;
  indemnites?: IntraDoExpertise[];
  indemnitesRelation?: IntraDoDevisIndemnite[];
  factures?: IntraDoDevisFacture[];
  sinistre?: IntraDoSinistre;
  societe?: Societes;
}

export class IntraDoDevis implements IntraDoDevisInterface {
  "id": number;
  "datedevis": Date;
  "ftpdevis": string;
  "filedevis": string;
  "annexeFile1": string;
  "annexeFile2": string;
  "libelledevis": string;
  "montantdevis": number;
  "montantTTCdevis": number;
  "TTC": number;
  "TVA": number;
  "pourcentage": number;
  "total": number;
  "montantEconomiste": number;
  "commentdevis": string;
  "reglement": string;
  "idsinistre": number;
  "idsociete": number;
  "statut": string;
  "indemnise": number;
  "categorie_id": number;
  categorie: IntraDoDevisCategorie;
  indemnites: IntraDoExpertise[];
  indemnitesRelation: IntraDoDevisIndemnite[];
  factures: IntraDoDevisFacture[];
  sinistre: IntraDoSinistre;
  societe: Societes;
  constructor(data?: IntraDoDevisInterface) {
    Object.assign(this, data);
  }
  /**
   * The name of the model represented by this $resource,
   * i.e. `IntraDoDevis`.
   */
  public static getModelName() {
    return "IntraDoDevis";
  }
  /**
  * @method factory
  * <AUTHOR> Casarrubias
  * @license MIT
  * This method creates an instance of IntraDoDevis for dynamic purposes.
  **/
  public static factory(data: IntraDoDevisInterface): IntraDoDevis{
    return new IntraDoDevis(data);
  }
  /**
  * @method getModelDefinition
  * <AUTHOR> Ledun
  * @license MIT
  * This method returns an object that represents some of the model
  * definitions.
  **/
  public static getModelDefinition() {
    return {
      name: 'IntraDoDevis',
      plural: 'IntraDoDevis',
      path: 'IntraDoDevis',
      idName: 'id',
      properties: {
        "id": {
          name: 'id',
          type: 'number'
        },
        "datedevis": {
          name: 'datedevis',
          type: 'Date'
        },
        "ftpdevis": {
          name: 'ftpdevis',
          type: 'string'
        },
        "filedevis": {
          name: 'filedevis',
          type: 'string'
        },
        "annexeFile1": {
          name: 'annexeFile1',
          type: 'string'
        },
        "annexeFile2": {
          name: 'annexeFile2',
          type: 'string'
        },
        "libelledevis": {
          name: 'libelledevis',
          type: 'string'
        },
        "montantdevis": {
          name: 'montantdevis',
          type: 'number'
        },
        "montantTTCdevis": {
          name: 'montantTTCdevis',
          type: 'number'
        },
        "TTC": {
          name: 'TTC',
          type: 'number'
        },
        "TVA": {
          name: 'TVA',
          type: 'number'
        },
        "pourcentage": {
          name: 'pourcentage',
          type: 'number'
        },
        "total": {
          name: 'total',
          type: 'number'
        },
        "montantEconomiste": {
          name: 'montantEconomiste',
          type: 'number'
        },
        "commentdevis": {
          name: 'commentdevis',
          type: 'string'
        },
        "reglement": {
          name: 'reglement',
          type: 'string'
        },
        "idsinistre": {
          name: 'idsinistre',
          type: 'number'
        },
        "idsociete": {
          name: 'idsociete',
          type: 'number'
        },
        "statut": {
          name: 'statut',
          type: 'string'
        },
        "indemnise": {
          name: 'indemnise',
          type: 'number'
        },
        "categorie_id": {
          name: 'categorie_id',
          type: 'number'
        },
      },
      relations: {
        categorie: {
          name: 'categorie',
          type: 'IntraDoDevisCategorie',
          model: 'IntraDoDevisCategorie',
          relationType: 'belongsTo',
                  keyFrom: 'categorie_id',
          keyTo: 'id'
        },
        indemnites: {
          name: 'indemnites',
          type: 'IntraDoExpertise[]',
          model: 'IntraDoExpertise',
          relationType: 'hasMany',
          modelThrough: 'IntraDoDevisIndemnite',
          keyThrough: 'id_devis',
          keyFrom: 'id',
          keyTo: 'id_indemnite'
        },
        indemnitesRelation: {
          name: 'indemnitesRelation',
          type: 'IntraDoDevisIndemnite[]',
          model: 'IntraDoDevisIndemnite',
          relationType: 'hasMany',
                  keyFrom: 'id',
          keyTo: 'id_devis'
        },
        factures: {
          name: 'factures',
          type: 'IntraDoDevisFacture[]',
          model: 'IntraDoDevisFacture',
          relationType: 'hasMany',
                  keyFrom: 'id',
          keyTo: 'devis_id'
        },
        sinistre: {
          name: 'sinistre',
          type: 'IntraDoSinistre',
          model: 'IntraDoSinistre',
          relationType: 'belongsTo',
                  keyFrom: 'idsinistre',
          keyTo: 'id'
        },
        societe: {
          name: 'societe',
          type: 'Societes',
          model: 'Societes',
          relationType: 'belongsTo',
                  keyFrom: 'idsociete',
          keyTo: 'id'
        },
      }
    }
  }
}
