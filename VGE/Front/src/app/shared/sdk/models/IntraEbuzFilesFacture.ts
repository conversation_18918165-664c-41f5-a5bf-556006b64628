/* tslint:disable */
import {
  IntraEbuzFacture,
  People
} from '../index';

declare var Object: any;
export interface IntraEbuzFilesFactureInterface {
  "id"?: number;
  "file": string;
  "date": Date;
  "peopleId"?: number;
  "factureId"?: number;
  "facture_id"?: number;
  "people_id"?: number;
  facture?: IntraEbuzFacture;
  createur?: People;
}

export class IntraEbuzFilesFacture implements IntraEbuzFilesFactureInterface {
  "id": number;
  "file": string;
  "date": Date;
  "peopleId": number;
  "factureId": number;
  "facture_id": number;
  "people_id": number;
  facture: IntraEbuzFacture;
  createur: People;
  constructor(data?: IntraEbuzFilesFactureInterface) {
    Object.assign(this, data);
  }
  /**
   * The name of the model represented by this $resource,
   * i.e. `IntraEbuzFilesFacture`.
   */
  public static getModelName() {
    return "IntraEbuzFilesFacture";
  }
  /**
  * @method factory
  * <AUTHOR>
  * @license MIT
  * This method creates an instance of IntraEbuzFilesFacture for dynamic purposes.
  **/
  public static factory(data: IntraEbuzFilesFactureInterface): IntraEbuzFilesFacture{
    return new IntraEbuzFilesFacture(data);
  }
  /**
  * @method getModelDefinition
  * <AUTHOR> Ledun
  * @license MIT
  * This method returns an object that represents some of the model
  * definitions.
  **/
  public static getModelDefinition() {
    return {
      name: 'IntraEbuzFilesFacture',
      plural: 'IntraEbuzFilesFactures',
      path: 'IntraEbuzFilesFactures',
      idName: 'id',
      properties: {
        "id": {
          name: 'id',
          type: 'number'
        },
        "file": {
          name: 'file',
          type: 'string'
        },
        "date": {
          name: 'date',
          type: 'Date'
        },
        "peopleId": {
          name: 'peopleId',
          type: 'number'
        },
        "factureId": {
          name: 'factureId',
          type: 'number'
        },
        "facture_id": {
          name: 'facture_id',
          type: 'number'
        },
        "people_id": {
          name: 'people_id',
          type: 'number'
        },
      },
      relations: {
        facture: {
          name: 'facture',
          type: 'IntraEbuzFacture',
          model: 'IntraEbuzFacture',
          relationType: 'belongsTo',
                  keyFrom: 'facture_id',
          keyTo: 'id'
        },
        createur: {
          name: 'createur',
          type: 'People',
          model: 'People',
          relationType: 'belongsTo',
                  keyFrom: 'people_id',
          keyTo: 'id'
        },
      }
    }
  }
}
