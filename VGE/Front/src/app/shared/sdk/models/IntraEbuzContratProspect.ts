/* tslint:disable */
import {
  IntraEbuzOperation,
  IntraEbuzContratProspectPrestation,
  IntraEbuzContratProspectCommercialEtat,
  IntraEbuzContratProspectCommercialHistorique,
  IntraEbuzContratProspectCommercialFiles,
  People,
  Societes,
  Universign
} from '../index';

declare var Object: any;
export interface IntraEbuzContratProspectInterface {
  "id"?: number;
  "libelle"?: string;
  "subject"?: string;
  "adresse"?: string;
  "description"?: string;
  "etat"?: string;
  "ICCsigne"?: number;
  "date": Date;
  "debut"?: string;
  "fin"?: string;
  "file"?: string;
  "file2"?: string;
  "file3"?: string;
  "file4"?: string;
  "file5"?: string;
  "cadre"?: number;
  "operation_id"?: number;
  "etat_id"?: number;
  "people_id"?: number;
  "responsablecompta_id"?: number;
  "responsablecompta2_id"?: number;
  "responsablecompta3_id"?: number;
  "responsablecompta4_id"?: number;
  "responsablecompta5_id"?: number;
  "client_id"?: number;
  "universign_id"?: number;
  operation?: IntraEbuzOperation;
  prestations?: IntraEbuzContratProspectPrestation[];
  etatProposition?: IntraEbuzContratProspectCommercialEtat;
  historiques?: IntraEbuzContratProspectCommercialHistorique[];
  files?: IntraEbuzContratProspectCommercialFiles[];
  createur?: People;
  comptable?: People;
  comptable2?: People;
  comptable3?: People;
  comptable4?: People;
  comptable5?: People;
  client?: Societes;
  universign?: Universign;
}

export class IntraEbuzContratProspect implements IntraEbuzContratProspectInterface {
  "id": number;
  "libelle": string;
  "subject": string;
  "adresse": string;
  "description": string;
  "etat": string;
  "ICCsigne": number;
  "date": Date;
  "debut": string;
  "fin": string;
  "file": string;
  "file2": string;
  "file3": string;
  "file4": string;
  "file5": string;
  "cadre": number;
  "operation_id": number;
  "etat_id": number;
  "people_id": number;
  "responsablecompta_id": number;
  "responsablecompta2_id": number;
  "responsablecompta3_id": number;
  "responsablecompta4_id": number;
  "responsablecompta5_id": number;
  "client_id": number;
  "universign_id": number;
  operation: IntraEbuzOperation;
  prestations: IntraEbuzContratProspectPrestation[];
  etatProposition: IntraEbuzContratProspectCommercialEtat;
  historiques: IntraEbuzContratProspectCommercialHistorique[];
  files: IntraEbuzContratProspectCommercialFiles[];
  createur: People;
  comptable: People;
  comptable2: People;
  comptable3: People;
  comptable4: People;
  comptable5: People;
  client: Societes;
  universign: Universign;
  constructor(data?: IntraEbuzContratProspectInterface) {
    Object.assign(this, data);
  }
  /**
   * The name of the model represented by this $resource,
   * i.e. `IntraEbuzContratProspect`.
   */
  public static getModelName() {
    return "IntraEbuzContratProspect";
  }
  /**
  * @method factory
  * <AUTHOR> Casarrubias
  * @license MIT
  * This method creates an instance of IntraEbuzContratProspect for dynamic purposes.
  **/
  public static factory(data: IntraEbuzContratProspectInterface): IntraEbuzContratProspect{
    return new IntraEbuzContratProspect(data);
  }
  /**
  * @method getModelDefinition
  * <AUTHOR> Ledun
  * @license MIT
  * This method returns an object that represents some of the model
  * definitions.
  **/
  public static getModelDefinition() {
    return {
      name: 'IntraEbuzContratProspect',
      plural: 'IntraEbuzContratProspects',
      path: 'IntraEbuzContratProspects',
      idName: 'id',
      properties: {
        "id": {
          name: 'id',
          type: 'number'
        },
        "libelle": {
          name: 'libelle',
          type: 'string'
        },
        "subject": {
          name: 'subject',
          type: 'string'
        },
        "adresse": {
          name: 'adresse',
          type: 'string'
        },
        "description": {
          name: 'description',
          type: 'string'
        },
        "etat": {
          name: 'etat',
          type: 'string'
        },
        "ICCsigne": {
          name: 'ICCsigne',
          type: 'number'
        },
        "date": {
          name: 'date',
          type: 'Date'
        },
        "debut": {
          name: 'debut',
          type: 'string'
        },
        "fin": {
          name: 'fin',
          type: 'string'
        },
        "file": {
          name: 'file',
          type: 'string'
        },
        "file2": {
          name: 'file2',
          type: 'string'
        },
        "file3": {
          name: 'file3',
          type: 'string'
        },
        "file4": {
          name: 'file4',
          type: 'string'
        },
        "file5": {
          name: 'file5',
          type: 'string'
        },
        "cadre": {
          name: 'cadre',
          type: 'number'
        },
        "operation_id": {
          name: 'operation_id',
          type: 'number'
        },
        "etat_id": {
          name: 'etat_id',
          type: 'number'
        },
        "people_id": {
          name: 'people_id',
          type: 'number'
        },
        "responsablecompta_id": {
          name: 'responsablecompta_id',
          type: 'number'
        },
        "responsablecompta2_id": {
          name: 'responsablecompta2_id',
          type: 'number'
        },
        "responsablecompta3_id": {
          name: 'responsablecompta3_id',
          type: 'number'
        },
        "responsablecompta4_id": {
          name: 'responsablecompta4_id',
          type: 'number'
        },
        "responsablecompta5_id": {
          name: 'responsablecompta5_id',
          type: 'number'
        },
        "client_id": {
          name: 'client_id',
          type: 'number'
        },
        "universign_id": {
          name: 'universign_id',
          type: 'number'
        },
      },
      relations: {
        operation: {
          name: 'operation',
          type: 'IntraEbuzOperation',
          model: 'IntraEbuzOperation',
          relationType: 'belongsTo',
                  keyFrom: 'operation_id',
          keyTo: 'id'
        },
        prestations: {
          name: 'prestations',
          type: 'IntraEbuzContratProspectPrestation[]',
          model: 'IntraEbuzContratProspectPrestation',
          relationType: 'hasMany',
                  keyFrom: 'id',
          keyTo: 'contrat_id'
        },
        etatProposition: {
          name: 'etatProposition',
          type: 'IntraEbuzContratProspectCommercialEtat',
          model: 'IntraEbuzContratProspectCommercialEtat',
          relationType: 'belongsTo',
                  keyFrom: 'etat_id',
          keyTo: 'id'
        },
        historiques: {
          name: 'historiques',
          type: 'IntraEbuzContratProspectCommercialHistorique[]',
          model: 'IntraEbuzContratProspectCommercialHistorique',
          relationType: 'hasMany',
                  keyFrom: 'id',
          keyTo: 'contrat_id'
        },
        files: {
          name: 'files',
          type: 'IntraEbuzContratProspectCommercialFiles[]',
          model: 'IntraEbuzContratProspectCommercialFiles',
          relationType: 'hasMany',
                  keyFrom: 'id',
          keyTo: 'contrat_id'
        },
        createur: {
          name: 'createur',
          type: 'People',
          model: 'People',
          relationType: 'belongsTo',
                  keyFrom: 'people_id',
          keyTo: 'id'
        },
        comptable: {
          name: 'comptable',
          type: 'People',
          model: 'People',
          relationType: 'belongsTo',
                  keyFrom: 'responsablecompta_id',
          keyTo: 'id'
        },
        comptable2: {
          name: 'comptable2',
          type: 'People',
          model: 'People',
          relationType: 'belongsTo',
                  keyFrom: 'responsablecompta2_id',
          keyTo: 'id'
        },
        comptable3: {
          name: 'comptable3',
          type: 'People',
          model: 'People',
          relationType: 'belongsTo',
                  keyFrom: 'responsablecompta3_id',
          keyTo: 'id'
        },
        comptable4: {
          name: 'comptable4',
          type: 'People',
          model: 'People',
          relationType: 'belongsTo',
                  keyFrom: 'responsablecompta4_id',
          keyTo: 'id'
        },
        comptable5: {
          name: 'comptable5',
          type: 'People',
          model: 'People',
          relationType: 'belongsTo',
                  keyFrom: 'responsablecompta5_id',
          keyTo: 'id'
        },
        client: {
          name: 'client',
          type: 'Societes',
          model: 'Societes',
          relationType: 'belongsTo',
                  keyFrom: 'client_id',
          keyTo: 'id'
        },
        universign: {
          name: 'universign',
          type: 'Universign',
          model: 'Universign',
          relationType: 'belongsTo',
                  keyFrom: 'universign_id',
          keyTo: 'id'
        },
      }
    }
  }
}
