/* tslint:disable */
import {
  Societes,
  IntraDoOperation,
  People,
  IntraDoFilesVGS,
  IntraDoPhotosVGS
} from '../index';

declare var Object: any;
export interface IntraDoSinistreVGSInterface {
  "id"?: number;
  "num"?: number;
  "date": Date;
  "dateExt"?: Date;
  "year": number;
  "localisation"?: string;
  "lot"?: string;
  "urgent"?: number;
  "observations"?: string;
  "contraintesinter"?: string;
  "contactsite"?: string;
  "tel"?: string;
  "portable"?: string;
  "description"?: string;
  "societeId"?: number;
  "peopleSocieteId"?: number;
  "etatId": number;
  "etatIdExt"?: number;
  "etatIdVGS"?: number;
  "dateetat"?: Date;
  "peopleetatId"?: number;
  "operationId": number;
  "enattente"?: number;
  "peoplecreatorId": number;
  "export"?: number;
  "state": number;
  "stateVGS"?: number;
  "stateExt"?: number;
  "avancement"?: number;
  "avancementVGS"?: number;
  "avancementExt"?: number;
  "referencevge"?: string;
  "referencevgs"?: string;
  "referenceExt"?: string;
  "referenceedo"?: string;
  "referenceassur"?: string;
  "referenceeco"?: string;
  "date60"?: Date;
  "date90"?: Date;
  "date225"?: Date;
  "positionassur"?: number;
  "datepositionassureur"?: Date;
  "commentpositionassur"?: string;
  "ftppositionassur"?: string;
  "mdate"?: Date;
  "foldergeneral"?: string;
  "folderassureur"?: string;
  "folderdevis"?: string;
  "folderrapports"?: string;
  "folderexpert"?: string;
  "folderproprietaire"?: string;
  "foldergestionnaire"?: string;
  "foldermoe"?: string;
  "ftpbilan"?: string;
  "filebilan"?: string;
  "ftpbilanExt"?: string;
  "filebilanExt"?: string;
  "datedesignexpert"?: Date;
  "datecourrierinterruptif"?: Date;
  "enjeu"?: number;
  "txtlibre"?: string;
  "cx"?: number;
  "ga": number;
  "modeftp"?: number;
  "isVGE"?: number;
  "isVGI"?: number;
  "isVGS"?: number;
  "source"?: string;
  "mesuresConservatoires"?: number;
  "nbAnneeFac"?: number;
  "positionNonGarantie"?: number;
  "dateCloture"?: Date;
  "twanted"?: number;
  "operation_id"?: number;
  societe?: Societes;
  operation?: IntraDoOperation;
  createur?: People;
  files?: IntraDoFilesVGS[];
  photos?: IntraDoPhotosVGS[];
}

export class IntraDoSinistreVGS implements IntraDoSinistreVGSInterface {
  "id": number;
  "num": number;
  "date": Date;
  "dateExt": Date;
  "year": number;
  "localisation": string;
  "lot": string;
  "urgent": number;
  "observations": string;
  "contraintesinter": string;
  "contactsite": string;
  "tel": string;
  "portable": string;
  "description": string;
  "societeId": number;
  "peopleSocieteId": number;
  "etatId": number;
  "etatIdExt": number;
  "etatIdVGS": number;
  "dateetat": Date;
  "peopleetatId": number;
  "operationId": number;
  "enattente": number;
  "peoplecreatorId": number;
  "export": number;
  "state": number;
  "stateVGS": number;
  "stateExt": number;
  "avancement": number;
  "avancementVGS": number;
  "avancementExt": number;
  "referencevge": string;
  "referencevgs": string;
  "referenceExt": string;
  "referenceedo": string;
  "referenceassur": string;
  "referenceeco": string;
  "date60": Date;
  "date90": Date;
  "date225": Date;
  "positionassur": number;
  "datepositionassureur": Date;
  "commentpositionassur": string;
  "ftppositionassur": string;
  "mdate": Date;
  "foldergeneral": string;
  "folderassureur": string;
  "folderdevis": string;
  "folderrapports": string;
  "folderexpert": string;
  "folderproprietaire": string;
  "foldergestionnaire": string;
  "foldermoe": string;
  "ftpbilan": string;
  "filebilan": string;
  "ftpbilanExt": string;
  "filebilanExt": string;
  "datedesignexpert": Date;
  "datecourrierinterruptif": Date;
  "enjeu": number;
  "txtlibre": string;
  "cx": number;
  "ga": number;
  "modeftp": number;
  "isVGE": number;
  "isVGI": number;
  "isVGS": number;
  "source": string;
  "mesuresConservatoires": number;
  "nbAnneeFac": number;
  "positionNonGarantie": number;
  "dateCloture": Date;
  "twanted": number;
  "operation_id": number;
  societe: Societes;
  operation: IntraDoOperation;
  createur: People;
  files: IntraDoFilesVGS[];
  photos: IntraDoPhotosVGS[];
  constructor(data?: IntraDoSinistreVGSInterface) {
    Object.assign(this, data);
  }
  /**
   * The name of the model represented by this $resource,
   * i.e. `IntraDoSinistreVGS`.
   */
  public static getModelName() {
    return "IntraDoSinistreVGS";
  }
  /**
  * @method factory
  * <AUTHOR> Casarrubias
  * @license MIT
  * This method creates an instance of IntraDoSinistreVGS for dynamic purposes.
  **/
  public static factory(data: IntraDoSinistreVGSInterface): IntraDoSinistreVGS{
    return new IntraDoSinistreVGS(data);
  }
  /**
  * @method getModelDefinition
  * <AUTHOR> Ledun
  * @license MIT
  * This method returns an object that represents some of the model
  * definitions.
  **/
  public static getModelDefinition() {
    return {
      name: 'IntraDoSinistreVGS',
      plural: 'IntraDoSinistreVGs',
      path: 'IntraDoSinistreVGs',
      idName: 'id',
      properties: {
        "id": {
          name: 'id',
          type: 'number'
        },
        "num": {
          name: 'num',
          type: 'number'
        },
        "date": {
          name: 'date',
          type: 'Date'
        },
        "dateExt": {
          name: 'dateExt',
          type: 'Date'
        },
        "year": {
          name: 'year',
          type: 'number'
        },
        "localisation": {
          name: 'localisation',
          type: 'string'
        },
        "lot": {
          name: 'lot',
          type: 'string'
        },
        "urgent": {
          name: 'urgent',
          type: 'number'
        },
        "observations": {
          name: 'observations',
          type: 'string'
        },
        "contraintesinter": {
          name: 'contraintesinter',
          type: 'string'
        },
        "contactsite": {
          name: 'contactsite',
          type: 'string'
        },
        "tel": {
          name: 'tel',
          type: 'string'
        },
        "portable": {
          name: 'portable',
          type: 'string'
        },
        "description": {
          name: 'description',
          type: 'string'
        },
        "societeId": {
          name: 'societeId',
          type: 'number'
        },
        "peopleSocieteId": {
          name: 'peopleSocieteId',
          type: 'number'
        },
        "etatId": {
          name: 'etatId',
          type: 'number'
        },
        "etatIdExt": {
          name: 'etatIdExt',
          type: 'number'
        },
        "etatIdVGS": {
          name: 'etatIdVGS',
          type: 'number'
        },
        "dateetat": {
          name: 'dateetat',
          type: 'Date'
        },
        "peopleetatId": {
          name: 'peopleetatId',
          type: 'number'
        },
        "operationId": {
          name: 'operationId',
          type: 'number'
        },
        "enattente": {
          name: 'enattente',
          type: 'number'
        },
        "peoplecreatorId": {
          name: 'peoplecreatorId',
          type: 'number'
        },
        "export": {
          name: 'export',
          type: 'number'
        },
        "state": {
          name: 'state',
          type: 'number'
        },
        "stateVGS": {
          name: 'stateVGS',
          type: 'number'
        },
        "stateExt": {
          name: 'stateExt',
          type: 'number'
        },
        "avancement": {
          name: 'avancement',
          type: 'number'
        },
        "avancementVGS": {
          name: 'avancementVGS',
          type: 'number'
        },
        "avancementExt": {
          name: 'avancementExt',
          type: 'number'
        },
        "referencevge": {
          name: 'referencevge',
          type: 'string'
        },
        "referencevgs": {
          name: 'referencevgs',
          type: 'string'
        },
        "referenceExt": {
          name: 'referenceExt',
          type: 'string'
        },
        "referenceedo": {
          name: 'referenceedo',
          type: 'string'
        },
        "referenceassur": {
          name: 'referenceassur',
          type: 'string'
        },
        "referenceeco": {
          name: 'referenceeco',
          type: 'string'
        },
        "date60": {
          name: 'date60',
          type: 'Date'
        },
        "date90": {
          name: 'date90',
          type: 'Date'
        },
        "date225": {
          name: 'date225',
          type: 'Date'
        },
        "positionassur": {
          name: 'positionassur',
          type: 'number'
        },
        "datepositionassureur": {
          name: 'datepositionassureur',
          type: 'Date'
        },
        "commentpositionassur": {
          name: 'commentpositionassur',
          type: 'string'
        },
        "ftppositionassur": {
          name: 'ftppositionassur',
          type: 'string'
        },
        "mdate": {
          name: 'mdate',
          type: 'Date'
        },
        "foldergeneral": {
          name: 'foldergeneral',
          type: 'string'
        },
        "folderassureur": {
          name: 'folderassureur',
          type: 'string'
        },
        "folderdevis": {
          name: 'folderdevis',
          type: 'string'
        },
        "folderrapports": {
          name: 'folderrapports',
          type: 'string'
        },
        "folderexpert": {
          name: 'folderexpert',
          type: 'string'
        },
        "folderproprietaire": {
          name: 'folderproprietaire',
          type: 'string'
        },
        "foldergestionnaire": {
          name: 'foldergestionnaire',
          type: 'string'
        },
        "foldermoe": {
          name: 'foldermoe',
          type: 'string'
        },
        "ftpbilan": {
          name: 'ftpbilan',
          type: 'string'
        },
        "filebilan": {
          name: 'filebilan',
          type: 'string'
        },
        "ftpbilanExt": {
          name: 'ftpbilanExt',
          type: 'string'
        },
        "filebilanExt": {
          name: 'filebilanExt',
          type: 'string'
        },
        "datedesignexpert": {
          name: 'datedesignexpert',
          type: 'Date'
        },
        "datecourrierinterruptif": {
          name: 'datecourrierinterruptif',
          type: 'Date'
        },
        "enjeu": {
          name: 'enjeu',
          type: 'number'
        },
        "txtlibre": {
          name: 'txtlibre',
          type: 'string'
        },
        "cx": {
          name: 'cx',
          type: 'number'
        },
        "ga": {
          name: 'ga',
          type: 'number'
        },
        "modeftp": {
          name: 'modeftp',
          type: 'number'
        },
        "isVGE": {
          name: 'isVGE',
          type: 'number'
        },
        "isVGI": {
          name: 'isVGI',
          type: 'number'
        },
        "isVGS": {
          name: 'isVGS',
          type: 'number'
        },
        "source": {
          name: 'source',
          type: 'string'
        },
        "mesuresConservatoires": {
          name: 'mesuresConservatoires',
          type: 'number'
        },
        "nbAnneeFac": {
          name: 'nbAnneeFac',
          type: 'number'
        },
        "positionNonGarantie": {
          name: 'positionNonGarantie',
          type: 'number'
        },
        "dateCloture": {
          name: 'dateCloture',
          type: 'Date'
        },
        "twanted": {
          name: 'twanted',
          type: 'number'
        },
        "operation_id": {
          name: 'operation_id',
          type: 'number'
        },
      },
      relations: {
        societe: {
          name: 'societe',
          type: 'Societes',
          model: 'Societes',
          relationType: 'belongsTo',
                  keyFrom: 'societeId',
          keyTo: 'id'
        },
        operation: {
          name: 'operation',
          type: 'IntraDoOperation',
          model: 'IntraDoOperation',
          relationType: 'belongsTo',
                  keyFrom: 'operation_id',
          keyTo: 'id'
        },
        createur: {
          name: 'createur',
          type: 'People',
          model: 'People',
          relationType: 'belongsTo',
                  keyFrom: 'peoplecreatorId',
          keyTo: 'id'
        },
        files: {
          name: 'files',
          type: 'IntraDoFilesVGS[]',
          model: 'IntraDoFilesVGS',
          relationType: 'hasMany',
                  keyFrom: 'id',
          keyTo: 'sinistre_id'
        },
        photos: {
          name: 'photos',
          type: 'IntraDoPhotosVGS[]',
          model: 'IntraDoPhotosVGS',
          relationType: 'hasMany',
                  keyFrom: 'id',
          keyTo: 'sinistre_id'
        },
      }
    }
  }
}
