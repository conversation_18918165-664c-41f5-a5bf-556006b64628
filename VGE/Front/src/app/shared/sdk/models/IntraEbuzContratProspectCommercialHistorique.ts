/* tslint:disable */
import {
  IntraEbuzContratProspect,
  People
} from '../index';

declare var Object: any;
export interface IntraEbuzContratProspectCommercialHistoriqueInterface {
  "id"?: number;
  "date"?: Date;
  "observations"?: string;
  "creatorId": number;
  "contratId": number;
  "file"?: string;
  "file2"?: string;
  "file3"?: string;
  "file4"?: string;
  "file5"?: string;
  "comment"?: string;
  "to"?: string;
  "dest"?: string;
  "email"?: string;
  "histoRef"?: number;
  "rappelAuto"?: number;
  "nextRappelAuto"?: Date;
  "auto"?: number;
  "countRappel"?: number;
  "contrat_id"?: number;
  "creator_id"?: number;
  contratProspect?: IntraEbuzContratProspect;
  creator?: People;
}

export class IntraEbuzContratProspectCommercialHistorique implements IntraEbuzContratProspectCommercialHistoriqueInterface {
  "id": number;
  "date": Date;
  "observations": string;
  "creatorId": number;
  "contratId": number;
  "file": string;
  "file2": string;
  "file3": string;
  "file4": string;
  "file5": string;
  "comment": string;
  "to": string;
  "dest": string;
  "email": string;
  "histoRef": number;
  "rappelAuto": number;
  "nextRappelAuto": Date;
  "auto": number;
  "countRappel": number;
  "contrat_id": number;
  "creator_id": number;
  contratProspect: IntraEbuzContratProspect;
  creator: People;
  constructor(data?: IntraEbuzContratProspectCommercialHistoriqueInterface) {
    Object.assign(this, data);
  }
  /**
   * The name of the model represented by this $resource,
   * i.e. `IntraEbuzContratProspectCommercialHistorique`.
   */
  public static getModelName() {
    return "IntraEbuzContratProspectCommercialHistorique";
  }
  /**
  * @method factory
  * <AUTHOR> Casarrubias
  * @license MIT
  * This method creates an instance of IntraEbuzContratProspectCommercialHistorique for dynamic purposes.
  **/
  public static factory(data: IntraEbuzContratProspectCommercialHistoriqueInterface): IntraEbuzContratProspectCommercialHistorique{
    return new IntraEbuzContratProspectCommercialHistorique(data);
  }
  /**
  * @method getModelDefinition
  * <AUTHOR> Ledun
  * @license MIT
  * This method returns an object that represents some of the model
  * definitions.
  **/
  public static getModelDefinition() {
    return {
      name: 'IntraEbuzContratProspectCommercialHistorique',
      plural: 'IntraEbuzContratProspectCommercialHistoriques',
      path: 'IntraEbuzContratProspectCommercialHistoriques',
      idName: 'id',
      properties: {
        "id": {
          name: 'id',
          type: 'number'
        },
        "date": {
          name: 'date',
          type: 'Date'
        },
        "observations": {
          name: 'observations',
          type: 'string'
        },
        "creatorId": {
          name: 'creatorId',
          type: 'number'
        },
        "contratId": {
          name: 'contratId',
          type: 'number'
        },
        "file": {
          name: 'file',
          type: 'string'
        },
        "file2": {
          name: 'file2',
          type: 'string'
        },
        "file3": {
          name: 'file3',
          type: 'string'
        },
        "file4": {
          name: 'file4',
          type: 'string'
        },
        "file5": {
          name: 'file5',
          type: 'string'
        },
        "comment": {
          name: 'comment',
          type: 'string'
        },
        "to": {
          name: 'to',
          type: 'string'
        },
        "dest": {
          name: 'dest',
          type: 'string'
        },
        "email": {
          name: 'email',
          type: 'string'
        },
        "histoRef": {
          name: 'histoRef',
          type: 'number'
        },
        "rappelAuto": {
          name: 'rappelAuto',
          type: 'number'
        },
        "nextRappelAuto": {
          name: 'nextRappelAuto',
          type: 'Date'
        },
        "auto": {
          name: 'auto',
          type: 'number'
        },
        "countRappel": {
          name: 'countRappel',
          type: 'number'
        },
        "contrat_id": {
          name: 'contrat_id',
          type: 'number'
        },
        "creator_id": {
          name: 'creator_id',
          type: 'number'
        },
      },
      relations: {
        contratProspect: {
          name: 'contratProspect',
          type: 'IntraEbuzContratProspect',
          model: 'IntraEbuzContratProspect',
          relationType: 'belongsTo',
                  keyFrom: 'contrat_id',
          keyTo: 'id'
        },
        creator: {
          name: 'creator',
          type: 'People',
          model: 'People',
          relationType: 'belongsTo',
                  keyFrom: 'creator_id',
          keyTo: 'id'
        },
      }
    }
  }
}
