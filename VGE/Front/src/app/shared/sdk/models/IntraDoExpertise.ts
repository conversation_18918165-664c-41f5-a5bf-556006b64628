/* tslint:disable */
import {
  IntraDoDevis,
  IntraDoDevisIndemnite,
  IntraDoSinistre
} from '../index';

declare var Object: any;
export interface IntraDoExpertiseInterface {
  "id"?: number;
  "libelle": string;
  "date": Date;
  "montant"?: number;
  "type": number;
  "indemnise"?: number;
  "ftp"?: string;
  "file"?: string;
  "idsinistre"?: number;
  devis?: IntraDoDevis[];
  devisRelation?: IntraDoDevisIndemnite[];
  sinistre?: IntraDoSinistre;
}

export class IntraDoExpertise implements IntraDoExpertiseInterface {
  "id": number;
  "libelle": string;
  "date": Date;
  "montant": number;
  "type": number;
  "indemnise": number;
  "ftp": string;
  "file": string;
  "idsinistre": number;
  devis: IntraDoDevis[];
  devisRelation: IntraDoDevisIndemnite[];
  sinistre: IntraDoSinistre;
  constructor(data?: IntraDoExpertiseInterface) {
    Object.assign(this, data);
  }
  /**
   * The name of the model represented by this $resource,
   * i.e. `IntraDoExpertise`.
   */
  public static getModelName() {
    return "IntraDoExpertise";
  }
  /**
  * @method factory
  * <AUTHOR> Casarrubias
  * @license MIT
  * This method creates an instance of IntraDoExpertise for dynamic purposes.
  **/
  public static factory(data: IntraDoExpertiseInterface): IntraDoExpertise{
    return new IntraDoExpertise(data);
  }
  /**
  * @method getModelDefinition
  * <AUTHOR> Ledun
  * @license MIT
  * This method returns an object that represents some of the model
  * definitions.
  **/
  public static getModelDefinition() {
    return {
      name: 'IntraDoExpertise',
      plural: 'IntraDoExpertises',
      path: 'IntraDoExpertises',
      idName: 'id',
      properties: {
        "id": {
          name: 'id',
          type: 'number'
        },
        "libelle": {
          name: 'libelle',
          type: 'string'
        },
        "date": {
          name: 'date',
          type: 'Date'
        },
        "montant": {
          name: 'montant',
          type: 'number'
        },
        "type": {
          name: 'type',
          type: 'number'
        },
        "indemnise": {
          name: 'indemnise',
          type: 'number'
        },
        "ftp": {
          name: 'ftp',
          type: 'string'
        },
        "file": {
          name: 'file',
          type: 'string'
        },
        "idsinistre": {
          name: 'idsinistre',
          type: 'number'
        },
      },
      relations: {
        devis: {
          name: 'devis',
          type: 'IntraDoDevis[]',
          model: 'IntraDoDevis',
          relationType: 'hasMany',
          modelThrough: 'IntraDoDevisIndemnite',
          keyThrough: 'id_indemnite',
          keyFrom: 'id',
          keyTo: 'id_devis'
        },
        devisRelation: {
          name: 'devisRelation',
          type: 'IntraDoDevisIndemnite[]',
          model: 'IntraDoDevisIndemnite',
          relationType: 'hasMany',
                  keyFrom: 'id',
          keyTo: 'id_indemnite'
        },
        sinistre: {
          name: 'sinistre',
          type: 'IntraDoSinistre',
          model: 'IntraDoSinistre',
          relationType: 'belongsTo',
                  keyFrom: 'idsinistre',
          keyTo: 'id'
        },
      }
    }
  }
}
