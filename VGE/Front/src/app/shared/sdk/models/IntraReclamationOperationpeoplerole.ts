/* tslint:disable */
import {
  IntraOperation,
  IntraReclamation,
  People,
  Societes
} from '../index';

declare var Object: any;
export interface IntraReclamationOperationpeopleroleInterface {
  "id"?: number;
  "respParentId"?: number;
  "societeParentId"?: number;
  "operation_id"?: number;
  "reclamation_id"?: number;
  "people_id"?: number;
  "societe_id"?: number;
  operation?: IntraOperation;
  reclamation?: IntraReclamation;
  people?: People;
  societe?: Societes;
}

export class IntraReclamationOperationpeoplerole implements IntraReclamationOperationpeopleroleInterface {
  "id": number;
  "respParentId": number;
  "societeParentId": number;
  "operation_id": number;
  "reclamation_id": number;
  "people_id": number;
  "societe_id": number;
  operation: IntraOperation;
  reclamation: IntraReclamation;
  people: People;
  societe: Societes;
  constructor(data?: IntraReclamationOperationpeopleroleInterface) {
    Object.assign(this, data);
  }
  /**
   * The name of the model represented by this $resource,
   * i.e. `IntraReclamationOperationpeoplerole`.
   */
  public static getModelName() {
    return "IntraReclamationOperationpeoplerole";
  }
  /**
  * @method factory
  * <AUTHOR> Casarrubias
  * @license MIT
  * This method creates an instance of IntraReclamationOperationpeoplerole for dynamic purposes.
  **/
  public static factory(data: IntraReclamationOperationpeopleroleInterface): IntraReclamationOperationpeoplerole{
    return new IntraReclamationOperationpeoplerole(data);
  }
  /**
  * @method getModelDefinition
  * <AUTHOR> Ledun
  * @license MIT
  * This method returns an object that represents some of the model
  * definitions.
  **/
  public static getModelDefinition() {
    return {
      name: 'IntraReclamationOperationpeoplerole',
      plural: 'IntraReclamationOperationpeopleroles',
      path: 'IntraReclamationOperationpeopleroles',
      idName: 'id',
      properties: {
        "id": {
          name: 'id',
          type: 'number'
        },
        "respParentId": {
          name: 'respParentId',
          type: 'number'
        },
        "societeParentId": {
          name: 'societeParentId',
          type: 'number'
        },
        "operation_id": {
          name: 'operation_id',
          type: 'number'
        },
        "reclamation_id": {
          name: 'reclamation_id',
          type: 'number'
        },
        "people_id": {
          name: 'people_id',
          type: 'number'
        },
        "societe_id": {
          name: 'societe_id',
          type: 'number'
        },
      },
      relations: {
        operation: {
          name: 'operation',
          type: 'IntraOperation',
          model: 'IntraOperation',
          relationType: 'belongsTo',
                  keyFrom: 'operation_id',
          keyTo: 'id'
        },
        reclamation: {
          name: 'reclamation',
          type: 'IntraReclamation',
          model: 'IntraReclamation',
          relationType: 'belongsTo',
                  keyFrom: 'reclamation_id',
          keyTo: 'id'
        },
        people: {
          name: 'people',
          type: 'People',
          model: 'People',
          relationType: 'belongsTo',
                  keyFrom: 'people_id',
          keyTo: 'id'
        },
        societe: {
          name: 'societe',
          type: 'Societes',
          model: 'Societes',
          relationType: 'belongsTo',
                  keyFrom: 'societe_id',
          keyTo: 'id'
        },
      }
    }
  }
}
