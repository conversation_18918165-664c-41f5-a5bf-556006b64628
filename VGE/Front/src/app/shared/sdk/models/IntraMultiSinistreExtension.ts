/* tslint:disable */
import {
  IntraMultiEtat
} from '../index';

declare var Object: any;
export interface IntraMultiSinistreExtensionInterface {
  "id"?: number;
  "num"?: number;
  "description": string;
  "date": Date;
  "state": number;
  "etatId": number;
  "dateetat"?: Date;
  "peopleetatId"?: number;
  "avancement"?: number;
  "referencevge": string;
  "referenceedo"?: string;
  "referenceassur"?: string;
  "referenceeco"?: string;
  "date60"?: Date;
  "date90"?: Date;
  "date225"?: Date;
  "positionassur"?: number;
  "datepositionassureur"?: Date;
  "commentpositionassur"?: string;
  "ftppositionassur"?: string;
  "datedesignexpert"?: Date;
  "datecourrierinterruptif"?: Date;
  "txtlibre"?: string;
  "idsinistre": number;
  etat?: IntraMultiEtat;
}

export class IntraMultiSinistreExtension implements IntraMultiSinistreExtensionInterface {
  "id": number;
  "num": number;
  "description": string;
  "date": Date;
  "state": number;
  "etatId": number;
  "dateetat": Date;
  "peopleetatId": number;
  "avancement": number;
  "referencevge": string;
  "referenceedo": string;
  "referenceassur": string;
  "referenceeco": string;
  "date60": Date;
  "date90": Date;
  "date225": Date;
  "positionassur": number;
  "datepositionassureur": Date;
  "commentpositionassur": string;
  "ftppositionassur": string;
  "datedesignexpert": Date;
  "datecourrierinterruptif": Date;
  "txtlibre": string;
  "idsinistre": number;
  etat: IntraMultiEtat;
  constructor(data?: IntraMultiSinistreExtensionInterface) {
    Object.assign(this, data);
  }
  /**
   * The name of the model represented by this $resource,
   * i.e. `IntraMultiSinistreExtension`.
   */
  public static getModelName() {
    return "IntraMultiSinistreExtension";
  }
  /**
  * @method factory
  * <AUTHOR> Casarrubias
  * @license MIT
  * This method creates an instance of IntraMultiSinistreExtension for dynamic purposes.
  **/
  public static factory(data: IntraMultiSinistreExtensionInterface): IntraMultiSinistreExtension{
    return new IntraMultiSinistreExtension(data);
  }
  /**
  * @method getModelDefinition
  * <AUTHOR> Ledun
  * @license MIT
  * This method returns an object that represents some of the model
  * definitions.
  **/
  public static getModelDefinition() {
    return {
      name: 'IntraMultiSinistreExtension',
      plural: 'IntraMultiSinistreExtensions',
      path: 'IntraMultiSinistreExtensions',
      idName: 'id',
      properties: {
        "id": {
          name: 'id',
          type: 'number'
        },
        "num": {
          name: 'num',
          type: 'number'
        },
        "description": {
          name: 'description',
          type: 'string'
        },
        "date": {
          name: 'date',
          type: 'Date'
        },
        "state": {
          name: 'state',
          type: 'number'
        },
        "etatId": {
          name: 'etatId',
          type: 'number'
        },
        "dateetat": {
          name: 'dateetat',
          type: 'Date'
        },
        "peopleetatId": {
          name: 'peopleetatId',
          type: 'number'
        },
        "avancement": {
          name: 'avancement',
          type: 'number'
        },
        "referencevge": {
          name: 'referencevge',
          type: 'string'
        },
        "referenceedo": {
          name: 'referenceedo',
          type: 'string'
        },
        "referenceassur": {
          name: 'referenceassur',
          type: 'string'
        },
        "referenceeco": {
          name: 'referenceeco',
          type: 'string'
        },
        "date60": {
          name: 'date60',
          type: 'Date'
        },
        "date90": {
          name: 'date90',
          type: 'Date'
        },
        "date225": {
          name: 'date225',
          type: 'Date'
        },
        "positionassur": {
          name: 'positionassur',
          type: 'number'
        },
        "datepositionassureur": {
          name: 'datepositionassureur',
          type: 'Date'
        },
        "commentpositionassur": {
          name: 'commentpositionassur',
          type: 'string'
        },
        "ftppositionassur": {
          name: 'ftppositionassur',
          type: 'string'
        },
        "datedesignexpert": {
          name: 'datedesignexpert',
          type: 'Date'
        },
        "datecourrierinterruptif": {
          name: 'datecourrierinterruptif',
          type: 'Date'
        },
        "txtlibre": {
          name: 'txtlibre',
          type: 'string'
        },
        "idsinistre": {
          name: 'idsinistre',
          type: 'number'
        },
      },
      relations: {
        etat: {
          name: 'etat',
          type: 'IntraMultiEtat',
          model: 'IntraMultiEtat',
          relationType: 'belongsTo',
                  keyFrom: 'etatId',
          keyTo: 'id'
        },
      }
    }
  }
}
