/* tslint:disable */
import {
  IntraDoSinistre,
  IntraDoSinistreDocumentation
} from '../index';

declare var Object: any;
export interface IntraMultiSinistreDocumentationCategorieInterface {
  "id"?: number;
  "libelle": string;
  "couleur"?: string;
  "parent_id"?: number;
  "sinistre_id": number;
  sinistre?: IntraDoSinistre;
  documents?: IntraDoSinistreDocumentation[];
}

export class IntraMultiSinistreDocumentationCategorie implements IntraMultiSinistreDocumentationCategorieInterface {
  "id": number;
  "libelle": string;
  "couleur": string;
  "parent_id": number;
  "sinistre_id": number;
  sinistre: IntraDoSinistre;
  documents: IntraDoSinistreDocumentation[];
  constructor(data?: IntraMultiSinistreDocumentationCategorieInterface) {
    Object.assign(this, data);
  }
  /**
   * The name of the model represented by this $resource,
   * i.e. `IntraMultiSinistreDocumentationCategorie`.
   */
  public static getModelName() {
    return "IntraMultiSinistreDocumentationCategorie";
  }
  /**
  * @method factory
  * <AUTHOR> Casarrubias
  * @license MIT
  * This method creates an instance of IntraMultiSinistreDocumentationCategorie for dynamic purposes.
  **/
  public static factory(data: IntraMultiSinistreDocumentationCategorieInterface): IntraMultiSinistreDocumentationCategorie{
    return new IntraMultiSinistreDocumentationCategorie(data);
  }
  /**
  * @method getModelDefinition
  * <AUTHOR> Ledun
  * @license MIT
  * This method returns an object that represents some of the model
  * definitions.
  **/
  public static getModelDefinition() {
    return {
      name: 'IntraMultiSinistreDocumentationCategorie',
      plural: 'IntraMultiSinistreDocumentationCategories',
      path: 'IntraMultiSinistreDocumentationCategories',
      idName: 'id',
      properties: {
        "id": {
          name: 'id',
          type: 'number'
        },
        "libelle": {
          name: 'libelle',
          type: 'string'
        },
        "couleur": {
          name: 'couleur',
          type: 'string'
        },
        "parent_id": {
          name: 'parent_id',
          type: 'number',
          default: 0
        },
        "sinistre_id": {
          name: 'sinistre_id',
          type: 'number'
        },
      },
      relations: {
        sinistre: {
          name: 'sinistre',
          type: 'IntraDoSinistre',
          model: 'IntraDoSinistre',
          relationType: 'belongsTo',
                  keyFrom: 'sinistre_id',
          keyTo: 'id'
        },
        documents: {
          name: 'documents',
          type: 'IntraDoSinistreDocumentation[]',
          model: 'IntraDoSinistreDocumentation',
          relationType: 'hasMany',
                  keyFrom: 'id',
          keyTo: 'categorie_id'
        },
      }
    }
  }
}
