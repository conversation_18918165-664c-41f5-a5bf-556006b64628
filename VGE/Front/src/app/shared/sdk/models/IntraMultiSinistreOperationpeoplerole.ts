/* tslint:disable */
import {
  IntraMultiOperation,
  IntraMultiSinistre,
  People,
  Societes
} from '../index';

declare var Object: any;
export interface IntraMultiSinistreOperationpeopleroleInterface {
  "id"?: number;
  "respParentId"?: number;
  "societeParentId"?: number;
  "operation_id"?: number;
  "sinistre_id"?: number;
  "people_id"?: number;
  "societe_id"?: number;
  operation?: IntraMultiOperation;
  sinistre?: IntraMultiSinistre;
  people?: People;
  societe?: Societes;
}

export class IntraMultiSinistreOperationpeoplerole implements IntraMultiSinistreOperationpeopleroleInterface {
  "id": number;
  "respParentId": number;
  "societeParentId": number;
  "operation_id": number;
  "sinistre_id": number;
  "people_id": number;
  "societe_id": number;
  operation: IntraMultiOperation;
  sinistre: IntraMultiSinistre;
  people: People;
  societe: Societes;
  constructor(data?: IntraMultiSinistreOperationpeopleroleInterface) {
    Object.assign(this, data);
  }
  /**
   * The name of the model represented by this $resource,
   * i.e. `IntraMultiSinistreOperationpeoplerole`.
   */
  public static getModelName() {
    return "IntraMultiSinistreOperationpeoplerole";
  }
  /**
  * @method factory
  * <AUTHOR> Casarrubias
  * @license MIT
  * This method creates an instance of IntraMultiSinistreOperationpeoplerole for dynamic purposes.
  **/
  public static factory(data: IntraMultiSinistreOperationpeopleroleInterface): IntraMultiSinistreOperationpeoplerole{
    return new IntraMultiSinistreOperationpeoplerole(data);
  }
  /**
  * @method getModelDefinition
  * <AUTHOR> Ledun
  * @license MIT
  * This method returns an object that represents some of the model
  * definitions.
  **/
  public static getModelDefinition() {
    return {
      name: 'IntraMultiSinistreOperationpeoplerole',
      plural: 'IntraMultiSinistreOperationpeopleroles',
      path: 'IntraMultiSinistreOperationpeopleroles',
      idName: 'id',
      properties: {
        "id": {
          name: 'id',
          type: 'number'
        },
        "respParentId": {
          name: 'respParentId',
          type: 'number'
        },
        "societeParentId": {
          name: 'societeParentId',
          type: 'number'
        },
        "operation_id": {
          name: 'operation_id',
          type: 'number'
        },
        "sinistre_id": {
          name: 'sinistre_id',
          type: 'number'
        },
        "people_id": {
          name: 'people_id',
          type: 'number'
        },
        "societe_id": {
          name: 'societe_id',
          type: 'number'
        },
      },
      relations: {
        operation: {
          name: 'operation',
          type: 'IntraMultiOperation',
          model: 'IntraMultiOperation',
          relationType: 'belongsTo',
                  keyFrom: 'operation_id',
          keyTo: 'id'
        },
        sinistre: {
          name: 'sinistre',
          type: 'IntraMultiSinistre',
          model: 'IntraMultiSinistre',
          relationType: 'belongsTo',
                  keyFrom: 'sinistre_id',
          keyTo: 'id'
        },
        people: {
          name: 'people',
          type: 'People',
          model: 'People',
          relationType: 'belongsTo',
                  keyFrom: 'people_id',
          keyTo: 'id'
        },
        societe: {
          name: 'societe',
          type: 'Societes',
          model: 'Societes',
          relationType: 'belongsTo',
                  keyFrom: 'societe_id',
          keyTo: 'id'
        },
      }
    }
  }
}
