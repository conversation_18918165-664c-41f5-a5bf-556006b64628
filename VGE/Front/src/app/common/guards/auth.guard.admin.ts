import { Injectable } from "@angular/core";
import { CanActivate, Router, ActivatedRouteSnapshot, RouterStateSnapshot } from "@angular/router";
import {LoopBackAuth, People} from "./../../shared/sdk/index";
import {ROLES} from "../../datas/roles.helpers";
import {PeopleWithrightsInterface} from "../interfaces/peopleWithrights.interface";

@Injectable()
export class AuthGuardAdmin implements CanActivate {

  user: PeopleWithrightsInterface;

  constructor(
    public router: Router,
    public auth: LoopBackAuth
  ) {
    this.user = this.auth.getCurrentUserData();
  }

  canActivate(next: ActivatedRouteSnapshot, state: RouterStateSnapshot) {

    if(!this.user){return false}

    if (ROLES.admin.indexOf(this.user.profil.name) === -1 && ROLES.special.indexOf(this.user.profil.name) === -1) {
      this.router.navigate(['/dashboard']);
      return false;
    } else {
      return true;
    }

  }

}
