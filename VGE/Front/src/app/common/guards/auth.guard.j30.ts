import { Injectable } from "@angular/core";
import { CanActivate, Router, ActivatedRouteSnapshot, RouterStateSnapshot } from "@angular/router";
import {LoopBackAuth} from "./../../shared/sdk/index";
import {PeopleWithrightsInterface} from "../interfaces/peopleWithrights.interface";
import {AppGlobalEventManagerService} from "../services/app.globalEventManager.service";
import {ToastrService} from "ngx-toastr";
import {JplusBlocked} from "../../modules/DOnet/models/JPlus";

@Injectable()
export class AuthGuardJ30 implements CanActivate {

  user: PeopleWithrightsInterface;
  isJ30Forced: boolean = false;

  constructor(
    public router: Router,
    public auth: LoopBackAuth,
    public _toastrService: ToastrService,
    private _globalEventManagerService: AppGlobalEventManagerService
  ) {
    this.user = this.auth.getCurrentUserData();

    this._globalEventManagerService.J30Emitter.subscribe((j30:boolean) => {
      this.isJ30Forced = j30;
    })
  }

  canActivate(next: ActivatedRouteSnapshot, state: RouterStateSnapshot) {

    let path: string = state.url;

    if(!this.user){return false}

    if(this.isJ30Forced){
      const regex = /^\/GPAnet\/|\/DOnet\/reporting-mensuel|reporting-mensuel|\/DOnet\/DOstat|DOstat|\/DOnet\/mon-reporting-mensuel|mon-reporting-mensuel|\/DOnet\/mes-dossiers|mes-dossiers|\/DOnet\/DOstat\/mes-dossiers|\/DOnet\/sinistres\/([0-9]+)|\/DOnet\/dostat\/jplus(\/\d+(\/\d+)*)|\/DOnet\/registre-du-journal(\/\d+(\/\d+)*)|\/DOnet\/operations|\/DOnet\/sinistre\/fiche(\/\d+(\/\d+)*)?$/;
      if (regex.test(path)) {
        // Si dans DOStat pour gérer ou fiche Sinistre
        return true;
      } else {
        this._toastrService.info('Vous avez encore des dossiers en J+'+JplusBlocked+' à terminer', 'Redirection')
        this.router.navigate(["/DOnet/mon-reporting-mensuel"]);
        return false;
      }
    } else {
      return true;
    }
  }

}
