import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import {
  AR24,
  IntraDevDevelopmentApi,
} from '../../../../../shared/sdk';
import moment from 'moment';
import {AppGlobalEventManagerService} from "../../../../../common/services/app.globalEventManager.service";
import {AppSessionService} from "../../../../../common/services/app.session.service";
import {PeopleWithrightsInterface} from "../../../../../common/interfaces/peopleWithrights.interface";
import {ToastrService} from "ngx-toastr";
import {FormBuilder} from "@angular/forms";

@Component({
  selector: 'Ar24FormModale',
  templateUrl: './ar24.form.modale.html'
})
export class Ar24FormModale implements OnInit {

  public user: PeopleWithrightsInterface;

  protected moment = moment;

  public folder:string = 'universign';

  @Output() createAR24Object = new EventEmitter<AR24>();
  @Input() restreint: boolean = true; // restreindre les contextes dans le cadre libre

  constructor(
    public activeModal: NgbActiveModal,
    public _sessionService: AppSessionService,
    private _apiService: IntraDevDevelopmentApi,
    private toastr: ToastrService,
    public formBuilder: FormBuilder,
    private appGlobalEventManagerService: AppGlobalEventManagerService
  ) {

    this._sessionService.logguedAccountEmitter.subscribe((people) => {
      this.user = people;
    })
  }


  ngOnInit() {
  }

}
