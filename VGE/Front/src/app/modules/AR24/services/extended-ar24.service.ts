import { Injectable, Inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { AR24Api, LoopBackConfig, SDKModels, LoopBackAuth, Error<PERSON>andler } from '../../../shared/sdk';

/**
 * Service étendu pour AR24 qui ajoute des fonctionnalités supplémentaires
 * sans modifier les fichiers générés
 */
@Injectable({
  providedIn: 'root'
})
export class ExtendedAR24Service extends AR24Api {

  constructor(
    @Inject(HttpClient) protected override http: HttpClient,
    @Inject(SDKModels) protected override models: SDKModels,
    @Inject(LoopBackAuth) protected override auth: LoopBackAuth,
    @Inject(ErrorHandler) protected override errorHandler: ErrorHandler
  ) {
    super(http, models, auth, errorHandler);
  }

  /**
   * Télécharge un fichier depuis AR24 en tant que Blob
   * @param id ID de l'objet AR24
   * @param fileUrl URL du fichier à télécharger
   * @returns Observable avec les données binaires du fichier
   */
  public getFileAsBlob(id: number, fileUrl: string): Observable<Blob> {
    const url = [
      LoopBackConfig.getPath(),
      LoopBackConfig.getApiVersion(),
      this.model.getModelDefinition().path,
      'getFile'
    ].join('/');
    
    // Utiliser HttpClient directement avec responseType: 'blob'
    return this.http.get(url+'?ar24Id='+id+'&fileUrl='+ encodeURI(fileUrl), {
      responseType: 'blob'
    });
  }
}