import {Component, OnInit} from "@angular/core";
import {ActivatedRoute, Router} from "@angular/router";
import {AppSessionService} from "../../../../common/services/app.session.service";
import {ToastrService} from "ngx-toastr";
import {LoopBackAuth} from "../../../../shared/sdk/services/core";
import {Observable} from "rxjs";
import {IntraOpcomment, IntraOperation, People} from "../../../../shared/sdk/models";
import {IntraOperationApi} from "../../../../shared/sdk/services/custom";
import {hiddenColumns} from "../../../../datas/pagination.helpers";
import {TableColumnInterface} from "../../../shared/interfaces/table.column.interface";
import {share, take} from "rxjs/operators";
import {AppGlobalEventManagerService} from "../../../../common/services/app.globalEventManager.service";
import {ROLES} from "../../../../datas/roles.helpers";
import {NgxSpinnerService} from "ngx-spinner";
import {PeopleWithrightsInterface} from "../../../../common/interfaces/peopleWithrights.interface";

@Component({
  selector: 'RegistreJournalGPAPage',
  templateUrl: './registreJournal.page.html'
})
export class RegistreJournalGPAPage implements OnInit {

  public operation: IntraOperation;
  public commentaires$: Observable<IntraOpcomment[]>;
  public columns: Array<TableColumnInterface>;
  public hiddenColumns;

  public ido;
  public mode;

  public user: PeopleWithrightsInterface;
  public ROLES = ROLES;

  constructor(
    public router: Router,
    public route: ActivatedRoute,
    public _sessionService: AppSessionService,
    public _toastr: ToastrService,
    public authService: LoopBackAuth,
    public intraOperationApiService: IntraOperationApi,
    public appGlobalEventManagerService: AppGlobalEventManagerService,
    public spinner: NgxSpinnerService
  ) {

    this.appGlobalEventManagerService.openSpinner();

    this._sessionService.logguedAccountEmitter.subscribe((people) => {
      this.user = people;
    })


    this.columns =
      [
        // Réclamations GPA	Réclamations GBF	Libellé	Adresse	Code postal	Ville	Date réception	Date Création	Statut GPAnet	Registre GPAnet	Docs Généraux	Actions

        {key: 'libelle', object:'operation', name:"Libellé", type: 'input', order:true, filter:true},
        {key: 'adresse', object:'operation', name:"Adresse", type: 'input', order:false, filter:false},
        {key: 'code', object:'operation', name:"Code postal", type: 'input', order:false, filter:true},
        {key: 'ville', object:'operation', name:"Ville", type: 'input', order:true, filter:true},
        {key: 'datereception', object:'operation', name:"Date réception", type: 'date', order:true, filter:false},
        {key: 'datecreation', object:'operation', name:"Date de création", type: 'date', order:true, filter:false},
        {key: 'termine', object:'operation', name:"Statut GPAnet", type: 'boolean', datas:{0:'En cours', 1:'Terminé'}, order:true, filter:false},


      ];
    this.hiddenColumns = hiddenColumns.concat([])

  }

  /** Call to delete Object **/
  delete(object){
    if(object && object.id)
    this.intraOperationApiService.destroyByIdCommentaires(this.ido, object.id).subscribe(
      (res) => {
        this._toastr.success('Suppression du commentaire', 'Le commentaire a été supprimé avec succès');
        this.load(); // Refresh list
      },
      (err) => {this._toastr.success('Suppression du commentaire', err.message);}
    );
  }

  load(){
    this.commentaires$ = Object.assign(new Observable<any[]>(), this.intraOperationApiService.getCommentaires(this.ido, {order:'date DESC'}).pipe(share()));
    this.commentaires$.subscribe(() => {
      this.appGlobalEventManagerService.closeSpinner();
    })
  }

  ngOnInit(): void {

    this.route.paramMap.subscribe(params => {
      this.ido = params.get("ido");
      this.mode = params.get("mode");
      this.intraOperationApiService.findById(this.ido).pipe(share(), take(1)).subscribe((operation:IntraOperation) => {
        this.operation = operation
        this.appGlobalEventManagerService.updateTitle('Boîte de dialogue générale, registre du journal opération '+this.operation.libelle);
        this.appGlobalEventManagerService.updateBreadcrumb([{title:'Home', url:'/'}, {title:'Liste des opérations GPA & GBF', url:'/GPAnet/operations'}, {title:'Registre du journal GPAnet opération '+this.operation.libelle, url:'/GPAnet/registre-du-journal/'+this.ido }])
      });
      this.load();
    })

  }


}
