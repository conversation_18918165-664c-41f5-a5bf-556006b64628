import {Component, OnInit} from "@angular/core";
import {ActivatedRoute, Router} from "@angular/router";
import {AppSessionService} from "../../../../common/services/app.session.service";
import {ToastrService} from "ngx-toastr";
import {LoopBackAuth} from "../../../../shared/sdk/services/core";
import {IntraOperationApi} from "../../../../shared/sdk/services/custom";
import {Observable} from "rxjs";
import {TableColumnInterface} from "../../../shared/interfaces/table.column.interface";
import {AppGlobalEventManagerService} from "../../../../common/services/app.globalEventManager.service";
import {share} from "rxjs/operators";
import {ListPageInterface} from "../../../shared/interfaces/list.page.interface";
import {IntraOperation, LoopBackFilter} from "../../../../shared/sdk/models";
import {filterBuilder} from "../../../shared/models/filterBuilder";


@Component({
  selector: 'docsGenerauxDoLists',
  templateUrl: './docsgeneraux.list.page.html'
})
export class DocsgenerauxListPage implements OnInit, ListPageInterface {

  public docs$: Observable<any[]> = new Observable<any[]>();
  public columns: Array<TableColumnInterface>;
  public hiddenColumns;
  public filtre: LoopBackFilter = {};

  public ido: string = null;
  public operation: IntraOperation = null;

  constructor(
    public router: Router,
    public _sessionService: AppSessionService,
    public _toastr: ToastrService,
    public route: ActivatedRoute,
    public authService: LoopBackAuth,
    public ApiService: IntraOperationApi,
    public toastr: ToastrService,
    public appGlobalEventManagerService: AppGlobalEventManagerService
  ) {


    this.columns =
      [
        {key: 'nom', name:"Nom", type: 'input', order:true, filter:true, width:{'width.%': 30}},
        {key: 'file', name:"Image", type: 'image', order:false, filter:false},
      ];

  }

  load() {
    this.docs$ =  Object.assign(new Observable<any[]>(),this.ApiService.getDocsGeneraux(this.ido ,this.filtre).pipe(share()));
  }

  delete(object){
    this.ApiService.destroyByIdDocsGeneraux(this.ido, object.id).subscribe((res) => {
      this.toastr.success('Supprimé avec succès', 'Suppression du document général');
      this.load();
    }, error => { this.toastr.error(error, 'Suppression du document général') });
  }

  filter(value: string, $event) {
    if(!this.filtre || !this.filtre['where']){this.filtre['where'] = {}}
    if(value && $event){
      this.filtre['where'] = filterBuilder.buildTerm(value, $event.target.value) ;
    }
    this.load();
  }

  ngOnInit(): void {

    this.route.paramMap.subscribe(params => {
      this.ido = params.get("ido");
      this.ApiService.findById(this.ido).subscribe((ope:IntraOperation) => {
        this.operation = ope;

        this.appGlobalEventManagerService.updateTitle('Liste des documents généraux opération '+ this.operation.libelle);
        this.appGlobalEventManagerService.updateBreadcrumb([{title:'Home', url:'/'}, {title:'Liste opérations', url:'/DOnet/operations'}, {title:'Liste des documents généraux opération '+ this.operation.libelle, url:'/DOnet/operation/documentsGeneraux/'+this.operation.id}])

      })
      this.load();
    })

  }

}

