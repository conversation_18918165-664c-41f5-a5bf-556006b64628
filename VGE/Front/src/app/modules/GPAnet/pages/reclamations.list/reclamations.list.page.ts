import {Component, OnInit, ViewChild} from "@angular/core";
import {ActivatedRoute, Router} from "@angular/router";
import {AppSessionService} from "../../../../common/services/app.session.service";
import {ToastrService} from "ngx-toastr";
import {LoopBackAuth} from "../../../../shared/sdk/services/core";
import {forkJoin, Observable, of} from "rxjs";
import {
  IntraEtat,
  IntraInterventionReclamationPeople,
  IntraOperation,
  IntraReclamation,
  LoopBackFilter,
  People, Societes
} from "../../../../shared/sdk/models";
import {IntraIntervention<PERSON>pi, IntraOperationApi, IntraReclamationApi} from "../../../../shared/sdk/services/custom";
import {hiddenColumns} from "../../../../datas/pagination.helpers";
import {TableColumnInterface} from "../../../shared/interfaces/table.column.interface";
import {ROLES} from "../../../../datas/roles.helpers";
import {AppGlobalEventManagerService} from "../../../../common/services/app.globalEventManager.service";
import {filterBuilder} from "../../../shared/models/filterBuilder";
import {GPAGBFStats, returnReclamationsGPA} from "../../interfaces/reclamationsGPA.interface";
import {SelftablepaginateDirective} from "../../../../directives/tables/selftablepaginate/selftablepaginate.directive";
import {NgxSpinnerService} from "ngx-spinner";
import {Form, FormBuilder, FormGroup, Validators} from "@angular/forms";
import {NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {AppCacheService} from "../../../../common/services/app.cache.service";
import {PeopleWithrightsInterface} from "../../../../common/interfaces/peopleWithrights.interface";
import moment from "moment";
import {ListPage} from "../../../../directives/controller/listPage";

@Component({
  selector: 'reclamationsList',
  templateUrl: './reclamations.list.page.html'
})
export class ReclamationsListPage extends ListPage implements OnInit {

  public call$ : Observable<returnReclamationsGPA> = new Observable<returnReclamationsGPA>();

  public reclamations$: Observable<any[]> = new Observable<any[]>();
  public reclamations: any[];
  public operation: IntraOperation = null;
  public GPA:GPAGBFStats;
  public GBF:GPAGBFStats;
  public stats:{nbResponsables:number, nbSocietes:number} = {nbResponsables:0, nbSocietes:0};

  public allEtats: Array<{key:number, value:string}> = [];

  public columns: Array<TableColumnInterface>;
  public hiddenColumns;

  /* Modale change status all reclamations Grouped */
  public formChangeEtats: FormGroup;
  public columnsChangeEtats: Array<TableColumnInterface>;

  public modifsGroupes: boolean = false;
  public etatsGroupes: IntraEtat[] = [];
  public valueElementsGroupes = {};

  public elementsFiltresGroupes: boolean = false;
  public valueElementsFiltresGroupes = [];


  public elementsGroupes: IntraReclamation[] = [];

  @ViewChild('list', {static:false}) declare table : SelftablepaginateDirective

  public ido;
  public mode:string = 'GPA';

  public user: PeopleWithrightsInterface;
  public ROLES = ROLES;

  public filtre : LoopBackFilter = {};

  public canCreate:boolean = false; // can create reclamation !

  /* Modale change statut */
  public formChangeEtat: FormGroup;
  public formFolder = 'reclamation';
  public objectEtat: IntraEtat = new IntraEtat();
  public currentEtatChange: IntraEtat = null;
  public columnsChangeEtat: Array<TableColumnInterface>;

  public formAddIntervenantsGroupe: FormGroup;
  public columnsAddIntervenantsGroupe: Array<TableColumnInterface>;

  public formAddInterventionGroupe: FormGroup;
  public columnsAddInterventionGroupe: Array<TableColumnInterface>;


  public intervenants: {key:any, value:string, societe?: string, societeId?: number}[] = [];
  public societesIntervenant: Societes[] = [];

  public serie:string = 'reclamations.list';

  constructor(
    public router: Router,
    public _sessionService: AppSessionService,
    public route: ActivatedRoute,
    public _toastr: ToastrService,
    public authService: LoopBackAuth,
    public intraOperationApiService: IntraOperationApi,
    public intraReclamationService: IntraReclamationApi,
    public appGlobalEventManagerService: AppGlobalEventManagerService,
    public spinner: NgxSpinnerService,
    public formBuilder: FormBuilder,
    public modalService: NgbModal,
    public _cacheService: AppCacheService,
    public _interventionApiService: IntraInterventionApi,
  ) {

    super();

    this.columns =
      [
        // N°	Date	Localisation	Lot	Intervenant	Description	A lever pour le	Etat	Date etat	Urgent	Export

        {key: 'num', name:"N°", type: 'input', typeInput:'number', order:true, filter:true},
        {key: 'date', name:"Date", type: 'date', order:false, filter:false},
        {key: 'localisation', name:"Localisation", type: 'input', order:false, filter:true, width:{'width.%':15}},
        {key: 'lot', name:"Lot", type: 'input', order:true, filter:true},
        {key: 'description', name:"Description", type: 'text', order:true, filter:false, width:{'width.%':25}},
        {key: 'pourle', name:"A lever pour le", type: 'date', order:true, filter:false},

      ];
    this.hiddenColumns = hiddenColumns.concat([])

    // if(this.appGlobalEventManagerService.filtersRequest[this.serie]){
    //   console.log(this.appGlobalEventManagerService.filtersRequest[this.serie])
    //   this.filtre = this.appGlobalEventManagerService.filtersRequest[this.serie];
    // }

    this.appGlobalEventManagerService.openSpinner();

  }

  load(force=true){

    let self = this;
    /***
     * TODO cache avec this.ido, this.filtre
     */

    // this.call$ = Object.assign(new Observable<any[]>(), this.intraOperationApiService.getReclamationsGPA(this.ido, this.filtre).pipe(share()));

    this.call$ = this._cacheService.getlistReclamationGPAForIdoAndFilter(force, this.ido, this.filtre);

    this.call$.subscribe((result:returnReclamationsGPA) => {

      this.reclamations = result.reclamations.filter((reclam) => {return reclam.enattente == 0;});
      this.reclamations$ = of(this.reclamations); // Uniquement les réclams Validées !

      this.intervenants = [];
      this.societesIntervenant = [];
      result.reclamations.forEach((reclam) => {
        reclam.interventions.forEach((inter) => {
          inter.intervenants.forEach((intervenant:IntraInterventionReclamationPeople)=> {

            if(intervenant.people){
              let index = this.intervenants.findIndex((r) => {return r.key == intervenant.people.id});
              if(index == -1){
                this.intervenants.push(
                  {key: intervenant.people.id, societeId: ((intervenant.people.societes && intervenant.people.societes[0]) ? intervenant.people.societes[0].id : null),  societe: ((intervenant.people.societes && intervenant.people.societes[0]) ? intervenant.people.societes[0].nom : '') ,value:intervenant.people.firstname + ' ' + intervenant.people.lastname +((intervenant.people.societes && intervenant.people.societes[0]) ? (' ('+intervenant.people.societes[0].nom+')') : '' )}
                );
              }
            }

            if(intervenant.people && intervenant.people.societes && intervenant.people.societes.length > 0){
              let index2 = this.societesIntervenant.findIndex((r) => {return r && intervenant.people && intervenant.people.societes && intervenant.people.societes.length > 0 && r.id == intervenant.people.societes[0].id});
              if(index2 == -1){
                this.societesIntervenant.push(intervenant.people.societes[0]);
              }
            }

          });
        });
      });

      this.operation = result.operation;
      this.GPA = result.GPA;
      this.GBF = result.GBF;

      /***
       * Obtenir tous les états des Réclamations
       */
      let allEtats = [];
      if(this.GPA && this.GPA.details)
      this.GPA.details.forEach((detGPA) => {
        const index = allEtats.findIndex((el) => {return el.key == detGPA.etat_id})
        if(index == -1){
          allEtats.push({key:detGPA.etat_id, value: detGPA.libelle})
        }
      })

      if(this.GBF && this.GBF.details)
      this.GBF.details.forEach((detGBF) => {
        const index = allEtats.findIndex((el) => {return el.key == detGBF.etat_id})
        if(index == -1){
          allEtats.push({key:detGBF.etat_id, value: detGBF.libelle})
        }
      })
      self.allEtats = allEtats; // Set in order to emit value change §



      if(this.GPA.nb == 0 && this.GBF.nb > 0){
        this.mode = 'GBF';
      }

      /**
       * STATS
       * Get Societes peopleLinked
       * Get responsables : peopleLinked sans entreprises */
      let cptSocietes = {};
      let cptResponsables = {};
      this.operation.responsablesRelations.forEach((resp) => {
        if(resp.people && resp.people.societes && resp.people.societes.length > 0){

          if(resp.role.code != 'ENT'){
            cptResponsables[resp.people.firstname+resp.people.lastname] = {societe:resp.people.societes[0].nom}
          }else{
            cptSocietes[resp.people.societes[0].nom] = {societe:resp.people.societes[0].nom, nom: resp.people.firstname + ' ' + resp.people.lastname}
          }
        }
      });
      this.stats = {nbResponsables: Object.keys(cptResponsables).length, nbSocietes : Object.keys(cptSocietes).length};


      /***
       * can create ?
       */
      let userRoles = this.operation.responsablesRelations.find((el) => {return el.people_id == self.user.id});
      if(userRoles){
        self.canCreate = userRoles.rules.some((el) => {
          return el.etat_id == 5;
        });
      }


      this.appGlobalEventManagerService.updateTitle('Liste des réclamations - opération '+this.operation.libelle);
      this.appGlobalEventManagerService.updateBreadcrumb([{title:'Home', url:'/'}, {title:'Liste des opérations GPA & GBF', url:'/GPAnet/operations'}, {title:'Liste des réclamations - opération '+this.operation.libelle, url:'/GPAnet/reclamations/'+this.ido }])

      this.changeMode(this.mode)

      setTimeout(() => {
        this.appGlobalEventManagerService.closeSpinner();
      }, 600)

    })
  }

  /**
   * Filter request
   * @param value
   * @param $event
   */
  filter(value:string, $event) {
    if(!this.filtre || !this.filtre['where']){
      this.filtre['where'] = {}
    }

    // console.log('ADD FILTER SUB',value, $event);

    if(value && $event){
      this.filtre['where'] = filterBuilder.buildTerm(value, $event.target.value) ;
    }
    this.load();
  }

  getParameter(){
    this.route.paramMap.subscribe(params => {
      this.ido = params.get("ido");
      this.mode = (params.get("mode")) ? params.get("mode") : 'GPA' ;

      if(this.ido == 'new'){this.router.navigate(['/GPAnet']); return}
      this.load();
    })
  }

  changeMode(mode){
    if(this.table){
      setTimeout(() => {
        this.mode = mode;
        this.table.addFilter({key:'mode', type:'unknown'}, this.mode);
      }, 500)
    }
  }

  ngOnInit(): void {
    this._sessionService.logguedAccountEmitter.subscribe((people) => {
      this.user = people;
    })

    this.getParameter();
  }

  openModaleChangeStatut(content, reclamation, etat:IntraEtat){

    this.currentEtatChange = etat;

    // console.log(etat);

    if(this.currentEtatChange.code == 'DI'){
      this.router.navigate(['/GPAnet', 'reclamation', 'ajoutintervenant', reclamation.id, this.ido])
      return;
    }

    if(this.currentEtatChange.code == 'ACI'){
      /*  Mode Extend */

      this.formChangeEtat = this.formBuilder.group({
        statut: [etat.id, [Validators.required]],
        reclamationId: [reclamation.id, [Validators.required]],
        observations: ['', []],
        dateintervention: ['', []],
        Commentaires: ['', []],
      });

      this.columnsChangeEtat =
        [
          {type:"separator", name:"Changer l'état de la réclamation N°"+reclamation.num+" en <strong>"+etat.libelle+"</strong>", icon:"fa-duotone fa-wrench", exclude:true},
          {key: "observations",name: "Observations", type: "textarea"},
          {key: "dateintervention",name: "Date d'intervention", type: "date"},
          {key: "Commentaires",name: "Commentaires", type: "textarea"},

        ];

    }else if(this.currentEtatChange.code == 'TR'){

      this.formChangeEtat = this.formBuilder.group({
        statut: [etat.id, [Validators.required]],
        reclamationId: [reclamation.id, [Validators.required]],
        observations: ['', []],
        daterealisation: ['', []],
        Commentaires: ['', []],
      });

      this.columnsChangeEtat =
        [
          {type:"separator", name:"Changer l'état de la réclamation N°"+reclamation.num+" en <strong>"+etat.libelle+"</strong>", icon:"fa-duotone fa-wrench", exclude:true},
          {key: "observations",name: "Observations", type: "textarea"},
          {key: "daterealisation",name: "Date de réalisation", type: "date"},
          {key: "Commentaires",name: "Commentaires", type: "textarea"},

        ];

    }else{
      this.formChangeEtat = this.formBuilder.group({
        statut: [etat.id, [Validators.required]],
        reclamationId: [reclamation.id, [Validators.required]],
        observations: ['', []]
      });

      this.columnsChangeEtat =
        [
          {type:"separator", name:"Changer l'état de la réclamation N°"+reclamation.num+" en <strong>"+etat.libelle+"</strong>", icon:"fa-duotone fa-wrench", exclude:true},
          {key: "observations",name: "Observations", type: "textarea"}

        ];

    }

    this.modalService.open(content, { size: 'lg', centered: false });
  }


  openModaleAjoutIntervenantGroupe(content){


    this.formAddIntervenantsGroupe = this.formBuilder.group({
      intervenants: ['', [Validators.required]],
    });

    this.columnsAddIntervenantsGroupe =
      [
        {type:"separator", name:"Assignation d'intervenants pour la toute la sélection", icon:"fa-duotone fa-bookmark", exclude:true},
        {key: "intervenants",name: "Définissez le(s) intervenant(s)", type: "select", selectOptions:{labelBind:'value', keyBind:'key', multiple: true, groupBy:'societe', selectableGroup:true, selectableGroupAsModel: true, options:this.intervenants}},
      ];


    this.modalService.open(content, { size: 'lg', centered: false });
  }

  addIntervenantsGroupe(event){

    this.appGlobalEventManagerService.openSpinner();

    const subs = [];
    const inters  = JSON.parse(JSON.stringify(this.intervenants.filter((int) => event.intervenants.indexOf(int.key) > -1)));
    const intervenantsDatas = inters.map((e) => {return {people_id: e.key, operation_id: this.operation.id, societe_id: e.societeId}});
    for(let reclamId of Object.keys(this.valueElementsGroupes)){
      const reclamation = this.reclamations.find((r) => r.id == reclamId);
      if(reclamation){
        intervenantsDatas.forEach((int) => {
          const exists = reclamation.peopleEntreprisesLinked.find((pel) => pel.operation_id === int.operation_id && pel.people_id === int.people_id && pel.societe_id === int.societe_id);
          if(!exists){
            subs.push(this.intraReclamationService.createPeopleEntreprisesLinked(reclamId, int));
          }
        })
      }
    }

    if(subs.length > 0){
      forkJoin(subs).subscribe((res) => {
        this.load();
        this.modalService.dismissAll();
        this.appGlobalEventManagerService.closeSpinner();
      })
    } else {
      this.modalService.dismissAll();
      this.appGlobalEventManagerService.closeSpinner();
    }


  }


  openModaleAjoutInterventionsGroupe(content){


    this.formAddInterventionGroupe = this.formBuilder.group({
      intervenants: ['', [Validators.required]],
      dateprevue: ['', [Validators.required]],
      description: ['', []],
      commentaire: ['', []]
    });

    this.columnsAddInterventionGroupe =
      [
        {type:"separator", name:"Assignation d'interventions pour la sélection", icon:"fa-duotone fa-bookmark", exclude:true},
        {key: "intervenants",name: "Définissez le(s) intervenant(s)", type: "select", selectOptions:{labelBind:'value', keyBind:'key', multiple: true, groupBy:'societe', selectableGroup:true, selectableGroupAsModel: true, options:this.intervenants}},
        {key: "dateprevue",name: "Date prévue", type: "date"},
        {key: "description",name: "Description", type: "textarea"},
        {key: "commentaire",name: "Commentaire", type: "textarea"}
      ];



    this.modalService.open(content, { size: 'lg', centered: false });
  }

  addInterventionsGroupe(event){
    this.appGlobalEventManagerService.openSpinner();
    let date = moment({years: event['dateprevue'].year, months: event['dateprevue'].month -1, days: event['dateprevue'].day});//.format('YYYY-MM-DD');
    event.reclamationsIds = this.valueElementsGroupes;
    event.date = date;
    event.operationId = this.operation.id;
    this.intraReclamationService.addMultipleInterventions(event).subscribe((res) => {
      this.load();
      this.modalService.dismissAll();
      this.appGlobalEventManagerService.closeSpinner();
    })
  }


  validateChangerEtat(event){

    let self = this;

    // event['reclamationId'] = this.id;
    //
    this.intraReclamationService.changeStatut(event).subscribe((r) => {
      /* ToasTr !  */

      if(r.reclamation){

        this.modalService.dismissAll();

        setTimeout(() => {
          self.load(true);
        }, 350)

        this._toastr.success('Le statut de la réclamation a été mis à jour', 'Changement de statut')
      }else{
        this._toastr.error('Le statut de la réclamation n\'a pas été mis à jour', 'Changement de statut')
      }
    }, (err) => {
      this._toastr.error(err.message, 'Changement de statut')
    });
  }

  callbackAfterFilter($event: {filters:Array<{column:TableColumnInterface, value:any, op:string}>, elements:any[]}){
    let self = this;

    if($event.filters['etat_id'] && $event.elements.length > 0){
      this.modifsGroupes = true;
      this.elementsGroupes = $event.elements;

      this.etatsGroupes = [];
      this.valueElementsGroupes = {};
      this.elementsGroupes.forEach((rec) => {

        this.valueElementsGroupes[rec.id] = true;

        rec['nextEtats'].forEach((etat:IntraEtat) => {
          if(this.etatsGroupes.findIndex((eg) => {return eg.id == etat.id}) == -1){
            if($event.filters['etat_id']['value'] != etat.id){
              this.etatsGroupes.push(etat);
            }
          }
        });
      });

      this.elementsFiltresGroupes = true;
      self.valueElementsFiltresGroupes = [];
      $event.elements.forEach((el) => {
        self.valueElementsFiltresGroupes.push(el.id);
      });

    }else{

      if(Object.keys($event.filters).length > 1 && $event.elements.length > 0){

        this.elementsFiltresGroupes = true;
        self.valueElementsFiltresGroupes = [];
        $event.elements.forEach((el) => {
          self.valueElementsFiltresGroupes.push(el.id);
        });

      }else{
        this.modifsGroupes = false;
        this.elementsGroupes = [];

        this.elementsFiltresGroupes = false;
        self.valueElementsFiltresGroupes = [];
      }
    }

    // console.log('After Filter !',$event);


  }


  /*****
   * Liste des Reclamations & change Statut !
   * @param content
   * @param reclamation
   * @param etat
   */
  openModaleChangeStatuts(content, etat:IntraEtat){

    // console.log(etat);

    if(etat.code == 'ACI' || etat.code == 'TR'){
      /*  Mode Extend */

      this.formChangeEtats = this.formBuilder.group({
        statut: [etat.id, [Validators.required]],
        observations: ['', []],
        dateintervention: ['', []],
        Commentaires: ['', []],
      });

      this.columnsChangeEtats =
        [
          {type:"separator", name:"Changer l'état des réclamations ci-dessus en <strong>"+etat.libelle+"</strong>", icon:"fa-duotone fa-wrench", exclude:true},
          {key: "observations",name: "Observations", type: "textarea"},
          {key: "dateintervention",name: "Date d'intervention", type: "date"},
          {key: "Commentaires",name: "Commentaires", type: "textarea"},

        ];
    }else{
      this.formChangeEtats = this.formBuilder.group({
        statut: [etat.id, [Validators.required]],
        observations: ['', []]
      });

      this.columnsChangeEtats =
        [
          {type:"separator", name:"Changer l'état des réclamations ci-dessus en <strong>"+etat.libelle+"</strong>", icon:"fa-duotone fa-wrench", exclude:true},
          {key: "observations",name: "Observations", type: "textarea"}

        ];

    }

    this.modalService.open(content, { size: 'lg', centered: false });
  }

  validateChangerEtats(event){

    let self = this;

    // this.elementsGroupes


    let listSubs = [];
    Object.keys(this.valueElementsGroupes).forEach((i) => {
      if(self.valueElementsGroupes[i] === true){
        let e = Object.assign({}, event);
        e.reclamationId = i;
        listSubs.push(self.intraReclamationService.changeStatut(e));
      }
    });


    forkJoin(listSubs).subscribe((r) => {
      this.modalService.dismissAll();

      setTimeout(() => {
        self.load(true);
      }, 350)

      this._toastr.success('Le statut des réclamations a été mis à jour', 'Changement de statut groupé')
    }, error => {
      this._toastr.error(error.message, 'Changement de statut groupé')
    });

  }


  /** Call to delete Object **/
  delete(object:IntraReclamation){

    this.intraReclamationService.deleteById(object.id).subscribe(
      (res) => {
        this._toastr.success('Suppression de la réclamation', 'La réclamation a été supprimé avec succès');
        this._cacheService.deleteCachelistReclamationGPAForIdoAndFilter(); // Invalidate Cache !
        this.load(); // Refresh list
      },
      (err) => {this._toastr.success('Suppression de la réclamation', err.message);}

    );


  }


}
