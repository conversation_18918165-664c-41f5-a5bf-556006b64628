import {Component, Input} from "@angular/core";
import {apiParameterInterface} from "../../../../shared/interfaces/apiParameter.interface";
import {AppExportGPAService} from "../../../services/app.export.service";

@Component({
  selector: 'exportGPADownload',
  templateUrl: './downloadExport.partials.html'
})
export class DownloadGPAExportPartials {

  @Input('operationId') operationId: number = null;
  @Input('societeId') societeId: number = null;
  @Input('reclamationId') reclamationId: number = null;
  @Input('type') type: 'GPA'|'GBF'|string|null = null;
  @Input('typeExport') typeExport:string = null;
  @Input('typeFile') typeFile:string = 'PDF';
  @Input('idList') idList:string|string[] = null; // List Of IDS !
  @Input('debug') debug:string = null;


  @Input('label') label: string = null;
  @Input('class') class: string = 'btn btn-blue btn-social btn-sm mL5';

  public apiParameters: apiParameterInterface;

  constructor(
    public _appExportService: AppExportGPAService
  ) {
  }


  getExport(){

    if(this.idList){
      if(Array.isArray(this.idList)){
        this.idList = this.idList.join(',');
      }
    }

    this._appExportService.getExport(this.operationId,this.reclamationId,this.societeId,this.type,this.typeExport, this.typeFile, this.idList,this.debug);
  }

}
