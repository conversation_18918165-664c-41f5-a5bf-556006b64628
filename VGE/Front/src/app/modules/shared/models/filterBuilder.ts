export class filterBuilder{

  constructor(

  ){

  }

  /**
   * TODO
   *
   * Build a where complexe to request on Loopback
   * @param conditions = array
   * [
   *  {
   *   aggregator:'and',
   *   conditions: [
   *      {field:'createAt', value:begindateF, operator:'gte'},
   *      {field:'createAt', value:enddateF, operator:'lte'}
   *   ]
   *  },
   *  ...
   * ]
   *
   *
   *
   * @return
   * and:[
   *  {createAt: {gte: begindateF}},
   *  {createAt: {lte: enddateF}}
   * ]
   *
   *
   * {%22and%22:[{%22published%22:true},{%22page%22:%22CREATE_ACCOUNT%22}]}
   *
   */
  static buildTerms(conditions){
    var result = '';
    conditions.forEach(function(condGroup){

      var agg = condGroup.aggregator;
      var t = '{"'+agg+'":[condstoReplace]}';

      var conds = [];
      condGroup.conditions.forEach(function(cond){
        var term = filterBuilder.buildTerm(cond.field, cond.value, cond.operator)
        conds.push(term);
      });

      t = t.replace( 'condstoReplace', conds.join(',') );
      result += t;

    });

    return JSON.parse(result);
  }

  /**
   * Convert a field and a value to API "where" well formed
   * @param field
   * @param value
   * @returns {string}
   */
  static buildTerm(field:string, value:any, operator:string = "like"){

    let where = '{}'
    if(field && value !== ''){
      var cotes = true;
      if(typeof(value) === 'string'){
        cotes = true;
      }
      if(typeof(value) === 'boolean'){
        cotes = false;
      }

      // https://loopback.io/doc/en/lb3/Where-filter.html#ilike-and-nilike
      switch (operator){
        case "equal": {
          where = '{"'+field+'":' + ((cotes) ? '"' : '') + value+ ((cotes) ? '"' : '') + '}';
        }
        break;
        case "nlike":
        case "nilike":
        case "ilike":
        case "like":{
          where = '{"'+field+'":{"'+operator+'":' + ((cotes) ? '"%' : '') + value+ ((cotes) ? '%", "options":"i"' : '') + '}}';
        }
          break;
        case "neq":{
          where = '{"'+field+'":{"neq":' + ((cotes) ? '"' : '') + value + ((cotes) ? '"' : '') + '}}';
        }
          break;
        case "regex":{
          where = '{"'+field+'":{"regex":' + ((cotes) ? '"' : '') + value + ((cotes) ? '"' : '') + '}}';
        }
          break;
        default:
          where = '{"'+field+'":{"'+operator+'":' + value  + '}}';

      }
    }

    return JSON.parse(where);
  }

}
