<div class="modal-header">
  <h4 class="modal-title">Fiche développement</h4>
  <button type="button" class="close" aria-label="Close" (click)="activeModal.dismiss('Cross click')">
    <span aria-hidden="true">&times;</span>
  </button>
</div>

<div class="modal-body" *ngIf="development">
  <div class="row">
    <div class="col-md-12">
      <div class="">
        <div class="">

          <div class="card">
            <div class="card-header">
              <h5 class="card-title" (dblclick)="modeEdition['libelle'] = true" [ngbTooltip]="'Double cliquer pour éditer'">
                <strong *ngIf="!modeEdition['libelle']">{{development.libelle}}</strong>
                <div *ngIf="modeEdition['libelle']">
                  <input type="text" class="form-control" [(ngModel)]="development.libelle" (keyup.enter)="saveLibelle()" (focusout)="saveLibelle()" (keyup.escape)="modeEdition['libelle'] = false" />
                </div>
              </h5>
            </div>
          </div>

          <div class="row mt-2">
            <div class="col-md-8">

              <div class="card">
                <div class="card-header"><h6>Descriptif du développement</h6></div>
                <div class="card-body">

                  <ng-container *ngIf="!modeEdition['description']">
                    <div [ngbTooltip]="'Double cliquer pour éditer la description'" container="body" position="top" (dblclick)="modeEdition['description'] = true" [innerHTML]="development.description"></div>
                  </ng-container>

                  <ng-container *ngIf="modeEdition['description']">

	                  <CKeditorForm [(ngModel)]="development.description" ></CKeditorForm>

                    <div class="row">
                      <div class="col-md-10">
                        <button class="btn btn-success btn-social btn-block btn-sm mT10" (click)="saveDescription()"><i class="fa fa-duotone fa-floppy-disk"></i> Modifier la description</button>
                      </div>
                      <div class="col-md-2">
                        <button class="btn btn-danger btn-social btn-block btn-sm mT10" (click)="modeEdition['description'] = false"><i class="fa fa-duotone fa-stop"></i> Annuler</button>
                      </div>
                    </div>

                  </ng-container>

                </div>
              </div>


              <div class="card">
                <div class="card-header"><h6>Activité</h6></div>
                <div class="card-body">

                  <div class="card"*ngFor="let histo of development.historiques">
                    <div class="row">
                      <div class="col-md-3">
                        <div class="p-1">
                          <people [people]="histo.createur" [small]="true" [showInitiales]="false" [showGroup]="false" [showMediaPhoto]="true"></people>
                          Le {{moment(histo.date, 'YYYY-MM-DD HH:mm:ss').format('DD/MM/YYYY - HH:mm')}}
                        </div>
                      </div>
                      <div class="" [ngClass]="{'col-md-7': histo?.createur_id === user.id, 'col-md-9': histo?.createur_id !== user.id}">
                        <div class="p-1" [innerHTML]="histo.description"></div>
                      </div>

                      <div class="col-md-2 text-right" *ngIf="histo?.createur_id === user.id">
                        <div class="btn-group p-1">
                          <button class="btn btn-info btn-sm mT10" (click)="editHisto(histo)"><i class="fa fa-duotone fa-edit"></i></button>
                          <button class="btn btn-danger btn-sm mT10"
                                  mwlConfirmationPopover
                                  [popoverTitle]="'Suppression de l\'activité'"
                                  [popoverMessage]="'Êtes vous sur de vouloir supprimer cette activité ?'"
                                  placement="left"
                                  (confirm)="deleteHisto(histo)"
                          ><i class="fa fa-duotone fa-trash-alt"></i></button>
                        </div>
                      </div>
                    </div>
                  </div>
                  <hr *ngIf="development.historiques.length > 0"/>

	                <CKeditorForm [(ngModel)]="histo.description" ></CKeditorForm>

                  <div class="row">
                    <div class="col-md-12">
                      <button class="btn btn-success btn-social btn-block btn-sm mT10" *ngIf="!histo.id" (click)="saveHisto()"><i class="fa fa-duotone fa-floppy-disk"></i> Ajouter une activité</button>
                      <button class="btn btn-success btn-social btn-block btn-sm mT10" *ngIf="histo.id" (click)="saveHisto()"><i class="fa fa-duotone fa-floppy-disk"></i> Modifier une activité</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-4">
              <div class="card">
                <div class="card-header">
                <h6>
                  <strong>Pièces jointes (Doc, PDF, impression d'écran)</strong>
                  <button [ngbTooltip]="'Ajouter une pièce jointe'" *ngIf="(user|hasRight:'admin') || user.id === development?.createur_id" class="btn btn-sm btn-info pull-right" (click)="addPJ = true"><i class="fa fa-duotone fa-plus"></i></button>
                </h6>
                </div>
                <div class="card-body">
                  <table width="100%" class="table table-compact table-hover table-striped" *ngIf="development?.files?.length > 0">
                    <tbody>
                      <tr *ngFor="let doc of development?.files">
                        <td>{{moment(doc?.date, 'YYYY-MM-DD HH:mm:ss').format('DD/MM/YYYY')}}</td>
                        <td><people [people]="doc?.createur" [small]="true" [showMediaPhoto]="true" [showInitiales]="false" [reverseNoms]="false" [showGroup]="false" [showSociete]="false"></people></td>
                        <td>
                          <span class="fs-9">{{(doc?.file|getFilename)?.filenameWithoutExt}}</span>
                        </td>
                        <td>
                          <i class="fa fa-2x text-success fa-file-{{(doc.file|getFilename)?.ext}}"></i>
                        </td>
                        <td>
                          <imageTable *ngIf="['jpg','png','jpeg','webp'].indexOf((doc.file|getFilename)?.ext.toLowerCase()) > -1" [preview]="true" [value]="doc.file"></imageTable>
                        </td>
                        <td class="text-right">
                          <span *ngIf="doc?.file">
                            <fileDownload [lien]="doc.file" [type]="'file'" [class]="'btn btn-sm btn-info'" [label]="null"></fileDownload>
                          </span>

                          <button type="button" class="btn btn-outline-danger btn-sm ml-1"
                                  mwlConfirmationPopover
                                  [popoverTitle]="'Suppression du fichier ?'"
                                  [popoverMessage]="'Êtes vous sur de vouloir supprimer ce fichier ?'"
                                  placement="left"
                                  (confirm)="deleteFile(doc)"
                          >
                            <i class="ft-trash-2"></i>
                          </button>
                        </td>
                      </tr>
                    </tbody>
                  </table>

                  <ng-container *ngIf="(user|hasRight:'admin') || user.id === development?.createur_id">
                    <div *ngIf="addPJ">
                      <h5>Ajouter un fichier</h5>
                      <div class="row">
                        <div class="col-md-12">
                          <form [formGroup]="formAddfile" (ngSubmit)="addFile()">
                            <div class="form-body">
                              <fileForm
                                #uploadFile
                                [id]="'file'"
                                [name]="'file'"
                                [folder]="folder"
                                [form]="formAddfile"
                                formControlName="file"
                                [label]="'Ajouter un nouveau fichier'"
                                [placeholder]="'Ajouter un nouveau fichier'"
                              ></fileForm>
                            </div>
                            <div class="form-actions right">
                              <button type="submit" [disabled]="!formAddfile.get('file').value" class="btn btn-primary  btn-sm">
                                <i class="fa-duotone fa-check"></i> Valider le fichier
                              </button>
                              <button class="btn btn-danger btn-sm ml-1" (click)="addPJ = false">
                                <i class="fa-duotone fa-stop"></i> Annuler
                              </button>
                            </div>
                          </form>
                        </div>
                      </div>
                    </div>
                  </ng-container>
                </div>
              </div>

              <ng-container *ngIf="user.accessEbuiz === 1">

                <div class="card">
                  <div class="card-header">
                    <h6>
                      <strong>Devis</strong>
                      <button [ngbTooltip]="'Ajouter un devis'" *ngIf="(user|hasRight:'admin') || user.id === development?.createur_id" class="btn btn-sm btn-info pull-right" (click)="addDevis = true"><i class="fa fa-duotone fa-plus"></i></button>
                    </h6>
                  </div>
                  <div class="card-body">

                    <div class="d-flex justify-content-around" *ngIf="development.pj2">
                      <filePresentation class="flex-grow-1" [link]="development.pj2"></filePresentation>
                      <div>
                        <fileDownload type="file" [tooltip]="'Devis pour ce développement'" [lien]="development.pj2"></fileDownload>
                        <button class="btn btn-danger btn-sm"
                              mwlConfirmationPopover
                              [popoverTitle]="'Suppression du devis'"
                              [popoverMessage]="'Êtes vous sur de vouloir supprimer ce devis ?'"
                              placement="left"
                              (confirm)="deleteDevis()"
                        ><i class="fa fa-duotone fa-trash-alt"></i></button>
                      </div>
                    </div>

                    <ng-container *ngIf="(user|hasRight:'admin') || user.id === development?.createur_id">
                      <div *ngIf="addDevis">

                        <h5 *ngIf="!development.pj2">Ajouter un devis</h5>
                        <h5 *ngIf="development.pj2">Modifier le devis</h5>

                        <div class="row">
                          <div class="col-md-12">
                            <form [formGroup]="formAdddevis" (ngSubmit)="addFileDevis($event)">
                              <div class="form-body">
                                <fileForm
                                  #uploadFile
                                  [id]="'file'"
                                  [name]="'pj2'"
                                  [folder]="folder"
                                  [form]="formAdddevis"
                                  formControlName="pj2"
                                  [label]="(!development.pj2) ? 'Ajouter un devis' : 'Modifier le devis'"
                                  [placeholder]="(!development.pj2) ? 'Ajouter un devis' : 'Modifier le devis'"
                                ></fileForm>
                              </div>
                              <div class="form-actions right">
                                <button type="submit" class="btn btn-primary btn-sm">
                                  <i class="fa-duotone fa-check"></i> Valider le devis
                                </button>
                                <button class="btn btn-danger btn-sm ml-1" (click)="addDevis = false">
                                  <i class="fa-duotone fa-stop"></i> Annuler
                                </button>
                              </div>
                            </form>
                          </div>
                        </div>
                      </div>
                    </ng-container>
                  </div>
                </div>
              </ng-container>

              <div class="card">
                <div class="card-header">
                  <h6><strong>Informations</strong></h6>
                </div>
                <div class="card-body">
                  <ul class="list-group">
                    <li class="list-group-item p-1 d-flex justify-content-between align-items-center">
                      <strong>Section :</strong>
                      <span (dblclick)="modeEdition['section'] = true" [ngbTooltip]="'Double cliquer pour éditer'" *ngIf="!modeEdition['section']">{{development.section}}</span>
                      <div class="text-right" style="min-width: 50%;" *ngIf="modeEdition['section']">
                        <ng-select
                          [items]="sections"
                          [multiple]="false"
                          [closeOnSelect]="true"
                          [searchable]="true"
                          [bindLabel]="'text'"
                          [bindValue]="'key'"
                          [groupBy]="'cat'"
                          [placeholder]="'Choisir une section'"
                          [(ngModel)]="development.section"
                          (ngModelChange)="save($event, 'section')"
                        >
                        </ng-select>
                        <button class="btn btn-danger btn-sm ml-1" (click)="modeEdition['section'] = false"><i class="fa fa-duotone fa-stop"></i> Annuler</button>
                      </div>
                    </li>
                    <li class="list-group-item p-1 d-flex justify-content-between align-items-center">
                      <strong>Priorité :</strong>
                      <span (dblclick)="modeEdition['urgent'] = true" [ngbTooltip]="'Double cliquer pour éditer'" *ngIf="!modeEdition['urgent']">
                        <span class="text-success" *ngIf="development.urgent === 0">{{priorite[0].text}}</span>
                        <span class="text-warning" *ngIf="development.urgent === 1">{{priorite[1].text}}</span>
                        <span class="text-danger" *ngIf="development.urgent === 2">{{priorite[2].text}}</span>
                      </span>

                      <div class="text-right" style="min-width: 50%;" *ngIf="modeEdition['urgent']">
                        <ng-select
                          [items]="priorite"
                          [multiple]="false"
                          [closeOnSelect]="true"
                          [searchable]="true"
                          [bindLabel]="'text'"
                          [bindValue]="'key'"
                          [groupBy]="'cat'"
                          [placeholder]="'Choisir une priorité'"
                          [(ngModel)]="development.urgent"
                          (ngModelChange)="save($event, 'urgent')"
                        >
                        </ng-select>
                        <button class="btn btn-danger btn-sm ml-1" (click)="modeEdition['urgent'] = false"><i class="fa fa-duotone fa-stop"></i> Annuler</button>
                      </div>

                    </li>
                    <li class="list-group-item p-1 d-flex justify-content-between align-items-center">
                      <strong>Type :</strong>

                      <span (dblclick)="modeEdition['type'] = true" [ngbTooltip]="'Double cliquer pour éditer'" *ngIf="!modeEdition['type']">{{development.type}}</span>
                      <div class="text-right" style="min-width: 50%;" *ngIf="modeEdition['type']">
                        <ng-select
                          [items]="types"
                          [multiple]="false"
                          [closeOnSelect]="true"
                          [searchable]="true"
                          [bindLabel]="'text'"
                          [bindValue]="'key'"
                          [groupBy]="'cat'"
                          [placeholder]="'Choisir un type'"
                          [(ngModel)]="development.type"
                          (ngModelChange)="save($event, 'type')"
                        >
                        </ng-select>
                        <button class="btn btn-danger btn-sm ml-1" (click)="modeEdition['type'] = false"><i class="fa fa-duotone fa-stop"></i> Annuler</button>
                      </div>

                    </li>
                    <li class="list-group-item p-1 d-flex justify-content-between align-items-center">
                      <strong>Statut :</strong>
                      <span (dblclick)="modeEdition['etat'] = true" [ngbTooltip]="'Double cliquer pour éditer'" *ngIf="!modeEdition['etat']">{{development.etat}}</span>
                      <div class="text-right" style="min-width: 50%;" *ngIf="modeEdition['etat']">
                        <ng-select
                          [items]="etats"
                          [multiple]="false"
                          [closeOnSelect]="true"
                          [searchable]="true"
                          [bindLabel]="'text'"
                          [bindValue]="'key'"
                          [groupBy]="'cat'"
                          [placeholder]="'Choisir un statut'"
                          [(ngModel)]="development.etat"
                          (ngModelChange)="save($event, 'etat')"
                        >
                        </ng-select>
                        <button class="btn btn-danger btn-sm ml-1" (click)="modeEdition['etat'] = false"><i class="fa fa-duotone fa-stop"></i> Annuler</button>
                      </div>
                    </li>

                    <li class="list-group-item p-1 d-flex justify-content-between align-items-center">
                      <strong>Créé par :</strong>
                      <people style="margin-top: -2px;"   [people]="development.createur" [showMediaPhoto]="true" [showGroup]="false" [small]="true"></people>
                    </li>

                    <li class="list-group-item p-1 d-flex justify-content-between align-items-center">
                      <strong>Date de création :</strong>
                      <span (dblclick)="modeEdition['dateCreation'] = true" [ngbTooltip]="'Double cliquer pour éditer'" *ngIf="development.dateCreation && !modeEdition['dateCreation']">{{moment(development.dateCreation).format('DD/MM/YYYY')}}</span>
                      <span (dblclick)="modeEdition['dateCreation'] = true" [ngbTooltip]="'Double cliquer pour éditer'" *ngIf="!development.dateCreation && !modeEdition['dateCreation']">Non renseigné</span>

                      <div class="text-right" style="min-width: 50%;" *ngIf="modeEdition['dateCreation']">
                        <div class="text-right" style="min-width: 50%;" *ngIf="modeEdition['dateCreation']">
                          <input type="date" class="form-control"
                                 [ngModel]="development.dateCreation | date:'yyyy-MM-dd'"
                                 (ngModelChange)="development.dateCreation = $event"
                                 (focusout)="saveDateCreation()" (keyup.enter)="saveDateCreation()" (keyup.escape)="modeEdition['dateCreation'] = false" />

                          <div class="btn-group">
                            <button class="btn btn-info btn-sm" [ngbTooltip]="'Supprimer la date'" (click)="development.dateCreation = null;saveDateCreation()"><i class="fa fa-duotone fa-eraser"></i></button>
                            <button class="btn btn-danger btn-sm" (click)="modeEdition['dateCreation'] = false"><i class="fa fa-duotone fa-stop"></i> Annuler</button>
                          </div>
                        </div>
                      </div>
                    </li>

                    <li class="list-group-item p-1 d-flex justify-content-between align-items-center">
                      <strong>Date de livraison estimée :</strong>
                      <span (dblclick)="modeEdition['datelivraisonEst'] = true" [ngbTooltip]="'Double cliquer pour éditer'" *ngIf="development.datelivraisonEst && !modeEdition['datelivraisonEst']">{{moment(development.datelivraisonEst).format('DD/MM/YYYY')}}</span>
                      <span (dblclick)="modeEdition['datelivraisonEst'] = true" [ngbTooltip]="'Double cliquer pour éditer'" *ngIf="!development.datelivraisonEst && !modeEdition['datelivraisonEst']">Non renseigné</span>

                      <div class="text-right" style="min-width: 50%;" *ngIf="modeEdition['datelivraisonEst']">
                        <input type="date" class="form-control"
                               [ngModel]="development.datelivraisonEst | date:'yyyy-MM-dd'"
                               (ngModelChange)="development.datelivraisonEst = $event"
                               (focusout)="saveDateLivraisonEst()" (keyup.enter)="saveDateLivraisonEst()" (keyup.escape)="modeEdition['datelivraisonEst'] = false" />

                        <div class="btn-group">
                          <button class="btn btn-info btn-sm" [ngbTooltip]="'Supprimer la date'" (click)="development.datelivraisonEst = null;saveDateLivraisonEst()"><i class="fa fa-duotone fa-eraser"></i></button>
                          <button class="btn btn-danger btn-sm" (click)="modeEdition['datelivraisonEst'] = false"><i class="fa fa-duotone fa-stop"></i> Annuler</button>
                        </div>
                      </div>
                    </li>

                    <li class="list-group-item p-1 d-flex justify-content-between align-items-center">
                      <strong>Date de livraison :</strong>
                      <span (dblclick)="modeEdition['dateLivr'] = true" [ngbTooltip]="'Double cliquer pour éditer'" *ngIf="development.dateLivr && !modeEdition['dateLivr']">{{moment(development.dateLivr).format('DD/MM/YYYY')}}</span>
                      <span (dblclick)="modeEdition['dateLivr'] = true" [ngbTooltip]="'Double cliquer pour éditer'" *ngIf="!development.dateLivr && !modeEdition['dateLivr']">Non renseigné</span>

                      <div class="text-right" style="min-width: 50%;" *ngIf="modeEdition['dateLivr']">
                        <input type="date" class="form-control"
                               [ngModel]="development.dateLivr | date:'yyyy-MM-dd'"
                               (ngModelChange)="development.dateLivr = $event"
                               (focusout)="saveDateLivraison()" (keyup.enter)="saveDateLivraison()" (keyup.escape)="modeEdition['dateLivr'] = false" />

                        <div class="btn-group">
                          <button class="btn btn-info btn-sm" [ngbTooltip]="'Supprimer la date'" (click)="development.dateLivr = null;saveDateLivraison()"><i class="fa fa-duotone fa-eraser"></i></button>
                          <button class="btn btn-danger btn-sm" (click)="modeEdition['dateLivr'] = false"><i class="fa fa-duotone fa-stop"></i> Annuler</button>
                        </div>
                      </div>

                    </li>

                    <li class="list-group-item p-1 d-flex justify-content-between align-items-center">
                      <strong>Avancement :</strong>
                      <span (dblclick)="modeEdition['avancement'] = true" [ngbTooltip]="'Double cliquer pour éditer'" *ngIf="!modeEdition['avancement']">{{development.avancement}}%</span>
                      <div class="text-right" style="min-width: 50%;" *ngIf="modeEdition['avancement']">
                        <input type="number" min="0" max="100" class="form-control" [(ngModel)]="development.avancement" (focusout)="saveAvancement()" (keyup.enter)="saveAvancement()" (keyup.escape)="modeEdition['avancement'] = false" />
                        <button class="btn btn-danger btn-sm ml-1" (click)="modeEdition['avancement'] = false"><i class="fa fa-duotone fa-stop"></i> Annuler</button>
                      </div>
                    </li>

                  </ul>

                </div>
              </div>
              <div class="card">
                <div class="card-header">
                  <h6>Responsables</h6>
                </div>
                <div class="card-body">
                  <div class="d-flex align-items-start">
                    <ng-container *ngIf="development.responsable" ><people [people]="development.responsable" [showMediaPhoto]="true" [small]="true"></people></ng-container>
                    <ng-container *ngIf="development.responsable2" ><people [people]="development.responsable2" [showMediaPhoto]="true" [small]="true" class="ml-2"></people></ng-container>
                  </div>
                  <hr *ngIf="development.responsable3 || development.responsable4" />
                  <div class="d-flex align-items-start" *ngIf="development.responsable3 || development.responsable4">
                    <ng-container *ngIf="development.responsable3" ><people [people]="development.responsable3" [showMediaPhoto]="true" [small]="true" class=""></people></ng-container>
                    <ng-container *ngIf="development.responsable4" ><people [people]="development.responsable4" [showMediaPhoto]="true" [small]="true" class="ml-2"></people></ng-container>
                  </div>
                  <hr *ngIf="development.responsable5" />
                  <div class="d-flex align-items-start" *ngIf="development.responsable5">
                    <ng-container *ngIf="development.responsable5" ><people [people]="development.responsable5" [showMediaPhoto]="true" [small]="true" class=""></people></ng-container>
                  </div>
                </div>

              </div>

            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

</div>

<div class="modal-footer">
  <button type="button" class="btn btn-outline-dark" (click)="activeModal.close('Close click')">Fermer</button>
</div>
