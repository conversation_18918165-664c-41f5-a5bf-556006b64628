<div class="jumbotron jumbotron-fluid bg-success bg-accent-1 mB0">
  <div class="container">
    <h1 class="text-center">Répartition des affaires par opérations</h1>
  </div>
</div>

<!--{{filtre|json}}-->

<div class="row mb-">
  <div class="col-4 mb-1">

  </div>
  <div class="col-8 mb-1 text-right">

  </div>
</div>

<div class="row">

  <div class="content-body col-12">
    <!-- / Buttons TOP -->
    <div class="row">
      <div class="col-12">

        <div class="card">
          <div class="card-header">
            <h4></h4>
          </div>
          <div class="card-body">
            <table class="table table-sm table-borderless table-compact table-hover">
              <thead>
              <tr>
                <th>
                  <ng-select
                    [items]="roles"
                    [multiple]="false"
                    [closeOnSelect]="false"
                    [searchable]="true"
                    [bindLabel]="'libelle'"
                    [bindValue]="'id'"
                    [(ngModel)]="selectedRole2"
                    (ngModelChange)="changeTypeRole(getRolesSelect.selectedValues[0])"
                    #getRolesSelect
                    [placeholder]="'Changer le role principal'"
                  ></ng-select>
                </th>
                <th>NB total d'immeubles</th>
                <th>NB OPEX</th>
                <th>NB Dossiers en cours</th>
                <th>NB Dossiers terminés</th>
                <th>NB Dossiers total</th>
                <th>NB CX</th>
              </tr>
              </thead>
              <tbody>
                <tr *ngFor="let admin of miniTab|keyvalue; let i = index">
                  <td><div><people [showMediaPhoto]="true" [noWrap]="true" [showInitiales]="false" [showGroup]="false" [people]="admins|find:'nameComplex':admin?.key"></people></div></td>
                  <td>{{admin?.value['arbo']?.tot?.nbOP}}</td>
                  <td>{{admin?.value['arbo']?.tot?.OPEX}}</td>
                  <td>{{admin?.value['arbo']?.tot?.nbSinEC}}</td>
                  <td>{{admin?.value['arbo']?.tot?.nbSinT}}</td>
                  <td>{{admin?.value['arbo']?.tot?.nbSinEC + admin?.value['arbo']?.tot?.nbSinT}}</td>
                  <td>{{admin?.value['arbo']?.tot?.nbCX}}</td>
                </tr>
              </tbody>
              <tfoot>
                <tr>
                  <th></th>
                  <th>{{ttx?.nbOP}}</th>
                  <th>{{ttx?.OPEX}}</th>
                  <th>{{ttx?.nbSinEC}}</th>
                  <th>{{ttx?.nbSinT}}</th>
                  <th>{{ttx?.nbSinEC + ttx?.nbSinT}}</th>
                  <th>{{ttx?.nbCX}}</th>
                </tr>
              </tfoot>
            </table>
          </div>
        </div>
      </div>
    </div>



    <hr/>

<!--    {{filtre|json}}-->

    <div class="alert bg-info small alert-icon-right alert-dismissible mb-2" role="alert">
      Double cliquer dans une cellule pour ajouter une relation à l'opération
    </div>


    <div class="row">
      <div class="col-4 mB20">
        <button class="mB10 btn btn-info btn-social btn-sm mR5" (click)="showAll()" *ngIf="!mode"><i class="fa-duotone fa-eye"></i> Voir les opérations cachées</button>
        <button class="mB10 btn btn-info btn-social btn-sm mR5" (click)="hideAll()" *ngIf="mode"><i class="fa-duotone fa-eye-slash"></i> Ne pas afficher les opérations cachées</button>
      </div>
      <div class="col-8 mB20">
        <button class="mB10 btn btn-info btn-social btn-sm mL5 pull-right" [routerLink]="['/', 'eBoarding', 'repartition-gestionnaire']"><i class="fa-duotone fa-people-roof"></i> Répartition par gestionnaire</button>
        <button type="button" (click)="(listIds.length > 0) ? exportPDFById() : exportPDF()" [ngbTooltip]="'Exporter le tableau ?'" placement="left" data-toggle="dropdown" class="btn btn-blue btn-social btn-sm mL5 pull-right"><i class="ft-upload"></i> {{(listIds.length > 0) ? 'Exporter les filtrés' : 'Exporter tout'}}</button>
      </div>


    </div>


    <selfTablePaginate
      [columns]="columns"
      [defaultKeyColumn]="'libelle'"
      [data]="operationList|AsObservable"
      list="operationsEbuz"
      [idKey]="'id'"
      [fixTopRow]="true"
      [overflow]="false"
      [scrollable]="true"
      [bodyTopTemplate]="bodyTop"
      [bodyBottomTemplate]="bodyBottom"
      [headerTopTemplate]="headerTop"
      [headerBottomTemplate]="headerBottom"
      [footerTopTemplate]="headerTop"
      [footerBottomTemplate]="footerBottom"
      [actionsTemplate]="actions"
      #list
      [headerSearchTemplate]="headerSearchTemplate"
      (filtredElements)="elementsFiltres($event)"
    >
    </selfTablePaginate>


    <ng-template #headerSearchTemplate>
      <tr class="sticky sticky2 bg-white">
        <th></th>
        <th>
          <ng-select
            [items]="cliList"
            [multiple]="false"
            [closeOnSelect]="false"
            [searchable]="true"
            [bindLabel]="'cli'"
            [bindValue]="'id'"
            (change)="addFilter({key:'cli', type:'unknown',  bindLabel: 'cli', libelle: 'Client'}, $event?.cli, 'equal', opSelect)"
            (clear)="addFilter({key:'cli', type:'unknown',  bindLabel: 'cli', libelle: 'Client'}, null, 'equal', opSelect)"
            #opSelect
            [placeholder]="'Client'"
          ></ng-select>
        </th>
        <th>
          <ng-select
            [items]="fondList"
            [multiple]="false"
            [closeOnSelect]="false"
            [searchable]="true"
            [bindLabel]="'fond'"
            [bindValue]="'id'"
            (change)="addFilter({key:'fond', type:'unknown',  bindLabel: 'fond', libelle: 'Fond'}, $event?.fond, 'equal', opSelect)"
            (clear)="addFilter({key:'fond', type:'unknown',  bindLabel: 'fond', libelle: 'Fond'}, null, 'equal', opSelect)"
            #opSelect
            [placeholder]="'Fond'"
          ></ng-select>
        </th>
        <th style="vertical-align: middle">
          <ng-select
            [items]="operationList"
            [multiple]="false"
            [closeOnSelect]="false"
            [searchable]="true"
            [bindLabel]="'libelle'"
            [bindValue]="'id'"
            (change)="addFilter({key:'id', type:'unknown',  bindLabel: 'libelle', libelle: 'Opération'}, $event?.id, 'equal', opSelect)"
            (clear)="addFilter({key:'id', type:'unknown',  bindLabel: 'libelle', libelle: 'Opération'}, null, 'equal', opSelect)"
            #opSelect
            [placeholder]="'Opération'"
          ></ng-select>
        <th style="vertical-align: middle">

          <ng-select
            [items]="allDpts"
            [multiple]="false"
            [closeOnSelect]="false"
            [searchable]="true"
            (change)="addFilter({key:'dpt', type:'unknown',  bindLabel: 'Dpt', libelle: 'Opération'}, $event, 'equal', opDpt)"
            (clear)="addFilter({key:'dpt', type:'unknown',  bindLabel: 'Dpt', libelle: 'Opération'}, null, 'equal', opDpt)"
            #opDpt
            [placeholder]="'DPT'"
          ></ng-select>

        </th>
        <th style="vertical-align: middle">
          <span class="text-warning">En cours</span><br/>
          <span class="text-success">Terminés</span>
        </th>
        <th style="vertical-align: middle"></th>

        <th style="vertical-align: middle" *ngFor="let role of roles">
          <ng-select
            [items]="adminsSelect"
            [multiple]="true"
            [closeOnSelect]="false"
            [searchable]="true"
            [bindLabel]="'text'"
            [bindValue]="'id'"
            [groupBy]="'societe'"
            (change)="addFilter({key:'adminsIds'+role.id, type:'unknown', libelle: 'Admin lié', bindLabel: 'text', bindValue: 'id', map:'key'}, $event, 'in', getAdminsSelect)"
            (clear)="addFilter( {key:'adminsIds'+role.id, type:'unknown', libelle: 'Admin lié', bindLabel: 'text', bindValue: 'id'}, null, 'equal', getAdminsSelect)"
            #getAdminsSelect
            [placeholder]="role?.libelle"
          ></ng-select>
        </th>
        <th></th>
      </tr>

    </ng-template>

    <ng-template #bodyTop let-object let-orderWay="orderWay" let-orderField="orderField">
      <td >
        <div class="btn-group d-inline-flex justify-content-start">
          <button class="btn btn-sm btn-warning" [ngbTooltip]="'Cacher cette opération de la liste'"
                  mwlConfirmationPopover
                  *ngIf="object.isinadmin"
                  [popoverTitle]="'Cacher l\'opération de la liste'"
                  [popoverMessage]="'Êtes vous sur de vouloir cacher cette opération '+object.libelle+' ?'"
                  placement="right"
                  (confirm)="hide(object)"
          ><i class="fa fa-duotone fa-eye-slash"></i></button>

          <button class="btn btn-sm btn-success" [ngbTooltip]="'Remettre cette opération de la liste'"
                  mwlConfirmationPopover
                  *ngIf="!object.isinadmin"
                  [popoverTitle]="'Remettre l\'opération dans la liste'"
                  [popoverMessage]="'Êtes vous sur de vouloir remettre cette opération '+object.libelle+' ?'"
                  placement="right"
                  (confirm)="show(object)"
          ><i class="fa fa-duotone fa-eye"></i></button>

          <button class="btn btn-sm btn-info pull-right"
                  placement="right"
                  [ngbTooltip]="'Attribuer des admins à cette opération'"
                  (click)="openModaleAttribueAdminsOperation(object)"
          > <i class="ft-edit"></i>
          </button>
        </div>
      </td>
      <td>
        {{object?.cli}}
      </td>
      <td>
        {{object?.fond}}
      </td>
    </ng-template>
    <ng-template #bodyBottom let-object let-orderWay="orderWay" let-orderField="orderField">
      <td class="text-center">

        <ng-template #addr>
          {{object?.adresse}}<br/>
          {{object?.code}} {{object?.ville}}
        </ng-template>

        <strong style="color: #4472C4" [ngbPopover]="addr"
                placement="top"
                triggers="mouseenter:mouseleave"
                [popoverTitle]="'Adresse '+object?.libelle" >{{object?.libelle}}</strong>
      </td>
      <td>
        {{object?.dpt}}
      </td>
      <td><span class="text-warning">{{doStatAdminById[object?.id]?.nbSinistresEncours}}</span> - <span class="text-success">{{doStatAdminById[object?.id]?.nbSinistresTermines}}</span> / {{doStatAdminById[object?.id]?.nbSinistresEncours + doStatAdminById[object?.id]?.nbSinistresTermines}}</td>
      <td>{{(doStatAdminById[object?.id]?.nbContentieux) ? doStatAdminById[object?.id]?.nbContentieux : ''}}</td>
      <td *ngFor="let car of roles" (dblclick)="addRelation(object, car)" style="padding:0">
        <ng-container *ngFor="let relAdmin of object?.adminOperation|filter:{'admin_role_id':{value: car.id, op: '==' }}">
          <div [ngStyle]="{'background-color': relAdmin?.people?.color}" class="pL5 pR5 pT5 pB5">
            <div class="d-flex justify-content-between align-items-center">
              <div><people [showMediaPhoto]="true" [noWrap]="true" [showInitiales]="true" [showGroup]="false" [people]="relAdmin?.people"></people></div>
              <div class="align-items-center">

                <div class="btn-group">
                  <button type="button"
                          class="btn btn-icon btn-sm btn-xs btn-warning"
                          [ngbTooltip]="'Changement d\'admin'"
                          placement="left"
                          (click)="switchRelation(relAdmin)"
                  ><i class="fa-duotone fa-repeat"></i>
                  </button>
                  <button type="button" [ngbTooltip]="'Supprimer'"
                          class="btn btn-icon btn-sm  btn-xs btn-danger"
                          mwlConfirmationPopover
                          [popoverTitle]="'Suppression admin'"
                          [popoverMessage]="'Êtes vous sur de vouloir supprimer cet admin sur cette opération ?'"
                          placement="left"
                          (confirm)="deleteRelation(relAdmin)"
                  >
                    <i class="fa-duotone fa-trash-alt"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </ng-container>
      </td>
    </ng-template>

    <ng-template #headerTop>
      <th></th>
      <th>Client</th>
      <th>Fond</th>
    </ng-template>
    <ng-template #headerBottom>
      <th>Opérations</th>
<!--      <th>Ville</th>-->
      <th>Dpt</th>
      <th>Nb dossiers</th>
      <th>Ctx.</th>
      <th *ngFor="let car of roles">
        {{car?.libelle}}
      </th>
    </ng-template>
    <ng-template #actions let-object let-orderWay="orderWay" let-orderField="orderField">
<!--      <ng-container *ngIf="user|hasRight:'admin'">-->
<!--        <div class="btn-group" role="group">-->
<!--          <button [ngbTooltip]="'Modifier'" [routerLink]="['/eBusiness','operations', 'edit', object.id]" class="btn btn-icon btn-sm btn-success">-->
<!--            <i class="ft-edit-3"></i>-->
<!--          </button>-->
<!--          <button type="button" [ngbTooltip]="'Supprimer'"-->
<!--                  *ngIf="!(object.isgpa || object.isdo)"-->
<!--                  class="btn btn-icon btn-sm btn-danger"-->
<!--                  mwlConfirmationPopover-->
<!--                  [popoverTitle]="'Suppression de l\'opération Ebusiness'"-->
<!--                  [popoverMessage]="'Êtes vous sur de vouloir supprimer cette opération '+object.libelle+' ?'"-->
<!--                  placement="left"-->
<!--                  (confirm)="delete(object)"-->
<!--          >-->
<!--            <i class="fa-duotone fa-trash-alt"></i>-->
<!--          </button>-->
<!--        </div>-->
<!--      </ng-container>-->
    </ng-template>

    <ng-template #footerTop let-orderWay="orderWay" let-orderField="orderField"></ng-template>
    <ng-template #footerBottom let-orderWay="orderWay" let-orderField="orderField">
      <th>{{nbOP}}</th>
      <th>Dpt</th>
      <th>
        <span class="text-warning">{{nbEncours}}</span> - <span class="text-success">{{nbTermines}}</span> / {{nbEncours+nbTermines}}
      </th>
      <th>{{nbCTX}}</th>
      <th *ngFor="let car of roles">
        {{car?.libelle}}
      </th>
    </ng-template>

  </div>
</div>

