  <div class="sidebar-left sidebar-fixed ps">
<!--    <perfect-scrollbar>-->
    <div class="sidebar">
      <div class="sidebar-content card">

          <div class="card-body chat-fixed-search">
            <h5><i class="ft-users"></i> Liste des collaborateurs</h5>
<!--            <fieldset class="form-group position-relative has-icon-left m-0">-->
<!--              <input type="text" class="form-control" id="iconLeft4" placeholder="Search user">-->
<!--              <div class="form-control-position">-->
<!--                <i class="ft-search"></i>-->
<!--              </div>-->
<!--            </fieldset>-->
          </div>
          <div id="users-list" class="list-group position-relative">
            <div class="users-list-padding media-list">

<!--                 (click)="enterGlobalRoom()" -->
              <a href="javascript:void(0)"
                 [routerLink]="['/eBoarding','chat', 1]"
                 class="media border-0" [ngClass]="{'active': currentUserToChat == 1, 'border-0': currentUserToChat != 1}">
                <div class="media-left pr-1">
                  <span class="avatar avatar-50 avatar-online">
                      <span class="media-object rounded-circle text-circle bg-success">VGE</span>
                      <i></i>
                  </span>
                </div>
                <div class="media-body w-100">
                  <h6 class="list-group-item-heading">
                    Chat Global VGE
                  </h6>
                </div>
              </a>

              <ng-container *ngFor="let admin of admins">
<!--                   (click)="enterRoom(admin)"-->
<!--                   (click)="goChat(admin)"-->
                <a href="javascript:void(0)"
                   [routerLink]="['/eBoarding','chat', [user.id, admin.id].sort().join('')]"
                   class="media border-0" [ngClass]="{'active': admin && currentUserToChat == admin.id, 'border-0': admin && currentUserToChat != admin.id}"
                   *ngIf="admin && admin.id != user.id">
                  <div class="media-left pr-1">
                      <imageTable
                        *ngIf="admin?.image"
                        [resize]="true"
                        [value]="admin?.image"
                        [id]="admin?.id"
                        [class]="'avatar avatar-50'"
                        [width]="50"
                        [height]="50"
                        [preview]="false"
                        [rounded]="false"
                      ></imageTable>
                    <avatarText [class]="'avatar avatar-50'" [people]="admin" [online]="online[admin?.id] === true" *ngIf="!admin?.image"></avatarText>
                  </div>
                  <div class="media-body w-100">
                    <h6 class="list-group-item-heading"><people [reverseNoms]="true" [showSociete]="false" [people]="admin"></people>
<!--                      <span class="font-small-3 float-right primary">4:14 AM</span>-->
                    </h6>
                    <p class="list-group-item-text text-muted mb-0">
<!--                      <i class="fa-duotone fa-comment-o primary font-small-2"></i> Okay-->
<!--                      <span class="float-right primary"><i class="font-medium-1 fa-duotone fa-map-pin blue-grey lighten-3"></i></span>-->
<!--                      <span class="float-right primary"><span class="badge badge-pill badge-primary">12</span></span>-->
                    </p>
                  </div>
                </a>
              </ng-container>
            </div>
          </div>
      </div>
    </div>
<!--    </perfect-scrollbar>-->
  </div>
  <div class="content-right" *ngIf="currentRoom">
    <div class="content-wrapper">
      <div class="content-header row">
      </div>
      <div class="content-body">
        <div class="content-overlay"></div>
        <section class="chat-app-window"  #bOxWindOws [scrollTop]="bOxWindOws.scrollHeight">

<!--          <perfect-scrollbar>-->

<!--          {{messages|json}}-->

          <div class="sidebar-toggle d-block d-lg-none"><i class="ft-menu font-large-1"></i></div>
          <div class="badge badge-secondary mb-1">Chat History</div>
          <div class="chats">
            <div class="chats">

              <ng-container *ngIf="messages?.length > 0; else empty">

                <div class="chat" *ngFor="let message of messages" [ngClass]="{'chat-left': message.userId == user.id}">
                  <div class="chat-avatar media">
                    <imageTable
                      *ngIf="message?.createur?.image"
                      [resize]="true"
                      [value]="message?.createur?.image"
                      [id]="message?.createur?.id"
                      [class]="'avatar avatar-50'"
                      [width]="50"
                      [height]="50"
                      [preview]="false"
                      [rounded]="false"
                    ></imageTable>
                    <avatarText [class]="'avatar avatar-50'" [people]="message?.createur" [online]="online[message?.createur?.id] === true" *ngIf="!message?.createur?.image"></avatarText>
                  </div>
                  <div class="chat-body">
                    <div class="chat-content">

                      <ng-container *ngIf="message?.file && message?.fileObject else messageTPL">
                        <fileDownload [type]="'file'"
                                      [tooltip]="null"
                                      [class]="'btn btn-sm'"
                                      [lien]="message?.file"
                                      [icon]="filesTypesIconByMime[message?.fileObject?.type]"
                                      [label]="message?.fileObject?.name"></fileDownload><br/>
                      </ng-container>
                      <ng-template #messageTPL>
                        <p [innerHTML]="message?.message"></p>
                      </ng-template>

                      <time class="fs-11" [ngClass]="{'text-primary': message.userId == user.id, 'text-white': message.userId != user.id}">
                        <span *ngIf="message.userId == user.id">Vous</span>
                        <people *ngIf="message.userId != user.id" [reverseNoms]="true" [showSociete]="false" [people]="message?.createur"></people>
                        • {{moment.duration(moment(message?.created).utc(false).diff(moment().utc(true))).humanize(true)}}</time>

                    </div>
                  </div>
                </div>

              </ng-container>
              <ng-template #empty>
                <div class="alert alert-secondary">Aucun message avec <people [showSociete]="false" [people]="currentAdminSelected"></people> pour le moment</div>
              </ng-template>

            </div>
          </div>

<!--          </perfect-scrollbar>-->

        </section>
        <section class="chat-app-form">
          <div class="chat-app-input d-flex">
            <fieldset class="form-group position-relative has-icon-left col-2 m-0" *ngIf="ROLES.admin.indexOf(user?.profil?.name) > -1">
              <button type="button" class="btn btn-blue btn-block" [ngbTooltip]="'Exporter la conversation'" (click)="exportPDF()"><i class="fa-duotone fa-upload"></i> Exporter</button>
            </fieldset>
            <fieldset class="form-group position-relative has-icon-left col-10 m-0" [ngClass]="{'col-10': ROLES.admin.indexOf(user?.profil?.name) == -1, 'col-8': ROLES.admin.indexOf(user?.profil?.name) > -1}">
              <div class="form-control-position" data-toggle="dropdown">
                <i class="fa-duotone fa-smile"></i>
              </div>
              <emoji-mart
                class="dropdown-menu"
                [darkMode]="false"
                [i18n]="{ search: 'Recherche', categories: { search: 'Résultats de recherche', recent: 'Récents' } }"
                (emojiClick)="addEmoji($event)"></emoji-mart>

              <input [(ngModel)]="newMessage" id="btn-input-2" type="text" class="form-control message" placeholder="Ecrivez votre message ici..." (keyup)="$event.keyCode == 13 && sendMessageRoom()" />

              <div class="form-control-position control-position-right" (click)="hiddenInput.click()">
                <i class="fa-duotone fa-upload"></i>
              </div>
              <input type="file" class="d-none" #hiddenInput ng2FileSelect [uploader]="uploader" />
              <input #realInput type="hidden"/>

            </fieldset>


            <fieldset class="form-group position-relative has-icon-left col-2 m-0">
              <button type="button" class="btn btn-secondary btn-block send" (click)="sendMessageRoom()"><i class="fa-duotone fa-paper-plane"></i> Envoyer</button>
            </fieldset>
          </div>
        </section>
      </div>
    </div>
  </div>
