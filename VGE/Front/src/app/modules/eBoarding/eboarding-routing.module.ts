import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

/* Guards */
import { AuthGuard } from '../../common/guards/auth.guard';
import { AuthGuardAdmin } from "../../common/guards/auth.guard.admin";
import {ChatPage} from "./pages/chat/chat.page";
import {ChatPublicPage} from "./pages/chatPublic/chatPublic.page";
import {HomePage} from "./pages/home/<USER>";
import {AlerteDelaisPage} from "./pages/alerteDelais/alerteDelais.page";
import {EventsPage} from "./pages/events/events.page";
import {RepartitionAdminPage} from "./pages/repartitionAdmin/repartitionAdmin.page";
import {RepartitionOperationPage} from "./pages/repartitionOperations/repartitionOperation.page";
import {AuthGuardJ30} from "../../common/guards/auth.guard.j30";
import {EventsClientPage} from "./pages/eventsClient/eventsClient.page";

/* Pages  */

const routes: Routes = [
  {
    path: '',
    redirectTo: 'societes',
    pathMatch: 'full'
  },
  {
    path: '',
    children: [
      {path: 'home', component:HomePage, canActivate:[AuthGuardAdmin, AuthGuardJ30]},
      {path: 'chat', component:ChatPage, canActivate:[AuthGuardAdmin, AuthGuardJ30]},
      {path: 'chat/:id', component:ChatPage, canActivate:[AuthGuardAdmin, AuthGuardJ30]},
      {path: 'chat-public', component:ChatPublicPage, canActivate:[AuthGuardAdmin, AuthGuardJ30]},
      {path: 'chat-public/:id', component:ChatPublicPage, canActivate:[AuthGuardAdmin, AuthGuardJ30]},
      {path: 'events', component:EventsPage, canActivate:[AuthGuardAdmin, AuthGuardJ30]},

      {path: 'eventsClient', component:EventsClientPage, canActivate:[AuthGuardAdmin, AuthGuardJ30]},

      {path: 'events/:id', component:EventsPage, canActivate:[AuthGuardAdmin, AuthGuardJ30]},
      {path: 'alerte-delais', component:AlerteDelaisPage, canActivate:[AuthGuardAdmin, AuthGuardJ30]},

      {path: 'repartition-gestionnaire', component:RepartitionAdminPage, canActivate:[AuthGuardAdmin, AuthGuardJ30]},
      {path: 'repartition-operations', component:RepartitionOperationPage, canActivate:[AuthGuardAdmin, AuthGuardJ30]},

    ]
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class EboardingRoutingModule { }
