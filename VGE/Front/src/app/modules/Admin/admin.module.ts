import { NgModule } from '@angular/core';
import { AdminRoutingModule } from './admin-routing.module';

/* Layouts */

import {SocieteEditPage} from "./pages/societe.edit/societe.edit.page";

import {SharedModule} from "../shared/shared.module";
import {UtilisateurEditPage} from "./pages/utilisateur.edit/utilisateur.edit.page";
import {SocietesListPage} from "./pages/societes.list/societes.list.page";
import {DocsgenerauxListPage} from "./pages/docsgeneraux.list/docsgeneraux.list.page";
import {UtilisateursListPage} from "./pages/utilisateurs.list/utilisateurs.list.page";
import {DocsgenerauxEditPage} from "./pages/docsgeneraux.edit/docsgeneraux.edit.page";
import {OperationsCorporateListPage} from "./pages/operations.list/operations.list.page";
import {OperationCorporatePage} from "./pages/operation/operation.page";
// import {AgmCoreModule} from "@agm/core";
import {TestsPage} from "./pages/tests/tests.page";
import {UniversignModule} from "../universign/universign.module";

@NgModule({

  declarations: [
    SocietesListPage,
    SocieteEditPage,

    DocsgenerauxListPage,
    DocsgenerauxEditPage,

    UtilisateursListPage,
    UtilisateurEditPage,

    OperationsCorporateListPage,
    OperationCorporatePage,

    TestsPage,


  ],
  imports: [
    SharedModule,
    UniversignModule,
    AdminRoutingModule,
    // AgmCoreModule.forRoot({
    //   apiKey: "AIzaSyBO94JL_cr39QLxZA81mcOtr5pBmZUUHTA"
    // }),
  ],
  exports: []
})
export class AdminModule{}
