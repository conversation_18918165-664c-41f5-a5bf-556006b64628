import {Component, OnInit} from "@angular/core";
import {ActivatedRoute, Router} from "@angular/router";
import {ToastrService} from "ngx-toastr";
import {LoopBackAuth} from "../../../../shared/sdk/services/core";
import {People<PERSON>pi, SocietesApi} from "../../../../shared/sdk/services/custom";
import {People, Societes} from "../../../../shared/sdk/models";
import {Observable, of, forkJoin} from "rxjs";
import {catchError, map, share} from "rxjs/operators";
import {TableColumnInterface} from "../../../shared/interfaces/table.column.interface";
import {AppGlobalEventManagerService} from "../../../../common/services/app.globalEventManager.service";
import {FormBuilder, FormGroup, Validators} from "@angular/forms";
import {AppCacheService} from "../../../../common/services/app.cache.service";
import {EditPageInterface} from "../../../shared/interfaces/edit.page.interface";
import {apiParameterInterface} from "../../../shared/interfaces/apiParameter.interface";
import {AppSessionService} from "../../../../common/services/app.session.service";
import {PeopleWithrightsInterface} from "../../../../common/interfaces/peopleWithrights.interface";
import {data} from "jquery";


@Component({
  selector: 'utilisateurEdit',
  templateUrl: './utilisateur.edit.page.html'
})
export class UtilisateurEditPage implements OnInit, EditPageInterface {

  public utilisateur$: Observable<any>;
  public columns: Array<TableColumnInterface>;
  public hiddenColumns;
  public form: FormGroup;
  public id;
  public object = null;
  public modelDef;
  public apiParameters: apiParameterInterface;

  public user: PeopleWithrightsInterface;

  public folder = 'utilisateurs'; // For Uploads !
  public mode = null;

  /* Custom */
  public allsocietes: Array<{key:string, text:string}> = [];
  public societesIdsLinked: string[] = [];

  public allProfils: Array<{key:string, text:string}> = [];

  constructor(
    public router: Router,
    public route: ActivatedRoute,
    public _sessionService: AppSessionService,
    public formBuilder: FormBuilder,
    public _toastr: ToastrService,
    public authService: LoopBackAuth,
    public utilisateursApiService: PeopleApi,
    public societeApiService: SocietesApi,
    public appGlobalEventManagerService: AppGlobalEventManagerService,
    public appCacheService: AppCacheService,
  ) {

    let self = this;

    this._sessionService.logguedAccountEmitter.subscribe((people) => {
      this.user = people;
    });

    this.appGlobalEventManagerService.getApiParameters().subscribe((parameters:apiParameterInterface) => {
       this.apiParameters = parameters
    });

    this.form = this.formBuilder.group({
      firstname: ['', [Validators.required]],
      lastname: ['', [Validators.required]],
      email: ['', [Validators.required, Validators.email]],
      address: ['', []],
      zip: ['', []],
      city: ['', []],
      phone: ['', []],
      profilid: ['', []],
      signature:['', []],

      signatureVGE:['', []],
      signatureVGI:['', []],
      signatureVGS:['', []],

      isVGE: ['', []],
      isVGI: ['', []],
      isVGS: ['', []],

      image:['',[]],

      color: ['',[]],
      societesUser: ['']
    });

    this.modelDef = People.getModelDefinition();

  }

  saveObject(datas:object){

    let self = this;
    let societes:Array<any> = datas['societesUser'] // Utilisateurs Liés

    /* Delete from User element */
    delete datas['societesUser']; // Custom

    if(this.mode == 'edit'){

      if(!datas['profilid']){
        datas['profilid'] = this.object.profilid;
      }

      this.utilisateursApiService.patchAttributes(this.id, datas).pipe(
        map((res) => res),
        catchError((err) => {return of(err)})
      ).subscribe((res) => {
        if(res && !res.message){

          let id = res.id;

          if(societes && societes.length > 0){

            self.utilisateursApiService.deleteSocietes(id).subscribe((del) => {

              // Custom, others relations !
              let forkSubscribes = [];
              societes.forEach((e) => {
                forkSubscribes.push(self.utilisateursApiService.linkSocietes(id, e))
              });

              forkJoin(forkSubscribes).subscribe((r) => {
                this._toastr.success('Succès !', 'Edition de l\'utilisateur')
                this.router.navigate(['/Admin','utilisateurs']);
              });

            })
          }else{
            this._toastr.success('Succès !', 'Edition de l\'utilisateur')
            this.router.navigate(['/Admin','utilisateurs']);
          }

        }else{
          this._toastr.error(res.message, 'Edition de l\'utilisateur');
        }
      });
    }else{
      /* Create */

      datas['cptcreate'] = 1;
      datas['rappel'] = 1; // valeur obligatoire !

      this.utilisateursApiService.create(datas).pipe(
        map((res) => res),
        catchError((err) => {return of(err)})
      ).subscribe((res) => {
        if(res && !res.message){

          if(societes){

            // Custom, others relations !
            let id = res.id;

            let forkSubscribes = [];
            societes.forEach((e) => {
              forkSubscribes.push(self.utilisateursApiService.linkSocietes(id, e))
            });

            forkJoin(forkSubscribes).subscribe((r) => {
              this._toastr.success('Succès !', 'Création de l\'utilisateur')
              this.router.navigate(['/Admin','utilisateurs']);
            });

          }else{
            this._toastr.success('Succès !', 'Création de l\'utilisateur')
            this.router.navigate(['/Admin','utilisateurs']);
          }

        }else{
          this._toastr.error(res.message, 'Création de l\'utilisateur');
        }
      });
    }
  }

  getParameter(){
    this.route.paramMap.subscribe(params => {
      this.id = params.get("id");
      if(this.id === 'new'){
        this.mode = 'create';
        this.utilisateur$ = of(null);
      }else{
        this.mode = 'edit';
        this.utilisateur$ = this.utilisateursApiService.findById(this.id).pipe(share());
      }
    })
  }

  ngOnInit(): void {

    let self = this;

    this.getParameter();

    this.utilisateur$.subscribe((utilisateur:People) => {
      if(utilisateur){
        this.object = utilisateur;

        this.appCacheService.getSocietes().subscribe((allSocietes) => {
          /* Associate key value to society */
          allSocietes.forEach(el => {
            self.allsocietes.push({key:el.id, text:el.nom});
          });

          if(this.object.profilid == 3){
            this.columns =
              [
                {type:"separator", name:"Utilisateur", icon:"fa-duotone fa-user-tie", exclude:true},

                {key: "firstname",name: "Nom", type: "input"},
                {key: "lastname",name: "Prénom", type: "input"},
                {key: "email",name: "Email", type: "input", typeInput:"email", icon:'fa-duotone fa-enveloppe'},
                {key: "address",name: "Adresse", type: "input"},
                {key: "zip",name: "Code postal", type: "input"},
                {key: "city",name: "Ville", type: "input"},
                {key: "phone",name: "Télephone", type: "input"},
                {key: "image",name: "Photo de profil", type: "imageResize", imageOptions:{maintainAspectRatio: true, showimageActuelle: true}},
                {key: "color",name: "Couleur", type: "color"},

                {type:"separator", name:"Sociétés", icon:"fa-duotone fa-building", exclude:true},
                {key: "societesUser",name: "Sociétés", type: "select", selectOptions:{multiple:true, options:self.allsocietes, keyBind:'key', labelBind:'text'}},

                {type:"separator", name:"Signatures", icon:"fa-duotone fa-sign", exclude:true},
                {key: "signature",name: "Signature par défaut", type: "text"},

                {key: "signatureVGE",name: "Signature VGE", type: "text"},
                {key: "signatureVGI",name: "Signature VGI", type: "text"},
                {key: "signatureVGS",name: "Signature VGS", type: "text"},

                {type:"separator", name:"Modules", icon:"fa-duotone fa-access", exclude:true},

                {key: "isVGE",name: "Accès module VGE", type: "boolean"},
                {key: "isVGI",name: "Accès module VGI", type: "boolean"},
                {key: "isVGS",name: "Accès module VGS", type: "boolean"},

              ];

          }else if([2,6].includes(this.object.profilid)){

            let optionsProfils = [];
            if(this.user.cancreateadmins || [3].indexOf(this.user.profilid) > -1 || [314, 4, 1, 473].indexOf(this.user.id) > -1){
              optionsProfils = [{key: 2, text:'Admin'}, {key: 6, text:'Admin Client'}, {key: 4, text:'Utilisateur eGestion'}, {key:1, text:'Membre sans accès'}]
            } else {
              optionsProfils = [{key: 4, text:'Utilisateur eGestion'}, {key:1, text:'Membre sans accès'}]
            }

            this.columns =
              [
                {type:"separator", name:"Utilisateur", icon:"fa-duotone fa-user-tie", exclude:true},

                {key: "firstname",name: "Nom", type: "input"},
                {key: "lastname",name: "Prénom", type: "input"},
                {key: "email",name: "Email", type: "input", typeInput:"email", icon:'fa-duotone fa-enveloppe'},
                {key: "address",name: "Adresse", type: "input"},
                {key: "zip",name: "Code postal", type: "input"},
                {key: "city",name: "Ville", type: "input"},
                {key: "phone",name: "Télephone", type: "input"},

              ]

              if([3].indexOf(this.user.profilid) > -1 || ([2].indexOf(this.user.profilid) > -1 && this.user.cancreateadmins)){
                this.columns.push({key: "profilid",name: "Profil", type: "select", selectOptions:{keyBind: 'key', labelBind: 'text', multiple: false,options: optionsProfils}},)
              }

              this.columns.push(...[

                  {key: "image",name: "Photo de profil", type: "imageResize", imageOptions:{maintainAspectRatio: true, showimageActuelle: true}},
                  {key: "color",name: "Couleur", type: "color"},

                  {type:"separator", name:"Sociétés", icon:"fa-duotone fa-building", exclude:true},
                  {key: "societesUser",name: "Sociétés", type: "select", selectOptions:{multiple:true, options:self.allsocietes, keyBind:'key', labelBind:'text'}},

                  {type:"separator", name:"Signatures", icon:"fa-duotone fa-sign", exclude:true},
                  {key: "signature",name: "Signature par défaut", type: "text"},

                ]
              )

            if(this.object.isVGE){
              this.columns.push({key: "signatureVGE",name: "Signature VGE", type: "text"});
            }

            if(this.object.isVGI){
              this.columns.push({key: "signatureVGI",name: "Signature VGI", type: "text"});
            }

            if(this.object.isVGS){
              this.columns.push({key: "signatureVGS",name: "Signature VGS", type: "text"});
            }

            if([3].indexOf(this.user.profilid) > -1 || ([2].indexOf(this.user.profilid) > -1 && this.user.cancreateadmins)){
              this.columns.push({type:"separator", name:"Modules", icon:"fa-duotone fa-key-skeleton-left-right", exclude:true})
              this.columns.push({key: "isVGE",name: "Accès module VGE", type: "boolean"})
              this.columns.push({key: "isVGI",name: "Accès module VGI", type: "boolean"})
              this.columns.push({key: "isVGS",name: "Accès module VGS", type: "boolean"})
            }


          } else {

            let optionsProfils = [];
            if(this.user.cancreateadmins || [3].indexOf(this.user.profilid) > -1 || [314, 4, 1, 473].indexOf(this.user.id) > -1){
              optionsProfils = [{key: 2, text:'Admin'}, {key: 6, text:'Admin Client'}, {key: 4, text:'Utilisateur eGestion'}, {key:1, text:'Membre sans accès'}]
            } else {
              optionsProfils = [{key: 4, text:'Utilisateur eGestion'}, {key:1, text:'Membre sans accès'}]
            }

            this.columns =
              [
                {type:"separator", name:"Utilisateur", icon:"fa-duotone fa-user-tie", exclude:true},

                {key: "firstname",name: "Nom", type: "input"},
                {key: "lastname",name: "Prénom", type: "input"},
                {key: "email",name: "Email", type: "input", typeInput:"email", icon:'fa-duotone fa-enveloppe'},
                {key: "address",name: "Adresse", type: "input"},
                {key: "zip",name: "Code postal", type: "input"},
                {key: "city",name: "Ville", type: "input"},
                {key: "phone",name: "Télephone", type: "input"},

                // {key: "profilid",name: "Profil", type: "select", selectOptions:{keyBind: 'key', labelBind: 'text', multiple: false,options: optionsProfils}},

                {key: "image",name: "Photo de profil", type: "imageResize", imageOptions:{maintainAspectRatio: true, showimageActuelle: true}},
                {key: "color",name: "Couleur", type: "color"},

                {type:"separator", name:"Sociétés", icon:"fa-duotone fa-building", exclude:true},
                {key: "societesUser",name: "Sociétés", type: "select", selectOptions:{multiple:true, options:self.allsocietes, keyBind:'key', labelBind:'text'}},

              ];

          }

          /* Custom */
          this.object.societes.forEach((el) => {
            this.societesIdsLinked.push(el.id); // String | number ! même type requis
          });

          /** Set Values **/
          this.columns.forEach((el) => {
            if(el.type != 'separator' && (self.object[el.key] || self.object[el.key] === 0)){
              self.form.controls[el.key].setValue(self.object[el.key]);
            }
          });
          self.form.controls['societesUser'].setValue(self.societesIdsLinked); // Custom

        });


        this.appGlobalEventManagerService.updateTitle('Edition utilisateur '+this.object.firstname+' '+this.object.lastname);
        this.appGlobalEventManagerService.updateBreadcrumb([{title:'Home', url:'/'}, {title:'Liste des utilisateurs', url:'/Admin/utilisateurs'}, {title:'Edition utilisateur '+this.object.firstname+' '+this.object.lastname, url:'/Admin/utilisateurs/edit/'+this.id }])
      }else{

        this.appCacheService.getSocietes().subscribe((allSocietes) => {
          /* Associate key value to society */
          allSocietes.forEach(el => {
            self.allsocietes.push({key: el.id, text: el.nom});
          });


          let optionsProfils = [];
          if(this.user.cancreateadmins || [3].indexOf(this.user.profilid) > -1 || [314, 4, 1, 473].indexOf(this.user.id) > -1){
            optionsProfils = [{key: 2, text:'Admin'},{key: 6, text:'Admin Client'}, {key: 4, text:'Utilisateur eGestion'}, {key:1, text:'Membre sans accès'}]
          } else {
            optionsProfils = [{key: 4, text:'Utilisateur eGestion'}, {key:1, text:'Membre sans accès'}]
          }

          this.columns =
            [
              {type: "separator", name: "Utilisateur", icon: "fa-duotone fa-user-tie", exclude: true},

              {key: "firstname", name: "Nom", type: "input"},
              {key: "lastname", name: "Prénom", type: "input"},
              {key: "email", name: "Email", type: "input", typeInput: "email", icon: 'fa-duotone fa-enveloppe'},
              {key: "address", name: "Adresse", type: "input"},
              {key: "zip", name: "Code postal", type: "input"},
              {key: "city", name: "Ville", type: "input"},
              {key: "phone", name: "Télephone", type: "input"},
              {
                key: "profilid",
                name: "Profil",
                type: "select",
                selectOptions: {
                  keyBind: 'key',
                  labelBind: 'text',
                  multiple: false,
                  options: optionsProfils
                }
              },
              {key: "signature", name: "Signature", type: "text"},
              {type: "separator", name: "Sociétés", icon: "fa-duotone fa-building", exclude: true},
              {
                key: "societesUser",
                name: "Sociétés",
                type: "select",
                selectOptions: {multiple: true, options: self.allsocietes, keyBind: 'key', labelBind: 'text'}
              },

            ];

        });


        this.appGlobalEventManagerService.updateTitle('Création utilisateur');
        this.appGlobalEventManagerService.updateBreadcrumb([{title:'Home', url:'/'}, {title:'Liste des utilisateurs', url:'/Admin/utilisateurs'}, {title:'Création utilisateur ', url:'/Admin/utilisateurs/edit/' }])
      }
    });

  }

}
