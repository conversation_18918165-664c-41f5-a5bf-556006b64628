<!-- Buttons TOP -->
<div class="row">
  <div class="col-12 mb-1 text-right">
    <button class="btn btn-info btn-social btn-sm mL5"
            mwlConfirmationPopover
            [popoverTitle]="'Batch de mise à jour'"
            [popoverMessage]="'Êtes vous sur de vouloir mettre à jour les admins dans les opérations DOnet ?'"
            placement="left"
            (confirm)="majAdminsOperations()"

    ><i class="ft-activity"></i> Batch Mise à jour des administrateurs dans les opérations DOnet</button>
    <button class="btn btn-success btn-social btn-sm mL5" [routerLink]="['/Admin', 'utilisateurs', 'edit', 'new']"><i class="ft-plus-square"></i> Ajouter un utilisateur</button>
  </div>
</div>
<!-- / Buttons TOP -->

<div class="content-detached content-left">


  <div class="content-body">

    <selfTablePaginate
      [data]="utilisateurs$"
      [columns]="columns"
      [defaultKeyColumn]="'firstname'"
      [idKey]="'id'"

      [bodyTopTemplate]="bodyTop"
      [bodyBottomTemplate]="bodyBottom"
      [headerTopTemplate]="headerTop"
      [headerBottomTemplate]="headerBottom"
      [footerTopTemplate]="footerTop"
      [footerBottomTemplate]="footerBottom"
      [actionsTemplate]="actions"
      #list
      list="utilisateurs">
    </selfTablePaginate>

    <ng-template #actions let-object let-orderWay="orderWay" let-orderField="orderField">
      <div class="btn-group" role="group" aria-label="Basic example">
        <button type="button" [ngbTooltip]="'Modifier'" class="btn btn-icon btn-sm btn-success" [routerLink]="['/Admin','utilisateurs', 'edit', object.id]"><i class="ft-edit-3"></i></button>
        <button type="button" [ngbTooltip]="'Options de compte'" class="btn btn-icon btn-sm btn-info" (click)="utilisateur = object"><i class="ft-settings"></i></button>
        <button type="button" [ngbTooltip]="'Supprimer'"
                *ngIf="user.accessEbuiz === 1"
                class="btn btn-icon btn-sm btn-danger"
                mwlConfirmationPopover
                [popoverTitle]="'Suppression utilisateur'"
                [popoverMessage]="'Êtes vous sur de vouloir supprimer '+object.firstname+' '+object.lastname+' ?'"
                placement="left"
                (confirm)="delete(object)"
        >
          <i class="fa-duotone fa-trash-alt"></i>
        </button>
      </div>

    </ng-template>

    <ng-template #bodyTop let-object let-orderWay="orderWay" let-orderField="orderField">
      <td width="25%">
        <div class="media" style="cursor: pointer" (click)="utilisateur = object">
          <a class="media-left" href="javascript:void(0)" style="display: inline-block; width: 50px; margin-right: 5px">
            <imageTable
              *ngIf="object.image"
              [resize]="true"
              [id]="object.id"
              [class]="'avatar-100'"
              [value]="object.image"
              [width]="40"
              [height]="40"
              [preview]="true"
              [rounded]="true"
            >
            </imageTable>
            <avatarText [class]="'avatar avatar-md'" [people]="object" [online]="null" *ngIf="!object.image"></avatarText>
          </a>
          <div class="media-body d-flex align-self-center">
            <h5 class="media-heading">{{object.firstname}} {{object.lastname}} (<small *ngFor="let societe of object.societes">{{societe.nom}} </small>)
              <small class="text-black-50"><br/>{{object?.profil?.name}}</small>
              <ng-container *ngIf="object.profilid === 1"><br/><small class="text-danger">Cet utilsateur ne pourra pas se connecter, veuillez lui attribuer le profil "membre eGestion"</small></ng-container>
            </h5>
          </div>
        </div>
      </td>
    </ng-template>

    <ng-template #bodyBottom let-object let-orderWay="orderWay" let-orderField="orderField">
      <td>
        Créé : {{object.createdate|momentDateFormat:'YYYY-MM-DD HH:mm:ss':'DD/MM/YYYY'}}<br/>
        Accès : {{object.lastAccess|momentDateFormat:'YYYY-MM-DD HH:mm:ss':'LLLL'}}
      </td>
      <td>
        <span class="badge badge-info" *ngIf="object.isInProcessChangePWD">Modif de mot de passe</span>
        <span class="badge badge-success" *ngIf="!object.isInProcessChangePWD && object.isComplete">Complet</span>
        <span class="badge badge-warning" *ngIf="!object.isInProcessChangePWD && !object.isComplete">Incomplet</span>
      </td>
      <td>
        <span class="badge badge-success" *ngIf="object.active">Activé</span>
        <span class="badge badge-danger" *ngIf="!object.active">Désactivé</span>
      </td>
      <td>
        <span class="badge badge-success" *ngIf="object.emailVerified">Email Vérifié</span>
        <span class="badge badge-danger" *ngIf="!object.emailVerified">Email Non vérifié (login impossible)</span>
      </td>

      <td>
        <ng-container *ngIf="[2,3,6].includes(object.profilid)">
          <span class="badge badge-success" *ngIf="object.isVGE">VGE</span>
          <span class="badge badge-danger" *ngIf="!object.isVGE">VGE</span>
        </ng-container>
      </td>

      <td>
        <ng-container *ngIf="[2,3,6].includes(object.profilid)">
          <span class="badge badge-success" *ngIf="object.isVGI">VGI</span>
          <span class="badge badge-danger" *ngIf="!object.isVGI">VGI</span>
        </ng-container>
      </td>

      <td>
        <ng-container *ngIf="[2,3,6].includes(object.profilid)">
          <span class="badge badge-success" *ngIf="object.isVGS">VGS</span>
          <span class="badge badge-danger" *ngIf="!object.isVGS">VGS</span>
        </ng-container>
      </td>

      <td>
        <div class="media" *ngFor="let societe of object.societes">
          <a class="media-left" href="javascript:void(0)" style="display: inline-block; width: 50px; margin-right: 5px" *ngIf="societe.image">
            <imageTable
              [resize]="true"
              [value]="societe.image"
              [width]="50"
              [height]="50"
              [preview]="false"
            >
            </imageTable>
          </a>
          <div class="media-body d-flex align-self-center">
            <span class="">{{societe.nom}}</span>
          </div>
        </div>
      </td>
    </ng-template>

    <ng-template #headerTop>
      <th>Nom - prénom</th>
    </ng-template>
    <ng-template #headerBottom let-object>
      <th>Dates</th>
      <th>Etat du compte</th>
      <th>Statut du compte</th>
      <th>Email vérifié</th>

      <th [ngbTooltip]="'Est admin VGE'" container="body">VGE</th>
      <th [ngbTooltip]="'Est admin VGI'" container="body">VGI</th>
      <th [ngbTooltip]="'Est admin VGS'" container="body">VGS</th>

      <th>Société</th>
    </ng-template>


    <ng-template #footerTop let-orderWay="orderWay" let-orderField="orderField" let-filter="filter">
      <th></th>
    </ng-template>
    <ng-template #footerBottom let-orderWay="orderWay" let-orderField="orderField" let-object>
      <th></th>
      <th></th>
      <th></th>
      <th></th>

      <th></th>
      <th></th>
      <th></th>

      <th></th>
    </ng-template>
  </div>
</div>

<div class="sidebar-detached sidebar-right">
  <div class="sidebar">
    <div class="project-sidebar-content">
      <div class="card">
        <div class="card-header">
          <h4 class="card-title">Rechercher</h4>
          <a class="heading-elements-toggle"><i class="fa-duotone fa-ellipsis-v font-medium-3"></i></a>
        </div>
        <div class="card-content collapse show">
          <!-- project search -->
          <div class="card-body border-top-blue-grey border-top-lighten-5">
            <div class="project-search">
              <div class="project-search-content">
                <form action="#">

                  <ng-container *ngFor="let column of columns">
                    <ng-container *ngIf="column.filter === true">
                      <div class="position-relative">
                        <input (change)="filter(column.key, $event)" type="search" class="form-control" placeholder="Recherche par {{column.name}}" />
                        <div class="form-control-position">
                          <i class="fa-duotone fa-search text-size-base text-muted"></i>
                        </div>
                      </div>
                      <hr/>
                    </ng-container>
                  </ng-container>

                  <div class="position-relative">
                    <input (change)="filter('firstname', $event)" type="search" class="form-control" placeholder="Recherche par Nom" />
                    <div class="form-control-position">
                      <i class="fa-duotone fa-search text-size-base text-muted"></i>
                    </div>
                  </div>
                  <hr/>
                  <div class="position-relative">
                    <ng-select
                      [bindLabel]="'text'"
                      [bindValue]="'key'"
                      [items]=" [{key: 2, text:'Admin'}, {key: 4, text:'Utilisateur eGestion'}, {key:1, text:'Membre sans accès'}]"
                      [placeholder]="'Profil'"
                      (change)="$event['target'] = {}; $event['target']['value'] = $event['key']; filter('profilid', $event)"
                    ></ng-select>
                  </div>
                </form>
              </div>
            </div>
          </div>
          <!-- /project search -->
        </div>
      </div>


      <div class="card" *ngIf="utilisateur">
        <div class="card-header">
          <h4 class="card-title">Options utilisateur</h4>
          <a class="heading-elements-toggle"><i class="fa-duotone fa-ellipsis-v font-medium-3"></i></a>
          <div class="heading-elements">
            <ul class="list-inline mb-0">
              <li><a data-action="close" (click)="utilisateur = null;"><i class="ft-x"></i></a></li>
            </ul>
          </div>
        </div>
        <div class="card-content collapse show">
          <div class="card-body">

            <div class="media mB20">

              <imageTable
                *ngIf="utilisateur.image"
                [resize]="true"
                [id]="utilisateur.id"
                [class]="'avatar-100'"
                [value]="utilisateur.image"
                [width]="50"
                [height]="50"
                [preview]="false"
                [rounded]="true"
              >
              </imageTable>
              <avatarText [class]="'avatar avatar-md'" [people]="utilisateur" [online]="null" *ngIf="!utilisateur.image"></avatarText>

<!--              <a class="media-left" href="javascript:void(0)" style="display: inline-block; width: 50px">-->
<!--                <img *ngIf="utilisateur?.societes && utilisateur?.societes[0] && utilisateur?.societes[0]?.image" class="media-object rounded-circle" [src]="utilisateur.societes[0].image" alt="-" style="max-width: 50px;max-height: 50px;">-->
<!--              </a>-->
              <div class="media-body pL10">
                <h4 class="media-heading">{{utilisateur.firstname}} {{utilisateur.lastname}}<br/>(<small *ngFor="let societe of utilisateur.societes">{{societe.nom}} </small>)</h4>
                <span class="text-black-50">{{utilisateur.profil.name}}</span>
              </div>
            </div>


            <div class="position-relative">
              <button *ngIf="utilisateur.active"
                      (click)="setUSerOff(utilisateur)"
                      class="btn btn-danger btn-social btn-block mB5"
                      [ngbTooltip]="'Désactiver l\'utilisateur '+utilisateur.firstname + ' '+utilisateur.lastname+''">
                <i class="fa-duotone fa-power-off"></i> Désactiver
              </button>
            </div>

            <div class="position-relative">
              <button *ngIf="!utilisateur.active"
                      (click)="setUSerOn(utilisateur)"
                      class="btn btn-info btn-social btn-block mB5"
                      [ngbTooltip]="'Activer l\'utilisateur '+utilisateur.firstname + ' '+utilisateur.lastname+''">
                <i class="fa-duotone fa-check"></i> Activer
              </button>
            </div>



<!--            <div class="position-relative">-->
<!--              <button *ngIf="!utilisateur.active && !utilisateur.password"-->
<!--                 class="btn btn-primary btn-social btn-block mB5"-->
<!--                 [ngbTooltip]="'Activer l\'utilisateur '+utilisateur.firstname + ' '+utilisateur.lastname+' et définir un mot de passe'"-->
<!--              >-->
<!--                <i class="fa-duotone fa-key"></i> Activer-->
<!--              </button>-->
<!--            </div>-->

<!--            <div class="position-relative">-->
<!--              <button *ngIf="!utilisateur.active && utilisateur.password"-->
<!--                 class="btn btn-primary btn-social btn-block mB5"-->
<!--                 [ngbTooltip]="'Activer l\'utilisateur '+utilisateur.firstname + ' '+utilisateur.lastname"-->
<!--              >-->
<!--                <i class="fa-duotone fa-key"></i>-->
<!--              </button>-->
<!--            </div>-->

<!--            <div class="position-relative">-->
<!--              <button *ngIf="utilisateur.active && utilisateur.password"-->
<!--                 class="btn btn-danger btn-social btn-block mB5"-->
<!--                 [ngbTooltip]="'Désactiver l\'utilisateur '+utilisateur.firstname + ' '+utilisateur.lastname">-->
<!--                <i class="fa-duotone fa-key"></i> Désactiver-->
<!--              </button>-->
<!--            </div>-->

            <div class="position-relative">
              <button
                 class="btn btn-primary btn-social btn-block mB5"
                 [popoverMessage]="'Voulez-vous changer le mot de passe automatisé pour '+utilisateur.firstname+' '+utilisateur.lastname"
                 [popoverTitle]="'Question'"
                 placement="left"
                 (confirm)="changePasswordtoUser(utilisateur)"
                 mwlConfirmationPopover
                 [ngbTooltip]="'Changer le mot de passe automatisé pour '+utilisateur.firstname + ' '+utilisateur.lastname">
                <i class="fa-duotone fa-envelope"></i> Changement mot de passe
              </button>


              <button *ngIf="!utilisateur.emailVerified"
                        (click)="setUSerEmailVerified(utilisateur)"
                        class="btn btn-info btn-social btn-block mB5"
                        [ngbTooltip]="'Définir en Email vérifié l\'utilisateur '+utilisateur.firstname + ' '+utilisateur.lastname+''">
                  <i class="fa-duotone fa-envelope"></i> Définir en email Vérifié
                </button>
            </div>

<!--      TODO ajouter un BTN pour activer la personne en tant qu'admin !      -->

<!--            <div class="position-relative">-->
<!--              <button-->
<!--                 class="btn btn-outline-primary btn-social btn-block mB5"-->
<!--                 [popoverMessage]="'Envoyer un mail pour que l\'utilisateur '+utilisateur.firstname+' '+utilisateur.lastname+' change son mot de passe ?'"-->
<!--                 [popoverTitle]="'Question'"-->
<!--                 placement="left"-->
<!--                 (confirm)="delete(utilisateur)"-->
<!--                 mwlConfirmationPopover-->
<!--                 [ngbTooltip]="'Envoyer un mail pour que l\'utilisateur '+utilisateur.firstname + ' '+utilisateur.lastname+' change son mot de passe'"-->
<!--              >-->
<!--                <i class="fa-duotone fa-envelope"></i> Envoyer mail-->
<!--              </button>-->
<!--            </div>-->

          </div>
        </div>
      </div>




    </div>
  </div>
</div>
