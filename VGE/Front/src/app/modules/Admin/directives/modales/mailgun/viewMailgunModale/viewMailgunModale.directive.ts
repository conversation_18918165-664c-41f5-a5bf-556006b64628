import {Component, EventEmitter, Input, OnInit, Output} from "@angular/core";
import {NgbActiveModal, NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {MailgunDb} from "../../../../../../shared/sdk";
import moment from 'moment';

@Component({
  selector: 'ViewMailgunModaleDirective',
  templateUrl: './viewMailgunModale.directive.html',
})
export class ViewMailgunModaleDirective implements OnInit {

  @Input() mailguns: MailgunDb[];
  @Input() libelle: string = null;

  public moment = moment;

  constructor(
    public activeModal: NgbActiveModal,
    public modalService: NgbModal,
  ) {

  }

  ngOnInit() {

  }

}
