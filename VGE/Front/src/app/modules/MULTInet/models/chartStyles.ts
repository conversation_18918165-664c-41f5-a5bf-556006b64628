export const chartsBarEUROptions = {
  type:'bar',
  data: {
    labels:[],
    datasets:[
    ]
  },
  options : {
    responsive: true,
    maintainAspectRatio: false,
    height: 265,

    tooltips: {
      callbacks: {
        label: function (tooltipItem, data) {
          try {
            let label = ' ' + data.datasets[tooltipItem.datasetIndex].label || '';

            if (label) {
              label += ': ';
            }

            const value = data.datasets[tooltipItem.datasetIndex].data[tooltipItem.index];

            label += new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(
              value,
            )
            return label;
          } catch (error) {
            // console.log(error);
          }
        }
      }
    },

    legend: {
      display: true,
      position: 'bottom',
      fontSize: 10,
      align:'left',
      fullWidth: true,
      reverse: true,
      padding: 5,

    },
    scaleShowLabels : true,
    bezierCurve : true,
    fill: true,
    layout: {
      padding: {
        left: 5,
        right: 5,
        top: 5,
        bottom: 5
      }
    },
    scales: {
      xAxes: [{
        stacked: true,
        // display: false
      }],
      yAxes: [{
        stacked: true
      }]
    },
    plugins: {
      labels: [
        {
          render: function (args) {
            return new Intl.NumberFormat('fr-FR', { style: 'currency', currency: 'EUR' }).format(
              args.value,
            )
          },
          fontColor:'#000000',
          arc: true,
          showZero: false,
          textShadow: true,
          overlap: true,
          position: 'default', //'default', 'border' and 'outside'
        }

      ]
    }
  }
}

export const chartsBarOptions = {
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
      shadowStyle: {
        color: 'rgba(0,0,0,0.05)',
        shadowBlur: 10
      }
    },
    formatter: (params: any) => {
      let tooltip = `
        <div style="
          padding: 10px;
          font-family: Arial, sans-serif;
          line-height: 1.4;
        ">
          <div style="
            font-weight: 600;
            margin-bottom: 8px;
            font-size: 14px;
            color: #2C3E50;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
          ">
            ${params[0].name}
          </div>`;

      let total = 0;
      params.forEach((param: any) => {
        total += param.value;
        tooltip += `
          <div style="
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 6px 0;
            font-size: 13px;
          ">
            <div style="display: flex; align-items: center;">
              <span style="
                display: inline-block;
                width: 8px;
                height: 8px;
                border-radius: 50%;
                background-color: ${param.color};
                margin-right: 8px;
                box-shadow: 0 0 4px rgba(0,0,0,0.1);
              "></span>
              <span style="color: #666;">${param.seriesName}</span>
            </div>
            <span style="
              font-weight: 600;
              margin-left: 24px;
              color: #2C3E50;
            ">${param.value}</span>
          </div>`;
      });

      tooltip += `
        <div style="
          margin-top: 8px;
          padding-top: 8px;
          border-top: 1px solid #eee;
          font-weight: 600;
          color: #2C3E50;
          display: flex;
          justify-content: space-between;
        ">
          <span>Total</span>
          <span>${total}</span>
        </div>
      </div>`;

      return tooltip;
    },
    backgroundColor: 'rgba(255, 255, 255, 0.98)',
    borderWidth: 0,
    padding: [12, 16],
    textStyle: {
      color: '#666',
      fontSize: 13
    },
    extraCssText: 'box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1); border-radius: 8px;'
  },
  legend: {
    show: true,
    bottom: 15,
    icon: 'roundRect',
    textStyle: {
      fontSize: 12,
      color: '#666',
      fontWeight: 500
    },
    itemWidth: 10,
    itemHeight: 10,
    itemGap: 20,
    padding: [8, 15],
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    borderRadius: 6,
    pageTextStyle: {
      color: '#666'
    }
  },
  grid: {
    left: '4%',
    right: '4%',
    bottom: '18%',
    top: '8%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: [],
    axisLabel: {
      interval: 0,
      rotate: 45,
      fontSize: 12,
      color: '#666',
      margin: 16,
      fontWeight: 500
    },
    axisTick: {
      alignWithLabel: true,
      lineStyle: {
        color: '#e8e8e8'
      }
    },
    axisLine: {
      lineStyle: {
        color: '#e8e8e8',
        width: 1.5
      }
    },
    splitLine: {
      show: false
    }
  },
  yAxis: {
    type: 'value',
    name: 'Nombre',
    nameTextStyle: {
      color: '#666',
      fontSize: 12,
      padding: [0, 0, 8, 0],
      fontWeight: 500
    },
    axisLabel: {
      color: '#666',
      fontSize: 12,
      fontWeight: 500
    },
    splitLine: {
      lineStyle: {
        type: 'dashed',
        color: '#eeeeee',
        width: 0.8
      }
    }
  },
  series: [{
    data: [],
    barWidth: '55%',
    animationDuration: 1500,
    animationEasing: 'elasticOut',
    itemStyle: {
      borderRadius: [4, 4, 0, 0],
      shadowColor: 'rgba(0,0,0,0.05)',
      shadowBlur: 5,
      shadowOffsetY: 2
    },
    emphasis: {
      focus: 'series',
      itemStyle: {
        shadowBlur: 15,
        shadowColor: 'rgba(0,0,0,0.2)'
      }
    },
    label: {
      show: true,
      color: '#ffffff',
      position: 'top',
      distance: 8,
      fontSize: 12,
      fontWeight: 500
    }
  }],
  animationDuration: 1500,
  animationEasing: 'elasticOut',
  universalTransition: true,
  color: [
    '#3498db', '#2ecc71', '#f1c40f', '#e74c3c',
    '#9b59b6', '#1abc9c', '#34495e', '#e67e22',
    '#16a085', '#c0392b'
  ]
};


export const chartsBarNoLegendOptions = {
  type:'bar',
  data: {
    labels:[],
    datasets:[]
  },
  options : {
    responsive: true,
    maintainAspectRatio: false,
    height: 265,

    legend: {
      display: true,
      position: 'bottom',
      fontSize: 10,
      align:'left',
      fullWidth: true,
      reverse: true,
      padding: 5,

    },
    scaleShowLabels : true,
    bezierCurve : true,
    fill: true,
    layout: {
      padding: {
        left: 5,
        right: 5,
        top: 5,
        bottom: 5
      }
    },
    scales: {
      xAxes: [{
        stacked: true,
        // display: false
      }],
      yAxes: [{
        stacked: true
      }]
    },
    plugins: {
      labels: [
        {
          render: 'value',
          fontColor:'#000000',
          arc: true,
          showZero: false,
          textShadow: true,
          overlap: true,
          position: 'default', //'default', 'border' and 'outside'
        }

      ]
    }
  }
}


export const chartsOptions = {
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
      shadowStyle: {
        color: 'rgba(0,0,0,0.1)'
      }
    },
    formatter: (params: any) => {
      const dataPoint = params[0];
      return `<strong>${dataPoint.name}</strong><br/>
              <span style="color:${dataPoint.color}">${dataPoint.value} dossiers</span>`;
    },
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderWidth: 1,
    borderColor: '#ccc',
    padding: 10,
    textStyle: {
      color: '#333'
    }
  },
  legend: {
    show: false
  },
  grid: {
    left: '5%',      // Augmenté de 3% à 10%
    right: '4%',
    bottom: '15%',
    top: '10%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: [],
    axisLabel: {
      interval: 0,
      rotate: 45,
      fontSize: 12,
      margin: 15,
      color: '#666'
    },
    axisTick: {
      alignWithLabel: true
    },
    axisLine: {
      lineStyle: {
        color: '#ddd'
      }
    }
  },
  yAxis: {
    type: 'value',
    name: 'Nombre de dossiers',
    nameTextStyle: {
      color: '#666',
      fontSize: 12,
      padding: [0, 0, 5, 0]
    },
    axisLabel: {
      color: '#666',
      fontSize: 12
    },
    splitLine: {
      lineStyle: {
        type: 'dashed',
        color: '#eee'
      }
    }
  },
  series: [{
    type: 'bar',
    data: [],
    barWidth: '60%',
    label: {
      show: true,
      position: 'top',
      fontSize: 12,
      color: '#666',
      formatter: '{c}',
      distance: 15
    },
    itemStyle: {
      borderRadius: [4, 4, 0, 0], // Coins arrondis en haut des barres
      shadowColor: 'rgba(0, 0, 0, 0.1)',
      shadowBlur: 10
    },
    emphasis: {
      itemStyle: {
        shadowBlur: 20,
        shadowColor: 'rgba(0,0,0,0.2)'
      }
    },
    animationDuration: 1000,
    animationEasing: 'elasticOut'
  }]
};
