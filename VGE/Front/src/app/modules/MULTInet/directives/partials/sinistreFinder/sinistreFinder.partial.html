<!--{{operation$|async|json}}-->
<!--<pre>{{operationsWithclient|json}}</pre>-->
<div class="row mB10">
  <div *ngIf="showOperation === true" [ngClass]="{'col-12': !(currentOperation || currentYear) , 'col-5': (currentOperation || currentYear)}">

    <div class="d-flex justify-content-start">
      <div class="flex-fill">
<!--          groupBy="clientLibelle"-->
        <ng-select
          [items]="operationsWithclient"
          [multiple]="false"
          [closeOnSelect]="true"
          [searchable]="true"
          bindLabel="title"
          groupBy="sub"
          [(ngModel)]="currentOperation"
          placeholder="Choisir une opération pour sélection de dossier Multi-risques"
          (change)="loadYears($event)"
          (clear)="reset()"
        >
          <ng-template ng-optgroup-tmp let-item="item">
            {{item?.sub}}
          </ng-template>
          <ng-template ng-option-tmp let-item="item">
            {{item?.title}}
          </ng-template>
        </ng-select>
      </div>
      <button class="btn btn-info" style="padding-bottom: 6px;margin-left: 5px;padding-top: 6px;" *ngIf="showGoBtnOperation && currentOperation" [routerLink]="['/MULTInet', 'sinistres', currentOperation.id]" [ngbTooltip]="'Accéder a l\'opération' + currentOperation.id"><i class="fa-duotone fa-caret-right"></i></button>
    </div>

  </div>

  <div class="flex-fill" [ngClass]="{'col-3': showOperation === true, 'col-6': showOperation === false}">
    <ng-select
      *ngIf="currentOperation"
      [items]="years"
      [(ngModel)]="currentYear"
      placeholder="Choisir une année"
      (change)="loadDossiers($event)"
    ></ng-select>
  </div>

  <div class="flex-fill" [ngClass]="{'col-4': showOperation === true, 'col-6': showOperation === false}">
    <ng-select
      *ngIf="currentYear"
      [items]="dossiers"
      [(ngModel)]="currentDossier"
      placeholder="Choisir un dossier"
      bindLabel="referencevge"
      (change)="loadDossier($event)"
      (clear)="resetCurrentDossier()"
    >
    </ng-select>
  </div>
</div>
