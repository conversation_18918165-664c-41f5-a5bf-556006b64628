import {Component, Input, OnInit} from "@angular/core";
import {IntraDoFileftp, People} from "../../../../../shared/sdk/models";
import {AppFtpFoldersService, ArboInterface} from "../../../../../common/services/app.ftpFolders.service";
import {ROLES} from "../../../../../datas/roles.helpers";
import {AppSessionService} from "../../../../../common/services/app.session.service";
import {IntraDoFileftpApi} from "../../../../../shared/sdk/services/custom";
import {filesTypesIcon} from "../../../../../datas/fileType.helpers";
import {ToastrService} from "ngx-toastr";
import {PeopleWithrightsInterface} from "../../../../../common/interfaces/peopleWithrights.interface";


@Component({
  selector: 'elementDirectoryFile',
  templateUrl: './elementDirectoryFile.partial.html'
})
export class ElementDirectoryFilePartial implements OnInit {

  @Input('fileRights') fileRights: IntraDoFileftp[] = null;
  @Input('folder') folder:string = null; //folderAssureur | .. en minuscules
  @Input('pathFilesFolder') pathFilesFolder:ArboInterface = null;

  @Input('operationId') operationId:number = null;
  @Input('sinistreId') sinistreId:number = null;

  public user: PeopleWithrightsInterface;
  public ROLES = ROLES;

  public currentFileRight:IntraDoFileftp = null;

  public filesTypesIcon= filesTypesIcon;

  constructor(
    public ftpService: AppFtpFoldersService,
    public _sessionService: AppSessionService,
    public _intraDoFileFtpApi : IntraDoFileftpApi,
    public _toastr: ToastrService,
  ) {

    this._sessionService.logguedAccountEmitter.subscribe((people) => {
      this.user = people;
    })

  }

  ngOnInit(): void {

    if(this.pathFilesFolder.type != 'directory'){
      /***
       * Look IF public / public with an existing IntraDoFileftp
       */
      this.currentFileRight = this.fileRights.find((ele) => {
        return ele.type == this.folder && ele.file.toLowerCase() == this.pathFilesFolder.name.toLowerCase()
      });
    }
  }

  download(path){
    path = encodeURIComponent(path);
    this.ftpService.download(path);
  }

  togglePublic(pathFilesFolder:ArboInterface){

    if(this.currentFileRight){

      if(this.currentFileRight.public == 0){

        this._intraDoFileFtpApi.patchAttributes(this.currentFileRight.id, {public: 1}).subscribe((res:IntraDoFileftp) => {
          this.currentFileRight = res;
          this._toastr.success('Le fichier est devenu public', 'Ajout du droit sur ce fichier')
        });

      }else{

        this._intraDoFileFtpApi.patchAttributes(this.currentFileRight.id, {public: 0}).subscribe((res:IntraDoFileftp) => {
          this.currentFileRight = res;
          this._toastr.success('Le fichier est devenu privé', 'Suppression du droit sur ce fichier')
        });

      }

      // /* DELETE ! */
      // this._intraDoFileFtpApi.deleteById(this.currentFileRight.id).subscribe((res) => {
      //   this.currentFileRight = null;
      //   this._toastr.success('Le fichier est devenu privé', 'Suppression du droit sur ce fichier')
      // }, error => {this._toastr.success(error, 'Suppression du droit sur ce fichier')});
    }else{
      /* CREATE ! */
      let data = {
        operationId: this.operationId,
        sinistreId: this.sinistreId,
        public: 1,
        file: pathFilesFolder.name,
        type: this.folder
      }

      this._intraDoFileFtpApi.create(data).subscribe((res:IntraDoFileftp) => {
        this.currentFileRight = res;
        this._toastr.success('Le fichier est devenu public', 'Ajout du droit sur ce fichier');

      }, error => {this._toastr.error(error, 'Ajout du droit sur ce fichier')});

    }



  }



}
