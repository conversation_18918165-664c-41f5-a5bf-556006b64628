<div class="row mT15 mB15">
  <div class="col-12 d-flex justify-content-start">
    <ng-container *ngFor="let folder of folders">
      <div class="btn-group mr-1 mb-1" *ngIf="sinistre[folder.folder] && folder?.element">
        <button type="button" class="btn btn-blue btn-sm btn-social dropdown-toggle" ngbDropdownToggle data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
          <i class="fa-duotone fa-folder"></i> {{folder.libelle}}
        </button>
        <div class="dropdown-menu arrow">
          <elementDirectoryFile [sinistreId]="sinistre?.id"
                                [fileRights]="fileRights"
                                [operationId]="sinistre?.operation_id"
                                [folder]="folder?.folder"
                                [pathFilesFolder]="folder?.element"></elementDirectoryFile>
        </div>
      </div>
    </ng-container>


  </div>
</div>
