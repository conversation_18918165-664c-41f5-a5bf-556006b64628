
<div class="modal-header">
  <h4 class="modal-title">Changement d'état du sinistre {{sinistre?.referencevge}}</h4>
  <button type="button" class="close" aria-label="Close" (click)="activeModal.dismiss('Cross click')">
    <span aria-hidden="true">&times;</span>
  </button>
</div>

<div class="modal-body">

  <h4>Historique des changements d'état</h4>

  <table width="100%" class="table table-compact table-hover table-striped fs-12">
    <thead>
    <tr>
      <th style="vertical-align: middle">Date</th>
      <th style="vertical-align: middle">Créateur</th>
      <!--                    <th style="vertical-align: middle">eRelaunch</th>-->
      <th style="vertical-align: middle">
        <div class="d-flex justify-content-between align-items-center">
          <div class="mR15">
            Actualisations
          </div>
        </div>
      </th>
      <th style="vertical-align: middle">FTP</th>
      <th style="vertical-align: middle"><PERSON><PERSON><PERSON> joints</th>
      <th style="vertical-align: middle">Mail</th>
    </tr>
    </thead>
    <tbody>
    <ng-container *ngIf="sinistre?.historiques?.length > 0">
      <ng-container *ngFor="let histo of sinistre?.historiques; let index = index">
        <tr *ngIf="histo?.comment.includes('Modification de l\'état')">
          <td style="white-space: nowrap">{{histo?.date|momentDateFormat:'YYYY-MM-DD HH:mm':'DD/MM/YYYY'}}</td>
          <td class="text-center"><people [people]="histo.people" [small]="true" [showInitiales]="true" [showMediaPhoto]="true"></people></td>
          <td [ngStyle]="{'color': histo?.etatSuivi?.couleur}">
            <div class="d-flex justify-content-start">
              <span class="mR5">
                <i *ngIf="histo?.id_etat_suivi" class="fa-duotone fa-info-circle" [ngbTooltip]="histo?.etatSuivi?.libelle"></i>
                <i *ngIf="!histo?.id_etat_suivi" class="fa-duotone fa-comment"></i>
              &nbsp;</span>
              <span class="preventDisplay" [innerHTML]="histo?.comment"></span>
            </div>
          </td>
          <td>
            <ftpDownload *ngIf="histo?.ftp" [lienFtp]="histo.ftp" [operationFolder]="operation?.dossierftp"></ftpDownload>
          </td>
          <td class="text-nowrap">
            <span class="mR2" *ngIf="histo?.file"><fileDownload [lien]="histo.file" [ngbTooltip]="histo?.file.split('/').pop()"></fileDownload></span>
            <span class="mR2" *ngIf="histo?.file2"><fileDownload [lien]="histo.file2" [ngbTooltip]="histo?.file2.split('/').pop()"></fileDownload></span>
            <span class="mR2" *ngIf="histo?.file3"><fileDownload [lien]="histo.file3" [ngbTooltip]="histo?.file3.split('/').pop()"></fileDownload></span>
            <span class="mR2" *ngIf="histo?.file4"><fileDownload [lien]="histo.file4" [ngbTooltip]="histo?.file4.split('/').pop()"></fileDownload></span>
            <span class="mR2" *ngIf="histo?.file5"><fileDownload [lien]="histo.file5" [ngbTooltip]="histo?.file5.split('/').pop()"></fileDownload></span>
          </td>
          <td>
            <span *ngIf="histo?.email">
              <fileDownload [lien]="histo?.email" [tooltip]="'Télecharger le mail EML'"></fileDownload>
            </span>
          </td>
          <td style="white-space: nowrap">
            <i class="fa-duotone fa-users" *ngIf="histo?.to" [ngbTooltip]="histo?.to"></i>
          </td>
        </tr>
      </ng-container>
    </ng-container>
    </tbody>

  </table>

  <globalForms
    [data]="null"
    [columns]="columnsForm"
    [form]="form"
    (sendDatas)="save($event)"
    [folder]="folder"
    [back]="null"
  >
  </globalForms>

</div>
