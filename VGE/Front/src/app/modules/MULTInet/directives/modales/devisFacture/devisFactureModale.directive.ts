import {Component, EventEmitter, Input, OnInit, Output, ViewChild} from "@angular/core";
import {<PERSON><PERSON><PERSON><PERSON>, FormGroup, Validators} from "@angular/forms";
import {NgbActiveModal, NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {ToastrService} from "ngx-toastr";
import moment from "moment";
import {TableColumnInterface} from "../../../../shared/interfaces/table.column.interface";
import {AppSessionService} from "../../../../../common/services/app.session.service";
import {AppCacheService} from "../../../../../common/services/app.cache.service";
import {
	IntraMultiDevisApi,
	IntraMultiDevisFactureApi
} from "../../../../../shared/sdk/services/custom";
import {ROLES} from "../../../../../datas/roles.helpers";
import {
	IntraMultiDevis,
	IntraMultiDevisCategorie,
	IntraMultiDevisFacture,
	IntraMultiOperation,
	IntraMultiSinistre,
	People
} from "../../../../../shared/sdk/models";
import {PeopleWithrightsInterface} from "../../../../../common/interfaces/peopleWithrights.interface";

@Component({
	selector: 'DevisFactureMultiModale',
	templateUrl: './devisFactureModale.directive.html',
})
export class DevisFactureMultiModaleDirective implements OnInit{

	@Input('devis') devis: IntraMultiDevis = null;
	@Input('operation') operation: IntraMultiOperation;
	@Input('sinistre') sinistre: IntraMultiSinistre;
	@Input('devisFacture') devisFacture: IntraMultiDevisFacture;
	@Output() clickeventUpdateDevis = new EventEmitter<IntraMultiDevisFacture>();

	public columnsForm: Array<TableColumnInterface>;

	public allsocietes: Array<{key:string, text:string}> = [];

	public folder = 'devis'; // For Uploads !
	public form: FormGroup;

	public entreprises = [];
	public etatsSuivis = [];

	public categoriesDevis: IntraMultiDevisCategorie[] = [];

	public user: PeopleWithrightsInterface;
	public ROLES = ROLES;

	constructor(
		public activeModal: NgbActiveModal,
		public modalService: NgbModal,
		public _sessionService: AppSessionService,
		public _appCacheService: AppCacheService,
		public formBuilder: FormBuilder,
		public _toastr: ToastrService,
		public doDevisApiService: IntraMultiDevisApi,
		public doDevisFactureApiService: IntraMultiDevisFactureApi,
	) {
		this._sessionService.logguedAccountEmitter.subscribe((people) => {
			this.user = people;
		});

	}

	save(datas:object){
		let self = this;

		let date = moment({years: datas['date'].year, months: datas['date'].month -1, days: datas['date'].day, hours: 12});
		let devisDatas = {
			libelle:datas['libelle'],
			date:date,
			montantHT:datas['montantHT'],
			montantTTC: datas['montantTTC'],
			comment:datas['comment'],
			file: datas['file'],
			devis_id: this.devis.id,
			createur_id: this.user.id,
		}

		if(this.devisFacture){
			this.doDevisFactureApiService.patchAttributes(this.devisFacture.id, devisDatas).subscribe((res:any) => {
				this.clickeventUpdateDevis.emit(res);
			});
		} else {
			this.doDevisFactureApiService.create(devisDatas).subscribe((res:any) => {
				this.clickeventUpdateDevis.emit(res);
			});
		}
	}

	ngOnInit(): void {
		let self = this;

		this.form = this.formBuilder.group({
			devis_id:[null, [Validators.required]],
			libelle: [null, [Validators.required]],
			date: ['', [Validators.required]],
			montantHT:['', [Validators.pattern('[0-9,\.]+'), Validators.required]],
			montantTTC:['', [Validators.pattern('[0-9,\.]+'), Validators.required]],
			comment: ['', []],
			file:['', []],
		});

			this.columnsForm =
				[

					{type:"separator", name:((this.devisFacture) ? "Modification d'une facture devis" : "Création d'une facture devis"), icon:"fa-duotone fa-money-bill-alt", exclude:true},
					{key: "libelle",name: "Libellé de la facture devis", type: "input", typeInput:'text'},
					{key: "date",name: "Date de la facture devis", type: "date"},
					{key: "montantHT",name: "Montant HT", type: "input", typeInput:'number'},
					{key: "montantTTC",name: "Montant TTC", type: "input", typeInput:'number'},
					{key: "comment",name: "Commentaire Travaux de la facture devis", type: "text"},
					{key: "file",name: "Fichier facture devis", type: "file"},
				];

			if(this.devis){
				this.form.controls['devis_id'].setValue(this.devis.id);
			}


			/* Set Values ! */
			if(this.devisFacture){

				this.form.controls['libelle'].setValue(this.devisFacture.libelle);

				let date = moment(this.devisFacture.date, 'YYYY-MM-DD').toObject();
				this.form.controls['date'].setValue({year:date.years, month:date.months+1, day: date.date});

				this.form.controls['montantHT'].setValue(this.devisFacture.montantHT);
				this.form.controls['montantTTC'].setValue(this.devisFacture.montantTTC);
				this.form.controls['comment'].setValue(this.devisFacture.comment);
				this.form.controls['file'].setValue(this.devisFacture.file);
				this.form.controls['devis_id'].setValue(this.devisFacture.devis_id);
				this.form.controls['createur_id'].setValue(this.devisFacture.createur_id);
			}
	}

	changeTVA($event){
		if(this.form.controls['montantHT'].value && !this.form.controls['montantTTC'].value){
			this.form.controls['montantTTC'].setValue(parseFloat(this.form.controls['montantHT'].value) * (1+20/100))
		}

	}

}
