import {Component, EventEmitter, Input, OnInit, Output, ViewChild} from "@angular/core";
import {FormBuilder, FormGroup, Validators} from "@angular/forms";
import {NgbActiveModal, NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {ToastrService} from "ngx-toastr";
import {
	IntraDoSinistreApi,
	IntraDoSinistreDocumentation,
	IntraDoSinistreDocumentationApi,
	IntraDoSinistreDocumentationCategorieApi,
	IntraMultiSinistreApi,
	IntraMultiSinistreDocumentationApi,
	IntraMultiSinistreDocumentationCategorieApi
} from "../../../../../../../shared/sdk";
import {AppCacheService} from "../../../../../../../common/services/app.cache.service";
import {TableColumnInterface} from "../../../../../../shared/interfaces/table.column.interface";

@Component({
	selector: 'ChangeMultiCategorieDocumentationModaleDirective',
	templateUrl: './changeCategorieDocumentationModale.directive.html',
})
export class ChangeMultiCategorieDocumentationModaleDirective implements OnInit{

	@Input() doc;
	@Input() documents;
	@Output() clickeventUpdateDocumentation = new EventEmitter<IntraDoSinistreDocumentation>();

	public columnsForm: Array<TableColumnInterface>;

	public folder = 'documentation'; // For Uploads !
	public form: FormGroup;

	constructor(
		public activeModal: NgbActiveModal,
		public modalService: NgbModal,
		public _appCacheService: AppCacheService,
		public formBuilder: FormBuilder,
		public _toastr: ToastrService,
		public doSinistreApiService: IntraMultiSinistreApi,
		public documentationCategorieApiService: IntraMultiSinistreDocumentationCategorieApi,
		public documentationApiService: IntraMultiSinistreDocumentationApi,
	) {

		this.form = this.formBuilder.group({
			categorie_id:[null, [Validators.required]],
		});

		this.columnsForm =
			[
				{type:"separator", name:"Définir une catégorie", icon:"fa-duotone fa-cog", exclude:true},
				{key: "categorie_id",name: "Catégorie", type: "select", selectOptions:{multiple:false, labelBind:'libelle', keyBind:'id', options:[]}},
			];
	}


	save(datas:object){
		let self = this;

		this.documentationApiService.patchAttributes(this.doc.id, datas).subscribe((res:IntraDoSinistreDocumentation) => {
			if(res && !res['message']){
				this._toastr.success('Succès !', 'Modification de la catégorie du document');
				this.clickeventUpdateDocumentation.emit(res);
			}else{
				this._toastr.error(res['message'], 'Modification de la catégorie du document');
			}
		});

	}

	buildCategories(){
		const categoriesOptions = [];
		categoriesOptions.push({id: null, libelle: 'Non classifiée'});

		if(this.documents.categories && this.documents.categories.length > 0){
			this.documents.categories.forEach((categorie) => {
				categoriesOptions.push({id: categorie.id, libelle: categorie.libelle})

				if(categorie.enfants && categorie.enfants.length > 0){
					categorie.enfants.forEach((sousCategorie) => {
						categoriesOptions.push({id: sousCategorie.id, libelle: `${("- ").repeat(2)}${categorie.libelle} / ${sousCategorie.libelle}`})
					})
				}

			})
		}

		const col = this.columnsForm.find((el) => {
			return el.key == 'categorie_id';
		});
		if(col){col.selectOptions.options = JSON.parse(JSON.stringify(categoriesOptions))};
	}

	ngOnInit(): void {
		if(this.doc && this.doc.categorie_id){
			this.form.controls['categorie_id'].setValue(this.doc.categorie_id);
		}

		this.buildCategories()

	}


}
