import {Component, EventEmitter, Input, OnInit, Output, ViewChild} from "@angular/core";
import { IntraMultiSinistre} from "../../../../../../shared/sdk/models";
import {TableColumnInterface} from "../../../../../shared/interfaces/table.column.interface";
import {FormBuilder, FormGroup, Validators} from "@angular/forms";
import {NgbActiveModal, NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {AppCacheService} from "../../../../../../common/services/app.cache.service";
import {ToastrService} from "ngx-toastr";
import {IntraMultiSinistreApi} from "../../../../../../shared/sdk/services/custom";
import moment from 'moment';

@Component({
  selector: 'ChangeAvancementModaleDirective',
  templateUrl: './changeAvancementModale.directive.html',
})
export class ChangeAvancementModaleDirective implements OnInit{

  @Input() sinistre: IntraMultiSinistre;
  @Output() clickeventUpdateSinistre = new EventEmitter<IntraMultiSinistre>();

  public columnsForm: Array<TableColumnInterface>;

  public folder = 'sinistreMulti'; // For Uploads !
  public form: FormGroup;

  constructor(
    public activeModal: NgbActiveModal,
    public modalService: NgbModal,
    public _appCacheService: AppCacheService,
    public formBuilder: FormBuilder,
    public _toastr: ToastrService,
    public doSinistreApiService: IntraMultiSinistreApi,
  ) {

    this.form = this.formBuilder.group({
      avancement: ['', [Validators.required, Validators.min(0), Validators.max(100)]],
    });

    this.columnsForm =
      [
        {type:"separator", name:"Changement du taux d'avancement", icon:"fa-duotone fa-cog", exclude:true},
        {key: "avancement",name: "Taux d'avancement", type: "input", typeInput:'number', max:100, min:0},
      ];

  }


  save(datas:object){
    let self = this;

    // datas['mdate'] = moment().format('YYYY-MM-DD');

    // console.log(this.sinistre.id, 'this.sinistre.id', this.sinistre)
    this.doSinistreApiService.patchAttributes(this.sinistre.id, datas).subscribe((res:IntraMultiSinistre) => {
      if(res && !res['message']){
        this._toastr.success('Succès !', 'Modification du taux d\'avancement sinistre');
        this.clickeventUpdateSinistre.emit(res);
      }else{
        this._toastr.error(res['message'], 'Modification du taux d\'avancement');
      }
    });

  }

  ngOnInit(): void {
    let self = this;


    this.form.controls['avancement'].setValue(this.sinistre.avancement);

  }


}
