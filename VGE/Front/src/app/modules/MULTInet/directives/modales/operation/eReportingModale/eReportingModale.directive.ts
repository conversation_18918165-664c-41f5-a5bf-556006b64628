import {Component, EventEmitter, Input, OnInit, Output} from "@angular/core";
import {
  IntraMultiOperation, IntraMultiOperationApi,
} from "../../../../../../shared/sdk";
import {NgbActiveModal, NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {shareReplay} from "rxjs/operators";
import {returnOperationsDOs} from "../../../../pages/operations.e-reporting/operations.e-reporting.page";
import {Observable} from "rxjs";
import {AppGlobalEventManagerService} from "../../../../../../common/services/app.globalEventManager.service";
import {accessEReportingRolesHelpers} from "../../../../../../datas/accessEReportingRoles.helpers";
import {AppSessionService} from "../../../../../../common/services/app.session.service";
import {PeopleWithrightsInterface} from "../../../../../../common/interfaces/peopleWithrights.interface";
import {ROLES} from "../../../../../../datas/roles.helpers";

@Component({
  selector: 'EReportingMultiModaleDirective',
  templateUrl: './eReportingModale.directive.html',
})
export class EReportingMultiModaleDirective implements OnInit{

  public operations$: Observable<returnOperationsDOs>;
  public operationssubscribed: returnOperationsDOs = {};

  public user: PeopleWithrightsInterface;
  public ROLES = ROLES;

  public canAccessEreporting: boolean = false;

  @Input() operation: IntraMultiOperation;

  constructor(
    public activeModal: NgbActiveModal,
    public modalService: NgbModal,
    public _sessionService: AppSessionService,
    public intraDoOperationApiService: IntraMultiOperationApi,
    public appGlobalEventManagerService: AppGlobalEventManagerService,
  ) {

    this._sessionService.logguedAccountEmitter.subscribe((people) => {
      if(people){
        this.user = people;
        this.canAccessEreporting = this.user.DO && this.user.DO.some((e) => {
          return accessEReportingRolesHelpers.indexOf(e.role) > -1
        })
      }
    });

  }

  load(){
    this.operations$ = this.intraDoOperationApiService.getOperationDOsMultiByClient({where: {id: this.operation.id}, 'order':'libelle ASC'}, true, false).pipe(shareReplay(1));

    this.operations$.subscribe((operations:returnOperationsDOs) => {

      this.operationssubscribed = operations;

      setTimeout(() => {
        this.appGlobalEventManagerService.closeSpinner();
      }, 600)
    });
  }

  ngOnInit(): void {
    let self = this;
    this.load();
  }


}
