import {Component, EventEmitter, Input, OnInit, Output, ViewChild} from "@angular/core";
import {
  IntraMultiEtat,
  IntraMultiEtatSuivi,
  IntraMultiOperation,
  IntraMultiSinistre,
  People
} from "../../../../../../shared/sdk/models";
import {TableColumnInterface} from "../../../../../shared/interfaces/table.column.interface";
import {FormBuilder, FormGroup, Validators} from "@angular/forms";
import {NgbActiveModal, NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {AppCacheService} from "../../../../../../common/services/app.cache.service";
import {ToastrService} from "ngx-toastr";
import {IntraMultiSinistreApi} from "../../../../../../shared/sdk/services/custom";
import {IntraDoHistorique<PERSON><PERSON>} from "../../../../../../shared/sdk/services/custom";
import moment from 'moment';
import {ROLES} from "../../../../../../datas/roles.helpers";
import {AppSessionService} from "../../../../../../common/services/app.session.service";
import {PeopleWithrightsInterface} from "../../../../../../common/interfaces/peopleWithrights.interface";

@Component({
  selector: 'ChangeEtatSinistreModaleDirective',
  templateUrl: './changeEtatSinistreModale.directive.html',
})
export class ChangeEtatSinistreModaleDirective implements OnInit{

  @Input() sinistre: IntraMultiSinistre | Partial<IntraMultiSinistre>;
  @Input() etatId: number;
  @Input() operation: IntraMultiOperation | Partial<IntraMultiOperation>;
  @Output() clickeventUpdateSinistre = new EventEmitter<IntraMultiSinistre>();

  public columnsForm: Array<TableColumnInterface>;

  public allsocietes: Array<{key:string, text:string}> = [];

  public folder = 'sinistreMulti'; // For Uploads !
  public form: FormGroup;

  public responsables = [];
  public etats = [];

  public etatsSuivis : {key:number, text:string}[] = [];
  public allEtatsSinistres:IntraMultiEtatSuivi[] = [];

  public user: PeopleWithrightsInterface;
  public ROLES = ROLES;

  constructor(
    public activeModal: NgbActiveModal,
    public modalService: NgbModal,
    public _sessionService: AppSessionService,
    public _appCacheService: AppCacheService,
    public formBuilder: FormBuilder,
    public _toastr: ToastrService,
    public doSinistreApiService: IntraMultiSinistreApi,
    public doHistoriqueApiService: IntraDoHistoriqueApi
  ) {

    this._sessionService.logguedAccountEmitter.subscribe((people) => {
      this.user = people;
    });

    this.form = this.formBuilder.group({
      etatId: ['', [Validators.required]],

      responsables: ['', []],
      comment: ['', []],
      emailbody:['', []],
      idEtatSuivi:[6, []],

    });


    this.columnsForm =
      [
        {type:"separator", name:"Changement d'état du sinistre", icon:"fa-duotone fa-cog", exclude:true},
        {key: "etatId",name: "Nouveau statut du sinistre", type: "select", selectOptions:{multiple:false, labelBind:'text', keyBind:'key', options:[]}},

        {type:"separator", name:"Ajout d'une actualisation au sinistre\n", icon:"fa-duotone fa-cog", exclude:true},
        {key: "responsables",name: "Acteurs à notifier sur l'operation", type: "select", selectOptions:{multiple:true, labelBind:'text', keyBind:'key', selectableGroupAsModel: true, selectableGroup: true, groupBy:'societe', options:[]}},
        {key: "comment",name: "Actualisation du suivi sinistre", type: "text"},
        {key: "emailbody",name: "Contenu du mail", type: "text"},
        {key: "idEtatSuivi",name: "Etat du suivi", type: "select", selectOptions:{multiple:false, labelBind:'text', keyBind:'key', options:[]}},
      ];
  }


  save(datas:object){
    let self = this;

    /***
     * Get infos IntraDoHistorique !
     *
     * SUIVI de sinistre !
     *
     */
    let tos = [];
    if(datas['responsables']){
      datas['responsables'].forEach((responsableId) => {
        let responsable = self.responsables.find((responsableTo) => {return responsableTo.id == responsableId});
        if(responsable && responsable.email){
          tos.push(responsable.email)
        }
      });
    }

    let comment = '';
    let etat = this.allEtatsSinistres.find((el) => {return el.id == datas['etatId']});
    if(!datas['comment']){
      if(etat){
        comment = 'Modification de l\'état sinistre en '+ etat.libelle;
      }else{
        comment = 'Modification de l\'état sinistre ';
      }
    }else{
      comment = datas['comment'];
    }

    let subject = 'l\'état du sinistre a été modifié';
    if(etat){
      subject + ' en '+ etat.libelle;
    }


    let histoDatas = {
      responsables: datas['responsables'],
      admins: datas['admins'],
      comment: comment, // If ok --> GO MAIL
      date: moment().utc(true).toISOString(),
      observations: "",
      // file: null,
      subject: subject,
      sinistreId:this.sinistre.id,
      userId: this.user.id, // Createur
      state: 0, // ?
      to: tos.join(', '),
      etatId: datas['etatId'], // Lien vers l'etat de la modifi du sinistre
      // etat_id: datas['etatId'], // Lien vers l'etat de la modifi du sinistre
      type: 0, //type dactu
      typeActualisation: 'simple',
      dest: JSON.stringify({
        to: tos.join(', '),
        destinataires: datas['responsables'],
        admins: datas['admins'],
      }),
      emailbody: datas['emailbody'],
      idEtatSuivi: datas['idEtatSuivi'], // lié a intra_do_etat_suivi
    }
    /***
     * END SUIVI SINISTRE
     */

    this.doSinistreApiService.changeStatut(this.sinistre.id, datas['etatId']).subscribe((res:IntraMultiSinistre) => {
      if(res && !res['message']){

        /* Send EMAIL ! */

        this.doHistoriqueApiService.create(histoDatas).subscribe((histo) => {
          this._toastr.success('Succès !', 'Changement de l\'état du sinistre');
          this.clickeventUpdateSinistre.emit(res);
        });

      }else{
        this._toastr.error(res['message'], 'Changement de l\'état du sinistre');
      }
    });

  }

  ngOnInit(): void {
    let self = this;

    this._appCacheService.getIntraMultiEtats(false, null).subscribe((res:IntraMultiEtat[]) => {
      res.forEach((el:IntraMultiEtat) => {
        self.etats.push({
          key: el.id,
          text: el.libelle
        });
      });

      let col = this.columnsForm.find((el) => {
        return el.key == 'etatId';
      });
      if(col){col.selectOptions.options = self.etats};
    });

    this.allEtatsSinistres = [];
    this._appCacheService.getIntraMultiEtats(false, null).subscribe((res:IntraMultiEtat[]) => {
      this.allEtatsSinistres = res;
    });

    this._appCacheService.getIntraMultiEtatsSuivi(false, null).subscribe((res:IntraMultiEtatSuivi[]) => {
      res.forEach((el:IntraMultiEtatSuivi) => {
        self.etatsSuivis.push({
          key: el.id,
          text: el.libelle
        });
      });

      let col = this.columnsForm.find((el) => {
        return el.key == 'idEtatSuivi';
      });
      if(col){col.selectOptions.options = self.etatsSuivis};
    });


    if(this.operation && this.operation.responsablesRelations){
      this.operation.responsablesRelations.forEach(el => {
        if(el && el.people && el.people.active) {
          let nom = ((el.people && el.people.societes && el.people.societes.length > 0) ? el.people.societes[0].nom : '') + ' (' + el.people.firstname + ' ' + el.people.lastname + ' [' + el.role.nom + '])';
          self.responsables.push({
            key: el.id,
            societe: ((el.people.societes && el.people.societes.length > 0) ? el.people.societes[0].nom : ''),
            text: nom
          });
        }
      });
      /* Set array associative in column */
      let col = this.columnsForm.find((el) => {
        return el.key == 'responsables';
      });
      if(col){col.selectOptions.options = self.responsables};

    }


    // this._appCacheService.getSocietes().subscribe((allSocietes) => {
    //   /* Associate key value to society */
    //   allSocietes.forEach(el => {
    //     self.allsocietes.push({key:el.id, text:el.nom});
    //   })
    //   /* Set array associative in column */
    //   let col = this.columnsForm.find((el) => {
    //     return el.key == 'idsocieteassureur';
    //   });
    //   if(col){col.selectOptions.options = self.allsocietes};
    // })

    this.form.controls['etatId'].setValue(this.etatId ? this.etatId  : this.sinistre.etat.id);

  }


}
