import {Component, OnInit} from "@angular/core";
import {Router} from "@angular/router";
import {AppSessionService} from "../../../../common/services/app.session.service";
import {ToastrService} from "ngx-toastr";
import {Loop<PERSON>ackAuth} from "../../../../shared/sdk/services/core";
import {IntraMultiOperationApi, IntraMultiSinistreApi, PeopleApi} from "../../../../shared/sdk/services/custom";
import {Observable} from "rxjs";
import {TableColumnInterface} from "../../../shared/interfaces/table.column.interface";
import {IntraMultiOperation, IntraMultiSinistre, IntraOperation, People, Societes} from "../../../../shared/sdk/models";
import {map, share, shareReplay, tap} from "rxjs/operators";
import {AppGlobalEventManagerService} from "../../../../common/services/app.globalEventManager.service";
import {ROLES} from "../../../../datas/roles.helpers";
import {NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {ChangeClientModaleDirective} from "../../directives/modales/operation/changeClientModale/changeClientModale.directive";
import {ChangeAssureurModaleDirective} from "../../directives/modales/operation/changeAssureurModale/changeAssureurModale.directive";
import {ChangePhotoOperationModaleDirective} from "../../directives/modales/operation/changePhotoOperationModale/changePhotoOperationModale.directive";
import {ChangePoliceAssureurModaleDirective} from "../../directives/modales/operation/changePoliceAssureurModale/changePoliceAssureurModale.directive";
import {ChangeStatutOperationModaleDirective} from "../../directives/modales/operation/changeStatutOperationModale/changeStatutOperationModale.directive";
import {AppCacheService} from "../../../../common/services/app.cache.service";
import {DashboardInterface} from "../../../Admin/interfaces/dashboard.interface";
import {PeopleWithrightsInterface} from "../../../../common/interfaces/peopleWithrights.interface";
import {accessEReportingRolesHelpers} from "../../../../datas/accessEReportingRoles.helpers";
import {
  ManageRightsSocieteModaleDirective
} from "../../directives/modales/operation/manageRightsSocieteModale/manageRightsSocieteModale.directive";
import {AddContactModaleDirective} from "../../directives/modales/operation/AddContatModale/addContactModale.directive";
import {statutsOperation} from '../../models/constsDO';
import {MULTIStats} from "../../interfaces/sinistresMULTI.interface";

export interface returnOperationsDOs{
  [client:string]: {
    client:Societes
    operations:operationReturnOperationsDOsInterface[]
    fonds:{
      client:Societes
      operations:operationReturnOperationsDOsInterface[]
      stats: MULTIStats;
    }[]
    stats: MULTIStats;
    statsFonds: MULTIStats;
    statsFondsGlobal: MULTIStats;
  }
}

export interface operationReturnOperationsDOsInterface {
  operation:IntraMultiOperation
  nbDO: number
  nbCX: number
  nbCXTermine: number
  nbCXEncours:number
  nbTermine: number
  nbEncours: number
  nbGa: number
  nbEs: number
  nbEnjeuxSup130kEncours: number
  nbEnjeuxSup130kTermine: number
  nbEnjeuxSup130k:number
  sumEnjeux: number
  sumIndemnites: number
  sumFrais: number
  years: number[]
}

@Component({
  selector: 'operationsClientDOList',
  templateUrl: './operations-client.list.page.html'
})
export class OperationsClientListPage implements OnInit {

  public operations$: Observable<returnOperationsDOs>;
  public operationssubscribed: returnOperationsDOs = {};
  public columns: Array<TableColumnInterface>;
  public hiddenColumns;

  public user: PeopleWithrightsInterface;
  public ROLES = ROLES;

  public stats: DashboardInterface = null;

  public canAccessEreporting: boolean = false;

  public operationsThumbnailMode: boolean;

  public showAccordions: boolean;

  constructor(
    // public activeModal: NgbActiveModal,
    public modalService: NgbModal,
    public router: Router,
    public _sessionService: AppSessionService,
    public authService: LoopBackAuth,
    public apiService: PeopleApi,
    public intraDoOperationApiService: IntraMultiOperationApi,
    public intraDoSinistreApiService: IntraMultiSinistreApi,
    public _toastr: ToastrService,
    public appGlobalEventManagerService: AppGlobalEventManagerService,
    public _cacheService: AppCacheService
) {

    this.appGlobalEventManagerService.openSpinner();

    this._sessionService.logguedAccountEmitter.subscribe((people) => {
      if (people) {
        this.user = people;

        this.canAccessEreporting = this.user.DO && this.user.DO.some((e) => {
          return accessEReportingRolesHelpers.indexOf(e.role) > -1;
        });

        this.handleOperationViewMode();
      }
    });

    this.appGlobalEventManagerService.updateTitle('Liste des opérations MULTInet');
    this.appGlobalEventManagerService.updateBreadcrumb([{title:'Home', url:'/'}, {title:'Liste des opérations MULTInet', url:'/'}])

    // this.columns =
    //   [
    //     // Réclamations GPA	Réclamations GBF	Libellé	Adresse	Code postal	Ville	Date réception	Date Création	Statut GPAnet	Registre GPAnet	Docs Généraux	Actions
    //
    //     {key: 'libelle', object:'operation', name:"Libellé", type: 'input', order:false, filter:true},
    //     {key: 'adresse', object:'operation', name:"Adresse", type: 'input', order:false, filter:false},
    //     {key: 'code', object:'operation', name:"Code postal", type: 'input', order:false, filter:true},
    //     {key: 'ville', object:'operation', name:"Ville", type: 'input', order:false, filter:false},
    //     {key: 'datereception', object:'operation', name:"Date réception", type: 'date', order:false, filter:false},
    //     {key: 'datecreation', object:'operation', name:"Date de création", type: 'date', order:false, filter:false},
    //     {key: 'termine', object:'operation', name:"Statut GPAnet", type: 'boolean', datas:{0:'En cours', 1:'Terminé'}, order:false, filter:false},
    //
    //
    //   ];
    // this.hiddenColumns = hiddenColumns.concat([])

    this.load();

  }

  load(){
    this.operations$ = this.intraDoOperationApiService.getOperationDOsMultiByClient({include:['commentaires', 'docsGeneraux'], order:'libelle ASC'}, true, false).pipe(shareReplay(1));

    this.operations$.subscribe((operations:returnOperationsDOs) => {

      this.operationssubscribed = operations;

      setTimeout(() => {
        this.appGlobalEventManagerService.closeSpinner();
      }, 600)
    });

  }

  delete(operation:IntraMultiOperation){

    this.intraDoOperationApiService.deleteById(operation.id).subscribe(
      (res) => {
        this._toastr.success('Suppression de l\'opération', 'L\'opération a été supprimée avec succès');
        this.load();
      },
      (err) => {this._toastr.success('Suppression de l\'opération', err.message);}

    );

  }

  ngOnInit(): void {

    this.apiService.dashboard(false).subscribe((stats: any)=>{
      this.stats = stats;
    })

  }

  openModaleManageContatsSociete(clientId: number, clientName: string){
    const activeModal = this.modalService.open(ManageRightsSocieteModaleDirective, { size: 'xl', centered: false });
    activeModal.componentInstance.clientId = clientId;
    activeModal.componentInstance.clientName = clientName;
    activeModal.componentInstance.clickeventUpdateOperation.subscribe((ret) => {

    })
  }

  openModaleAddContat(clientId: number, clientName: string){
    const activeModal = this.modalService.open(AddContactModaleDirective, { size: 'xl', centered: false });
    activeModal.componentInstance.clientId = clientId;
    activeModal.componentInstance.clientName = clientName;
    activeModal.componentInstance.clickeventUpdateOperation.subscribe((ret) => {

    })
  }

  openModaleChangeClient(operation:IntraMultiOperation, i:number, ii:number){
    let self = this;
    const activeModal = this.modalService.open(ChangeClientModaleDirective, { size: 'lg', centered: false });

    activeModal.componentInstance.operation = operation;
    activeModal.componentInstance.clickeventUpdateOperation.subscribe((ret:IntraMultiOperation) => {
      // console.log('ret',ret);
      if(ret){
        activeModal.close();

        this.operations$ = this.intraDoOperationApiService.getOperationDOsMultiByClient({include:['commentaires', 'docsGeneraux'], 'order':'libelle ASC'}, false, false).pipe(share());
        this._cacheService.deleteCachelistReclamationGPAForIdoAndFilter();

      }else{
        this._toastr.error('Une erreur s\'est produite', 'Problème');
      }
    })
  }
  openModaleChangeAssureur(operation:IntraMultiOperation, i:number, ii:number){
    const activeModal = this.modalService.open(ChangeAssureurModaleDirective, { size: 'lg', centered: false });

    activeModal.componentInstance.operation = operation;
    activeModal.componentInstance.clickeventUpdateOperation.subscribe((ret:IntraMultiOperation) => {
      if(ret){
        activeModal.close();

        this.operationssubscribed[Object.keys(this.operationssubscribed)[i]].operations[ii].operation = ret;

      }else{
        this._toastr.error('Une erreur s\'est produite', 'Problème');
      }
    })
  }
  openModaleChangePhoto(operation:IntraMultiOperation, i:number, ii:number){
    const activeModal = this.modalService.open(ChangePhotoOperationModaleDirective, { size: 'lg', centered: false });

    activeModal.componentInstance.operation = operation;
    activeModal.componentInstance.clickeventUpdateOperation.subscribe((ret:IntraMultiOperation) => {
      if(ret){
        activeModal.close();

        this.operationssubscribed[Object.keys(this.operationssubscribed)[i]].operations[ii].operation = ret;

      }else{
        this._toastr.error('Une erreur s\'est produite', 'Problème');
      }
    })
  }
  openModaleChangePoliceAssureur(operation:IntraMultiOperation, i:number, ii:number){
    const activeModal = this.modalService.open(ChangePoliceAssureurModaleDirective, { size: 'lg', centered: false });

    activeModal.componentInstance.operation = operation;
    activeModal.componentInstance.clickeventUpdateOperation.subscribe((ret:IntraMultiOperation) => {
      if(ret){
        activeModal.close();

        this.operationssubscribed[Object.keys(this.operationssubscribed)[i]].operations[ii].operation = ret;

      }else{
        this._toastr.error('Une erreur s\'est produite', 'Problème');
      }
    })
  }
  openModaleChangeStatutOperation(operation:IntraMultiOperation, i:number, ii:number){
    const activeModal = this.modalService.open(ChangeStatutOperationModaleDirective, { size: 'lg', centered: false });

    activeModal.componentInstance.operation = operation;
    activeModal.componentInstance.clickeventUpdateOperation.subscribe((ret:IntraMultiOperation) => {
      if(ret){
        activeModal.close();

        this.operationssubscribed[Object.keys(this.operationssubscribed)[i]].operations[ii].operation = ret;

      }else{
        this._toastr.error('Une erreur s\'est produite', 'Problème');
      }
    })
  }


  /**
   * Sinistre finder -> GO !
   * @param $event
   */
  loadSinistre($event:{opId:number, year:number|string, sinistreId:number}){
    if($event && $event.sinistreId && $event.opId){
      this._toastr.success('Redirection', 'Recherche de sinistre')
      this.router.navigate(['/', 'MULTInet', 'sinistre', 'fiche', $event.sinistreId, $event.opId]);
    }
  }

  findList(type, value){
    if(value){
      let filter = {};
      if(type == 'refAssur'){
        filter['where'] = {"referenceassur":{"like":"%"+value+"%","options":"i"}}
      }else if(type == 'refEDO'){
        filter['where'] = {"referenceedo":{"like":"%"+value+"%","options":"i"}}
      }
      this.intraDoSinistreApiService.find(filter).subscribe((res:IntraMultiSinistre[]) => {
        if(res && res.length == 1){
          this._toastr.success('Sinistre trouvé', 'Recherche de sinistre')
          this.router.navigate(['/MULTInet', 'sinistre', 'fiche', res[0].id, res[0].operation_id]);
        }else if(res && res.length > 1){

        }else{
          this._toastr.warning('Aucun sinistre trouvé', 'Recherche de sinistre')
        }
      });
    }else{
      this._toastr.warning('Veuillez entrer la référence', 'Recherche de sinistre')
    }

  }

  handleOperationViewMode(): void {
    //a priori on est sur la vue client donc pas admin
    const isAdmin = ROLES['admin'].indexOf(this.user.profil.name) > -1;

    if (localStorage.getItem('MULTInetOperationViewMode')) {
      this.operationsThumbnailMode = localStorage.getItem('MULTInetOperationViewMode') === 'thumbnail';
    } else {
      this.operationsThumbnailMode = !isAdmin;
    }
  }

  selectView(view: string): void {
    this.operationsThumbnailMode = view === 'thumbnail';
    localStorage.setItem('MULTInetOperationViewMode', view);
  }


	protected readonly statutsOperation = statutsOperation;
}
