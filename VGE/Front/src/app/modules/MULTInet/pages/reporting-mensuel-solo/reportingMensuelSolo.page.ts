import {Component, OnInit, Renderer2} from "@angular/core";
import {Router} from "@angular/router";
import {AppSessionService} from "../../../../common/services/app.session.service";
import {ToastrService} from "ngx-toastr";
import {LoopBackAuth} from "../../../../shared/sdk/services/core";
import {IntraMultiOperationApi, IntraMultiSinistreApi, PeopleApi} from "../../../../shared/sdk/services/custom";
import {Observable, of} from "rxjs";
import {TableColumnInterface} from "../../../shared/interfaces/table.column.interface";
import {
  IntraMultiOperation, People,
} from "../../../../shared/sdk/models";
import {AppGlobalEventManagerService} from "../../../../common/services/app.globalEventManager.service";
import {ROLES} from "../../../../datas/roles.helpers";
import {NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {AppCacheService} from "../../../../common/services/app.cache.service";
import {NgxSpinnerService} from "ngx-spinner";

import moment from 'moment';
import 'moment/min/locales';
import {DashboardInterface} from "../../../Admin/interfaces/dashboard.interface";
import {positionAssurOptions} from "../../models/constsDO";
import {PeopleWithrightsInterface} from "../../../../common/interfaces/peopleWithrights.interface";
import {SinistreDos, sinistresDosReportingMensuelInterface} from "../reporting-mensuel/reportingMensuel.page";
import {JplusBlocked, JplusWarning} from "../../models/JPlus";
moment.locale('fr');

@Component({
  selector: 'ReportingMensuelSoloPage',
  templateUrl: './reportingMensuelSolo.page.html'
})
export class ReportingMensuelSoloPage implements OnInit {

  public operations$: Observable<sinistresDosReportingMensuelInterface>;
  public operationsFiltred$: Observable<SinistreDos[]>;
  public operationssubscribed: SinistreDos[] = [];

  public positionAssurOptions: Array<{key:string|number, text:string}> = positionAssurOptions;

  public columns: Array<TableColumnInterface>;
  public hiddenColumns;

  public user: PeopleWithrightsInterface;
  public ROLES = ROLES;

  public moment = moment;

  public admins: Partial<People>[] = [];
  public adminsSelect: {key: number, societe: string, text: string}[] = []

  public stats: DashboardInterface = null;

  public listOperations:{[id:string]:IntraMultiOperation} = {};
  public allOperations:{key:any, value:any}[] = [];
  public allClients:{key:any, value:any}[] = [];
  public allAssureurs:{key:any, value:any}[] = [];
  public allStatus:{key:any, value:any, order:number}[] = [];

  public collapsed = {};
  public nbJours: number = JplusBlocked;

  public filters: Array<{column:TableColumnInterface, value:any, op:string, libelle?:string, serie?:string}> = [];
  public order: {column:TableColumnInterface, way:string, serie?:string} = {column: {name:'mdate', type:'date'}, way:'DESC'};
  public blackListFilters:string[] = [];

  public serie:string = 'reporting.mensuel';

  public userIdToLoad: number;

  constructor(
    // public activeModal: NgbActiveModal,
    public modalService: NgbModal,
    public router: Router,
    public _sessionService: AppSessionService,
    public _toastr: ToastrService,
    public spinner: NgxSpinnerService,
    public intraSinistreService: IntraMultiSinistreApi,
    public authService: LoopBackAuth,
    public intraDoOperationApiService: IntraMultiOperationApi,
    public appGlobalEventManagerService: AppGlobalEventManagerService,
    public _cacheService: AppCacheService,
    public apiService: PeopleApi,
    public globalEventManagerService :AppGlobalEventManagerService,
    public peopleApi: PeopleApi,
    public renderer: Renderer2,
) {

    this._sessionService.logguedAccountEmitter.subscribe((people) => {
      this.user = people;
    });

    this.appGlobalEventManagerService.updateTitle('Mon Reporting Mensuel des sinistres');
    this.appGlobalEventManagerService.updateBreadcrumb([{title:'Home', url:'/'}, {title:'Reporting Mensuel des mes sinistres', url:'/'}])


    this.apiService.dashboard(false).subscribe((stats: any) => {
      this.stats = stats;
    })
  }

  load(){
  }

  public nbElements = {};
  defineNbElements(elements){
    this.nbElements = elements;
  }

  loadSinistre($event:{opId:number, year:number|string, sinistreId:number}){
    if($event && $event.sinistreId && $event.opId){
      this._toastr.success('Redirection', 'Recherche de sinistre')
      this.router.navigate(['/', 'MULTInet', 'sinistre', 'fiche', $event.sinistreId, $event.opId]);
    }
  }

  ngOnInit(): void {

  }

  protected readonly JplusBlocked = JplusBlocked;
  protected readonly JplusWarning = JplusWarning;
}
