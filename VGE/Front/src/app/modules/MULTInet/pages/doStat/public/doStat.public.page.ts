import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy, OnInit, Output, QueryList, Renderer2, ViewChildren} from "@angular/core";
import {Router} from "@angular/router";
import {ToastrService} from "ngx-toastr";
import {Observable, of} from "rxjs";
import {NgbDate, NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {NgxSpinnerService} from "ngx-spinner";

import moment from 'moment';
import 'moment/min/locales';

import {
  IntraMultiEtat,
  IntraMultiExpertise,
  IntraDoHistorique,
  IntraMultiOperation, IntraMultiOperationApi, IntraMultiSinistre,
  IntraMultiSinistreApi, LoopBackAuth, People, PeopleApi,
  Societes
} from "../../../../../shared/sdk";
import {positionAssurKeysOptions, positionAssurOptions, statutsOperation} from "../../../models/constsDO";
import {PeopleWithrightsInterface} from "../../../../../common/interfaces/peopleWithrights.interface";
import {ROLES} from "../../../../../datas/roles.helpers";
import {TableColumnInterface} from "../../../../shared/interfaces/table.column.interface";
import {AppSessionService} from "../../../../../common/services/app.session.service";
import {AppGlobalEventManagerService} from "../../../../../common/services/app.globalEventManager.service";
import {AppCacheService} from "../../../../../common/services/app.cache.service";
import {chartsOptions} from "../../../models/chartStyles";
import {JplusBlocked, JplusWarning} from "../../../models/JPlus";
import {
  ActualisationMultipleModaleDirective
} from "../../../directives/modales/sinistre/actualisationMultipleModale/actualisationMultipleModale.directive";
import {flatMap, shareReplay, toArray} from "rxjs/operators";
import {StatsOperationMULTI} from "../../../interfaces/sinistresMULTI.interface";
import {AppMultiExportService} from "../../../services/app.multi.export.service";
moment.locale('fr');

export interface returnDOStatInterface{
  sinistres: sinistresDOStatInterface[],
  operations:{[id:string]:IntraMultiOperation}
  "stats": StatsOperationMULTI
}

export interface sinistresDOStatInterface{
    id: number
    num: number
    date: string
    year: number
    description: string
    etatId: number
    dateetat: string
    peopleetatId: number
    operationId: number
    enattente: number
    state: number
    avancement: number
    referencevge: string
    referenceedo: string
    referenceassur: string
    referenceeco: string
    date60: string
    date90: string
    date225: string
    positionassur: string
    datepositionassureur: string
    commentpositionassur: string
    mdate: string
    enjeu: number
    cx: number
    stateLibelle: string
    sumIndemnites: number
    sumFrais: number
    indemnites: IntraMultiExpertise[]
    // operation:{
    //   id: number
    //   libelle: string
    //   datereception: string,
    //   dossierftp?: string
    // },
    etat: IntraMultiEtat
    client:Partial<Societes>
    fond?: Partial<Societes>
    fondParent?: Partial<Societes>
    clientId?: number // Added to filter !
    fondParentId?: number
    historiques: IntraDoHistorique[],
    assureur?: string,
    surface?: string
}


@Component({
  selector: 'MultiStatPublicPage',
  templateUrl: './doStat.public.page.html'
})
export class MultiStatPublicPage implements OnInit, OnDestroy {

  public operations$: Observable<returnDOStatInterface>;
  public sinistresFiltred$: Observable<sinistresDOStatInterface[]>;

  checks = {};

  public positionAssurOptions: Array<{key:string|number, text:string}> = positionAssurOptions;

  public countSinistres: number;

  public columns: Array<TableColumnInterface>;
  public hiddenColumns;

  public user: PeopleWithrightsInterface;
  public ROLES = ROLES;

  public moment = moment;

  public admins: Partial<People>[] = [];
  public adminsSelect: {key: number, societe: string, text: string}[] = []

  public listOperations:{[id:string]:IntraMultiOperation} = {};
  public allOperations:{key:any, value:any}[] = [];
  public allClients:{key:any, value:any}[] = [];
  public allAssureurs:{key:any, value:any}[] = [];
  public allStatus:{key:any, value:any, order:number}[] = [];

  // public collapsed = {};
  public nbJours = JplusBlocked;

  public filters: Array<{column:TableColumnInterface, value:any, op:string, libelle?:string, serie?:string, elementInstigateur?:any}> = [];
  public order: {column:TableColumnInterface, way:string, serie?:string} = {column: {name:'mdate', type:'date'}, way:'DESC'};
  public blackListFilters:string[] = [];


  public idsList:number[] = [];

  public statesGlobal = { data: { labels: [], datasets: [] }};

  public statusGlobalEnCours = { data: { labels: [], datasets: [] }};
  // public statusGlobalCloture = { data: { labels: [], datasets: [] }};
  public statusGlobal = { data: { labels: [], datasets: [] }};

  // public statusYears : {[year:string]:{ data: { labels: string[], datasets: {data: any[], backgroundColor: string[], borderWidth: 1, label: ''}[] }}} = {};

  public stats:StatsOperationMULTI;

  

  public statutsOperation = statutsOperation;
  public positionAssurKeysOptions= positionAssurKeysOptions;

  @Output('majchecks') majchecks: EventEmitter<any> = new EventEmitter<any>();


  public chartsOptions = chartsOptions

  public dossiersAssureurStatus = { data: { labels: [], datasets: [] }};

  public positions = { data: { labels: [], datasets: [] }};
  public positionsAssureur = { data: { labels: [], datasets: [] }};

  public indemnitesAssureur = { data: { labels: [], datasets: [] }};
  public indemnitesClient = { data: { labels: [], datasets: [] }};
  public enjeuxClient = { data: { labels: [], datasets: [] }};

  public serie:string = 'reporting.mensuel';

  public userIdToLoad: number;

  constructor(
    // public activeModal: NgbActiveModal,
    public modalService: NgbModal,
    public router: Router,
    public _sessionService: AppSessionService,
    public _toastr: ToastrService,
    public spinner: NgxSpinnerService,
    public intraSinistreService: IntraMultiSinistreApi,
    public authService: LoopBackAuth,
    public intraDoOperationApiService: IntraMultiOperationApi,
    public appGlobalEventManagerService: AppGlobalEventManagerService,
    public _cacheService: AppCacheService,
    public apiService: PeopleApi,
    public globalEventManagerService :AppGlobalEventManagerService,
    public _appExportService: AppMultiExportService,
    public renderer: Renderer2,
    public peopleApi: PeopleApi,
) {

    this.user = this.authService.getCurrentUserData()
    this.userIdToLoad = this.user.id;
    this.appGlobalEventManagerService.updateTitle('MULTIstat, tous mes sinistres');
    this.appGlobalEventManagerService.updateBreadcrumb([{title: 'Home', url: '/'}, {
      title: 'MULTIstat, mes sinistres',
      url: '/'
    }])

  }

  public exportPDF(){
    let filter = {};
    this._appExportService.getExportMultiStat(filter, "PDF", null);
  }

  public exportXLSX(){
    let filter = {};
    this._appExportService.getExportMultiStat(filter, "XLSX", null);
  }

  public exportPDFById(){
    let filter = {};
    const list = [];
    this.sinistresFiltred$.subscribe(res => {
      res.forEach((r) => {
        list.push(r.id)
      })
    })

    filter['where'] = {
      id: {inq: list}
    }

    this._appExportService.getExportMultiStat(filter, "PDF", null);
  }

  public exportXLSXById(){
    let filter = {};
    const list = [];
    this.sinistresFiltred$.subscribe(res => {
      res.forEach((r) => {
        list.push(r.id)
      })
    })

    filter['where'] = {
      id: {inq: list}
    }

    this._appExportService.getExportMultiStat(filter, "XLSX", null);
  }


  // eRelance(){
  //   const activeModal = this.modalService.open(ActualisationMultipleModaleDirective, { size: 'xl', centered: false });
  //   activeModal.componentInstance.sinistresId = this.checks;
  //
  //   activeModal.componentInstance.clickeventAddSuiviSinistre.subscribe((res) => {
  //     if(res === true){
  //       this.checks = {};
  //     }
  //   })
  //
  // }

  checkBtn(){
    return Object.keys(this.checks).length > 0 && Object.keys(this.checks).some(e => this.checks[e] === true);
  }

  ngOnInit(): void {

  }

  public nbElements = {};
  defineNbElements(elements){
    this.nbElements = elements;
  }

  loadSinistre($event:{opId:number, year:number|string, sinistreId:number}){
    if($event && $event.sinistreId && $event.opId){
      this._toastr.success('Redirection', 'Recherche de sinistre')
      this.router.navigate(['/', 'MULTInet', 'sinistre', 'fiche', $event.sinistreId, $event.opId]);
    }
  }

  ngOnDestroy() {
    this.appGlobalEventManagerService.closeSpinner();
  }

  protected readonly JplusWarning = JplusWarning;
  protected readonly JplusBlocked = JplusBlocked;
}
