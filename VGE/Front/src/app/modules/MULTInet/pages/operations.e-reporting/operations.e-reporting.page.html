<div class="row mB20">
  <div class="col-4">

    <div>
      <h4>Exporter une synthèse sinistre</h4>
      <sinistreMultiFinder #exportSyntheseFinder (selectSinistreEmitter)="exportSynthese($event)"></sinistreMultiFinder>
    </div>
    <div *ngIf="user|hasRight:'admin'">
      <h4>Télécharger le bilan général d'un sinistre</h4>
      <sinistreMultiFinder #exportBilanFinder (selectSinistreEmitter)="exportBilan($event)"></sinistreMultiFinder>
    </div>
  </div>

  <div class="col-8 text-right">
    <button class="btn btn-danger btn-social btn-sm mL5" [routerLink]="['/MULTInet', 'operations']"><i class="ft-corner-down-left"></i> Retour sur la liste</button>
  </div>
</div>

<accordion [isAnimated]="false" [closeOthers]="true">
  <accordion-group *ngFor="let client of operationssubscribed|keyvalue; let i = index" heading="">

    <button class="btn btn-link btn-block d-flex justify-content-between" accordion-heading type="button" *ngIf="client?.value?.fonds?.length > 0">
      <div class="">{{client?.key}}</div>
      <span class="">
        <ng-container *ngIf="client?.value?.operations?.length">{{client?.value?.operations?.length}} immeubles,{{client?.value?.stats?.nbDO}} sinistres ({{client?.value?.stats?.nbEncours}} en cours, {{client?.value?.stats?.nbTermine}} terminés)</ng-container>
        <ng-container *ngIf="client?.value?.operations?.length && client?.value?.fonds?.length > 0"> & </ng-container>
        <ng-container *ngIf="client?.value?.fonds?.length > 0"> {{client?.value?.fonds?.length}} fonds ({{client?.value?.statsFondsGlobal?.nbDO}} sinistres ({{client?.value?.statsFondsGlobal?.nbEncours}} en cours, {{client?.value?.statsFondsGlobal?.nbTermine}} terminés))</ng-container></span>
    </button>

    <button class="btn btn-link btn-block d-flex justify-content-between" accordion-heading type="button" *ngIf="!(client?.value?.fonds?.length > 0)">
      <div class="">{{client?.key}}</div>
      <span class="">
        <ng-container *ngIf="client?.value?.operations?.length">{{client?.value?.operations?.length}} immeubles,{{client?.value?.statsFondsGlobal?.nbDO}} sinistres ({{client?.value?.statsFondsGlobal?.nbEncours}} en cours, {{client?.value?.statsFondsGlobal?.nbTermine}} terminés)</ng-container>
        <ng-container *ngIf="client?.value?.operations?.length && client?.value?.fonds?.length > 0"> & </ng-container>
        <ng-container *ngIf="client?.value?.fonds?.length > 0"> {{client?.value?.fonds?.length}} fonds ({{client?.value?.statsFondsGlobal?.nbDO}} sinistres ({{client?.value?.statsFondsGlobal?.nbEncours}} en cours, {{client?.value?.statsFondsGlobal?.nbTermine}} terminés))</ng-container>
      </span>
    </button>

    <!--    FONDS !!!!      -->

    <div class="row" *ngIf="canAccessEreporting || (user|hasRight:'admin')">
      <div class="col-6">
        <exportMULTIDownload [refreshButton]="true" [classDropdown]="'btn btn-sm btn-info'" [class]="'btn btn-block btn-info btn-social btn-sm'" [XLSX]="true" [label]="'Export Patrimoine ' + client?.key" [clientName]="client?.key" [typeExport]="'syntheseClient'"></exportMULTIDownload>
      </div>
      <div class="col-6">
        <exportMULTIDownload [class]="'btn btn-block btn-info btn-social btn-sm'" [XLSX]="true" [icon]="'fal fa-file-archive'" [label]="'Export E-reporting complet ' + client?.key + ' en .zip'" [clientId]="client?.value?.client?.id" [clientName]="client?.key" [typeExport]="'eReportingZipClient'" typeFile="ZIP"></exportMULTIDownload>
      </div>
    </div>

    <ng-container *ngIf="client?.value?.fonds?.length > 0">

      <accordion [isAnimated]="false" [closeOthers]="true">
        <accordion-group *ngFor="let fond of client?.value?.fonds" heading="">

          <button class="btn btn-link btn-block d-flex justify-content-between" accordion-heading type="button">
            <div class="">{{fond?.client.nom}}</div>
            <span class="">{{fond?.operations?.length}} immeubles,{{fond?.stats?.nbDO}} sinistres ({{fond?.stats?.nbEncours}} en cours, {{fond?.stats?.nbTermine}} terminés)</span>
          </button>

          <div class="row" *ngIf="canAccessEreporting || (user|hasRight:'admin')">
            <div class="col-6">
              <exportMULTIDownload [refreshButton]="true" [classDropdown]="'btn btn-block btn-info btn-sm'" [class]="'btn btn-block btn-info btn-social btn-sm'" [XLSX]="true" [label]="'Export Patrimoine ' + fond?.client.nom" [clientName]="fond?.client.nom" [typeExport]="'syntheseClient'"></exportMULTIDownload>
            </div>
            <div class="col-6">
              <exportMULTIDownload [refreshButton]="true" [classDropdown]="'btn btn-block btn-info btn-sm'" [class]="'btn btn-block btn-info btn-social btn-sm'" [XLSX]="true" [label]="'Export E-reporting complet ' + fond?.client.nom + ' en .zip'" [clientName]="fond?.client.nom" [clientId]="fond?.client.id" [typeExport]="'eReportingZipClient'" typeFile="ZIP"></exportMULTIDownload>
            </div>
          </div>


          <div class="container-table">

            <table class="table fs-11 table-compact table-hover table-bordered">
              <thead>
              <tr>
                <th class="">Nom de l'immeuble</th>
                <th>Stats sinistres</th>
                <th>Exports toutes années</th>
                <th>Exports reporting tous sinistres par années</th>
                <th class="">Exports reporting sinistres en cours par années</th>
                <th class="">Exports reporting sinistres terminés par années</th>
              </tr>
              </thead>
              <tbody>
              <ng-container *ngFor="let operation of fond?.operations; let iii = index">

                <tr *ngIf="operation?.nbDO > 0">
                  <td>
                    {{operation?.operation?.libelle}}
                  </td>
                  <td class="">
                    {{operation?.nbDO}} sinistres au total<br>
                    {{operation?.nbEncours}} sinistres en cours<br>
                    {{operation?.nbTermine}} sinistres terminés<br>
                    {{operation?.nbCX}} contentieux
                    <ng-container *ngIf="operation.operation.showGaEs">
                      <br>
                      {{operation?.nbGa}} sinistres GA<br>
                      {{operation?.nbEs}} sinistres ES
                    </ng-container>
                  </td>
                  <td class="">
                    <ng-container *ngIf="((user|hasRight:'admin') || (user|canMulti:operation?.operation?.responsablesRelations:'export') || canAccessEreporting)">
                      <exportMULTIDownload [refreshButton]="true" [classDropdown]="'btn btn-info btn-sm'" [XLSX]="true" *ngIf="operation?.nbDO > 0" [class]="'btn btn-block btn-info btn-social btn-sm'" [operationId]="operation?.operation?.id" [fonds]="true" [typeExport]="'syntheseGlobale'" [label]="'Export Général Export de la synthèse globale'"></exportMULTIDownload>
                      <exportMULTIDownload [refreshButton]="true" [classDropdown]="'btn btn-info btn-sm'" [XLSX]="true" *ngIf="operation?.nbDO > 0" [class]="'btn btn-block btn-info btn-social btn-sm'" [operationId]="operation?.operation?.id" [fonds]="true" [typeExport]="'reporting'" [label]="'Export Général de tous les dossiers'"></exportMULTIDownload>
                      <exportMULTIDownload [refreshButton]="true" [classDropdown]="'btn btn-info btn-sm'" [XLSX]="true" *ngIf="operation?.nbDO > 0" [class]="'btn btn-block btn-info btn-social btn-sm'" [operationId]="operation?.operation?.id" [fonds]="true" [typeExport]="'synoptique'" [label]="'Export Synoptique de l\'immeuble'"></exportMULTIDownload>
                      <exportMULTIDownload [refreshButton]="true" [classDropdown]="'btn btn-info btn-sm'" [XLSX]="true" *ngIf="operation?.nbDO > 0" [class]="'btn btn-block btn-info btn-social btn-sm'" [operationId]="operation?.operation?.id" [fonds]="true" [typeExport]="'generalSimplifie'" [label]="'Export général simplifié de l\'immeuble'"></exportMULTIDownload>
                    </ng-container>
                  </td>
                  <td class="">
                    <ng-container *ngIf="((user|hasRight:'admin') || (user|canMulti:operation?.operation?.responsablesRelations:'export') || canAccessEreporting)">
                      <ng-container *ngFor="let y of operation?.years">
                        <exportMULTIDownload [refreshButton]="true" [classDropdown]="'btn btn-dark btn-sm'" [XLSX]="true" *ngIf="operation?.nbDO > 0" [class]="'btn btn-block btn-dark btn-social btn-sm'" [operationId]="operation?.operation?.id" [fonds]="true" [typeExport]="'reporting'" [year]="y" [label]="'Export reporting tous sinistres '+y"></exportMULTIDownload>
                      </ng-container>
                    </ng-container>
                  </td>
                  <td class="">
                    <ng-container *ngIf="((user|hasRight:'admin') || (user|canMulti:operation?.operation?.responsablesRelations:'export') || canAccessEreporting)">
                      <ng-container *ngFor="let y of operation?.years">
                        <exportMULTIDownload [refreshButton]="true" [classDropdown]="'btn btn-warning btn-sm'" [XLSX]="true" *ngIf="operation?.nbEncours > 0" [class]="'btn btn-block btn-warning btn-social btn-sm'" [operationId]="operation?.operation?.id" [fonds]="true" [typeExport]="'reporting'" [year]="y" [state]="0" [label]="'Export reporting sinistres en cours '+y"></exportMULTIDownload>
                      </ng-container>
                    </ng-container>
                  </td>
                  <td class="">
                    <ng-container *ngIf="((user|hasRight:'admin') || (user|canMulti:operation?.operation?.responsablesRelations:'export') || canAccessEreporting)">
                      <ng-container *ngFor="let y of operation?.years">
                        <exportMULTIDownload [refreshButton]="true" [classDropdown]="'btn btn-success btn-sm'" [XLSX]="true" *ngIf="operation?.nbTermine > 0" [class]="'btn btn-block btn-success btn-social btn-sm'" [operationId]="operation?.operation?.id" [fonds]="true" [typeExport]="'reporting'" [year]="y" [state]="1" [label]="'Export reporting sinistres terminés '+y"></exportMULTIDownload>
                      </ng-container>
                    </ng-container>
                  </td>
                </tr>
              </ng-container>
              </tbody>
            </table>
          </div>
        </accordion-group>
      </accordion>

    </ng-container>


<!--    <exportMULTIDownload [class]="'btn btn-block btn-info btn-social btn-sm mB10'" [label]="'Export Général '+client?.key" [clientName]="client?.key"  [fonds]="false" [typeExport]="'syntheseClient'"></exportMULTIDownload>-->
<!--    <exportMULTIDownload *ngIf="client?.value?.fonds?.length > 0" [class]="'btn btn-block btn-primary btn-social btn-sm mB10'" [label]="'Export Général '+client?.key + ' avec fonds'" [clientName]="client?.key" [fonds]="true" [typeExport]="'syntheseClient'"></exportMULTIDownload>-->

    <ng-container *ngIf="client?.value?.operations?.length > 0">

      <div class="container-table">
        <table class="table fs-11 table-compact table-hover table-bordered">
          <thead>
            <tr>
              <th class="">Nom de l'immeuble</th>
              <th>Stats sinistres</th>
              <th>Exports toutes années</th>
              <th>Exports reporting tous sinistres par années</th>
              <th class="">Exports reporting sinistres en cours par années</th>
              <th class="">Exports reporting sinistres terminés par années</th>
            </tr>
          </thead>
          <tbody>
          <ng-container *ngFor="let operation of client?.value?.operations; let ii = index">
          <tr *ngIf="operation?.nbDO > 0">
            <td>
              {{operation?.operation?.libelle}}
            </td>
            <td class="">
              {{operation?.nbDO}} sinistres au total<br>
              {{operation?.nbEncours}} sinistres en cours<br>
              {{operation?.nbTermine}} sinistres terminés<br>
              {{operation?.nbCX}} contentieux
              <ng-container *ngIf="operation.operation.showGaEs">
                <br>
                {{operation?.nbGa}} sinistres GA<br>
                {{operation?.nbEs}} sinistres ES
              </ng-container>
            </td>
            <td class="">
              <ng-container *ngIf="((user|hasRight:'admin') || (user|canMulti:operation?.operation?.responsablesRelations:'export') || canAccessEreporting)">
                <exportMULTIDownload [refreshButton]="true" [classDropdown]="'btn btn-info btn-sm'" [XLSX]="true" *ngIf="operation?.nbDO > 0" [class]="'btn btn-block btn-info btn-social btn-sm'" [operationId]="operation?.operation?.id" [fonds]="false" [typeExport]="'syntheseGlobale'" [label]="'Export Général Export de la synthèse globale'"></exportMULTIDownload>
                <exportMULTIDownload [refreshButton]="true" [classDropdown]="'btn btn-info btn-sm'" [XLSX]="true" *ngIf="operation?.nbDO > 0" [class]="'btn btn-block btn-info btn-social btn-sm'" [operationId]="operation?.operation?.id" [fonds]="false" [typeExport]="'reporting'" [label]="'Export Général de tous les dossiers'"></exportMULTIDownload>
                <exportMULTIDownload [refreshButton]="true" [classDropdown]="'btn btn-info btn-sm'" [XLSX]="true" *ngIf="operation?.nbDO > 0" [class]="'btn btn-block btn-info btn-social btn-sm'" [operationId]="operation?.operation?.id" [fonds]="false" [typeExport]="'synoptique'" [label]="'Export Synoptique de l\'immeuble'"></exportMULTIDownload>
                <exportMULTIDownload [refreshButton]="true" [classDropdown]="'btn btn-info btn-sm'" [XLSX]="true" *ngIf="operation?.nbDO > 0" [class]="'btn btn-block btn-info btn-social btn-sm'" [operationId]="operation?.operation?.id" [fonds]="false" [typeExport]="'generalSimplifie'" [label]="'Export général simplifié de l\'immeuble'"></exportMULTIDownload>
              </ng-container>
            </td>
            <td class="">
              <ng-container *ngIf="((user|hasRight:'admin') || (user|canMulti:operation?.operation?.responsablesRelations:'export') || canAccessEreporting)">
                <ng-container *ngFor="let y of operation?.years">
                  <exportMULTIDownload [refreshButton]="true" [classDropdown]="'btn btn-dark btn-sm'" [XLSX]="true" *ngIf="operation?.nbDO > 0" [class]="'btn btn-block btn-dark btn-social btn-sm'" [operationId]="operation?.operation?.id" [fonds]="false" [typeExport]="'reporting'" [year]="y" [label]="'Export reporting tous sinistres '+y"></exportMULTIDownload>
                </ng-container>
              </ng-container>
            </td>
            <td class="">
              <ng-container *ngIf="((user|hasRight:'admin') || (user|canMulti:operation?.operation?.responsablesRelations:'export') || canAccessEreporting)">
                <ng-container *ngFor="let y of operation?.years">
                  <exportMULTIDownload [refreshButton]="true" [classDropdown]="'btn btn-warning btn-sm'" [XLSX]="true" *ngIf="operation?.nbEncours > 0" [class]="'btn btn-block btn-warning btn-social btn-sm'" [operationId]="operation?.operation?.id" [fonds]="false" [typeExport]="'reporting'" [year]="y" [state]="0" [label]="'Export reporting sinistres en cours '+y"></exportMULTIDownload>
                </ng-container>
              </ng-container>
            </td>
            <td class="">
              <ng-container *ngIf="((user|hasRight:'admin') || (user|canMulti:operation?.operation?.responsablesRelations:'export') || canAccessEreporting)">
                <ng-container *ngFor="let y of operation?.years">
                  <exportMULTIDownload [refreshButton]="true" [classDropdown]="'btn btn-success btn-sm'" [XLSX]="true" *ngIf="operation?.nbTermine > 0" [class]="'btn btn-block btn-success btn-social btn-sm'" [operationId]="operation?.operation?.id" [fonds]="false" [typeExport]="'reporting'" [year]="y" [state]="1" [label]="'Export reporting sinistres terminés '+y"></exportMULTIDownload>
                </ng-container>
              </ng-container>
            </td>
          </tr>
          </ng-container>
          </tbody>
        </table>
      </div>
    </ng-container>
  </accordion-group>
</accordion>

