import moment from "moment";
import {<PERSON>mpo<PERSON>, <PERSON><PERSON>nit, Renderer2} from "@angular/core";
import {Ngb<PERSON>ate, NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {Router} from "@angular/router";
import {AppSessionService} from "../../../../common/services/app.session.service";
import {ToastrService} from "ngx-toastr";
import {NgxSpinnerService} from "ngx-spinner";
import {
  IntraMultiOperation,
  IntraMultiOperationApi,
  IntraMultiSinistreApi,
  LoopBackAuth,
  People,
  PeopleApi
} from "../../../../shared/sdk";
import {AppGlobalEventManagerService} from "../../../../common/services/app.globalEventManager.service";
import {AppCacheService} from "../../../../common/services/app.cache.service";
import {Observable} from "rxjs";
import {SinistreDos} from "../reporting-mensuel/reportingMensuel.page";
import {TableColumnInterface} from "../../../shared/interfaces/table.column.interface";
import {PeopleWithrightsInterface} from "../../../../common/interfaces/peopleWithrights.interface";
import {DashboardInterface} from "../../../Admin/interfaces/dashboard.interface";
import {ROLES} from "../../../../datas/roles.helpers";
import {positionAssurOptions} from "../../models/constsDO";
import {FormBuilder, FormGroup, Validators} from "@angular/forms";
import {JplusBlocked, JplusWarning} from "../../models/JPlus";
import {
  ActualisationMultipleModaleDirective
} from "../../directives/modales/sinistre/actualisationMultipleModale/actualisationMultipleModale.directive";

moment.locale('fr');

@Component({
  selector: 'OperationsGlobalCollaborateursPage',
  templateUrl: './operations-global-collaborateurs.page.html'
})
export class OperationsGlobalCollaborateursPage implements OnInit {

  public operations$: Observable<any>;
  public operationsFiltred$: Observable<any[]>;
  public operationssubscribed: SinistreDos[] = [];

  public positionAssurOptions: Array<{key:string|number, text:string}> = positionAssurOptions;

  public columns: Array<TableColumnInterface>;
  public hiddenColumns;

  public user: PeopleWithrightsInterface;
  public ROLES = ROLES;

  public moment = moment;

  public admins: Partial<People>[] = [];
  public adminsSelect: {key: number, societe: string, text: string}[] = []

  public stats: DashboardInterface = null;

  public listOperations:{[id:string]:IntraMultiOperation} = {};
  public allOperations:{key:any, value:any}[] = [];
  public allClients:{key:any, value:any}[] = [];
  public allAssureurs:{key:any, value:any}[] = [];
  public allStatus:{key:any, value:any, order:number}[] = [];

  public collapsed = {};
  public nbJours: number = JplusWarning;

  checks = {};


  public filters: Array<{column:TableColumnInterface, value:any, op:string, libelle?:string, serie?:string}> = [];
  public order: {column:TableColumnInterface, way:string, serie?:string} = {column: {name:'mdate', type:'date'}, way:'DESC'};
  public blackListFilters:string[] = [];

  public serie:string = 'reporting.mensuel';

  public userIdToLoad: number;
  public collaborateur: Partial<People>;

  public datasGlobales

  public forms: {[id:number]: FormGroup} = {};

  constructor(
    // public activeModal: NgbActiveModal,
    public modalService: NgbModal,
    public router: Router,
    public _sessionService: AppSessionService,
    public _toastr: ToastrService,
    public spinner: NgxSpinnerService,
    public intraSinistreService: IntraMultiSinistreApi,
    public authService: LoopBackAuth,
    public intraDoOperationApiService: IntraMultiOperationApi,
    public appGlobalEventManagerService: AppGlobalEventManagerService,
    public _cacheService: AppCacheService,
    public apiService: PeopleApi,
    public globalEventManagerService :AppGlobalEventManagerService,
    public peopleApi: PeopleApi,
    public renderer: Renderer2,
    public formBuilder: FormBuilder,
  ) {

    this.appGlobalEventManagerService.openSpinner();

    this.user = this.authService.getCurrentUserData()
    this.userIdToLoad = this.user.id;

    this.appGlobalEventManagerService.updateTitle('Reporting global des collaborateurs');
    this.appGlobalEventManagerService.updateBreadcrumb([{title:'Home', url:'/'}, {title:'Reporting global des collaborateurs', url:'/'}])

    this.loadCollaborateurs()

    this.apiService.dashboard(false).subscribe((stats: any)=>{
      this.stats = stats;
    })

  }

  checkBtn(){
    return Object.keys(this.checks).length > 0 && Object.keys(this.checks).some(e => this.checks[e] === true);
  }

  eRelance(){
    const activeModal = this.modalService.open(ActualisationMultipleModaleDirective, { size: 'xl', centered: false });
    activeModal.componentInstance.sinistresId = this.checks;

    activeModal.componentInstance.clickeventAddSuiviSinistre.subscribe((res) => {
      if(res === true){
        this.checks = {};
      }
    })

  }

  loadCollaborateurs(){

    this.intraDoOperationApiService.sauronGlobalDossiers().subscribe((res) => {
      this.datasGlobales = res;

      res.forEach((r) => {

        let date = null;
        if(r.dateNoLimitAG){
          date = moment(r.dateNoLimitAG, 'YYYY-MM-DD').toObject();
        }
        this.forms[r.id] = this.formBuilder.group({
          dateNoLimitAG: [(date) ? {year:date.years, month:date.months+1, day: date.date} : '', []],
        })
      })
      this.appGlobalEventManagerService.closeSpinner()
    })

    this.peopleApi.getCollaborateurs().subscribe((adminsfromDB: Partial<People>[]) => {

      this.admins = adminsfromDB;

      this.adminsSelect = [];

      if (adminsfromDB && adminsfromDB.length > 0) {
        adminsfromDB.forEach(admin => {

          if (admin.isVGE) {
            this.adminsSelect.push({
              key: admin.id,
              societe: 'VGE',
              text: `VGE (${admin.firstname} ${admin.lastname})`
            });
          }

          if (admin.isVGI) {
            this.adminsSelect.push({
              key: admin.id,
              societe: 'VGI',
              text: `VGI (${admin.firstname} ${admin.lastname})`
            });
          }

          if (admin.isVGS) {
            this.adminsSelect.push({
              key: admin.id,
              societe: 'VGS',
              text: `VGS (${admin.firstname} ${admin.lastname})`
            });
          }

        })
      }
    })
  }

  setDate(el: Partial<People>, form){
    let dateToSave = null;
    if(form){
      const values = form.getRawValue();
      if(values.dateNoLimitAG !== null){
        dateToSave = moment({years: values.dateNoLimitAG.year, months: values.dateNoLimitAG.month -1, days: values.dateNoLimitAG.day, hours: 12});
      }
    }

    this.peopleApi.patchAttributes(el.id, {
      dateNoLimitAG: dateToSave
    }).subscribe((res) => {
      this.appGlobalEventManagerService.openSpinner();
      this.loadCollaborateurs()
    })
  }


  /**
   * Filter Natural inside subscribed
   */
  public filter(){

  }

  selectCollaborateur(collaborateur){
    this.collaborateur = collaborateur;
  }

  loadSinistre($event:{opId:number, year:number|string, sinistreId:number}){
    if($event && $event.sinistreId && $event.opId){
      this._toastr.success('Redirection', 'Recherche de sinistre')
      this.router.navigate(['/', 'MULTInet', 'sinistre', 'fiche', $event.sinistreId, $event.opId]);
    }
  }

  /*
   du type : {
    J27: X
    J35: X
   }
   */
  public nbElements = {};
  defineNbElements(elements){
    this.nbElements = elements;
  }


  ngOnInit() {
  }

  protected readonly focus = focus;
  protected readonly JplusBlocked = JplusBlocked;
  protected readonly JplusWarning = JplusWarning;
}
