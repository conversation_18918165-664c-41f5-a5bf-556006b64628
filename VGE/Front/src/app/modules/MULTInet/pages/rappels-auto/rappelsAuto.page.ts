import {Component, OnInit, ViewChild} from "@angular/core";
import {Router} from "@angular/router";
import {AppSessionService} from "../../../../common/services/app.session.service";
import {ToastrService} from "ngx-toastr";
import {LoopBackAuth} from "../../../../shared/sdk";
import {IntraDoHistoriqueApi} from "../../../../shared/sdk";
import {Observable, of} from "rxjs";
import {TableColumnInterface} from "../../../shared/interfaces/table.column.interface";
import {IntraDoHistorique, LoopBackFilter, People} from "../../../../shared/sdk";
import {shareReplay} from "rxjs/operators";
import {AppGlobalEventManagerService} from "../../../../common/services/app.globalEventManager.service";
import {ROLES} from "../../../../datas/roles.helpers";
import {NgbDate, NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {AppCacheService} from "../../../../common/services/app.cache.service";
import {NgxSpinnerService} from "ngx-spinner";

import moment from 'moment';
import 'moment/min/locales';
import {UpdateRappelAutoModaleDirective} from "../../directives/modales/historique/updateRappelAutoModale/updateRappelAutoModale.directive";
import {SelftablepaginateDirective} from "../../../../directives/tables/selftablepaginate/selftablepaginate.directive";
import {filterBuilder} from "../../../shared/models/filterBuilder";
import {NgSelectComponent} from "@ng-select/ng-select";
import {PeopleWithrightsInterface} from "../../../../common/interfaces/peopleWithrights.interface";
moment.locale('fr');



@Component({
  selector: 'RappelsAutoPage',
  templateUrl: './rappelsAuto.page.html'
})
export class RappelsAutoPage implements OnInit {


  public elements$: Observable<IntraDoHistorique[]>;
  public elementsSubscribed: IntraDoHistorique[] = [];


  public columns: TableColumnInterface[];
  public hiddenColumns;

  public moment = moment;

  public checkFilterValue: boolean = false;

  public filtre: LoopBackFilter = {};

  @ViewChild('list', {static:false}) declare table : SelftablepaginateDirective
  public serie:string = 'factures.list';

  public user: PeopleWithrightsInterface;
  public ROLES = ROLES;

  public listOp$: Observable<{key: string; value: string}[]> = new Observable(null);
  public listRefs$: Observable<{key: string; value: string}[]> = new Observable(null);

  @ViewChild('opSelect', {static:false}) opSelect!: NgSelectComponent
  @ViewChild('refSelect', {static:false}) refSelect!: NgSelectComponent

  constructor(
    public router: Router,
    public _sessionService: AppSessionService,
    public spinner: NgxSpinnerService,
    public _toastr: ToastrService,
    public authService: LoopBackAuth,
    public apiService: IntraDoHistoriqueApi,
    public modalService: NgbModal,
    public _cacheApiService: AppCacheService,
    public appGlobalEventManagerService: AppGlobalEventManagerService
  ) {

    this._sessionService.logguedAccountEmitter.subscribe((people) => {
      this.user = people;
    });

    this.filtre.where = {};

    /** Titles & Breadcrumbs */
    this.appGlobalEventManagerService.updateTitle('Liste des rappels auto actifs');
    this.appGlobalEventManagerService.updateBreadcrumb([{title:'Home', url:'/'}, {title:'Liste des opérations MULTInet', url:'/MULTInet/operations'}, {title:'Rappels auto', url:'/'}])

    this.columns =
      [
        {key: 'op', name:"Opération", type: 'input', order:true, filter:true},
        {key: 'ref', name:"Sinistre", type: 'input', order:false, filter:false},
        {key: 'desc', name:"Desc. Sinistre", type: 'input', order:false, filter:true},
        {key: 'date', name:"Date", type: 'date', order:true, filter:true},
      ];
    this.load();

  }

  callbackFiltred(filtre:{filters: Array<{column: TableColumnInterface, value: any, op: string}>, elements: any[]}){
    console.log(filtre)
  }


  load(){
    this.appGlobalEventManagerService.openSpinner();

    this.elements$ = <Observable<IntraDoHistorique[]>>this.apiService.getRappelsAuto().pipe(shareReplay(1));

    const ops = [];
    const refs = [];
    this.elements$.subscribe((elements:IntraDoHistorique[]) => {

      // Add attributes to current object to filter on it
      elements.forEach((h) => {
        h['op'] = h.sinistre.operation.libelle;
        h['ref'] = h.sinistre.referencevge;
        h['desc'] = h.sinistre.description;

        if(ops.findIndex((e) => e.key === h.sinistre.operation.libelle) === -1){ops.push({key: h.sinistre.operation.libelle, value: h.sinistre.operation.libelle})}
        if(refs.findIndex((e) => e.key === h.sinistre.referencevge) === -1){refs.push({key: h.sinistre.referencevge, value: h.sinistre.referencevge})}

        this.listOp$ = of(ops);
        this.listRefs$ = of(refs);

      })

      this.elementsSubscribed = elements;

      setTimeout(() => {
        this.appGlobalEventManagerService.closeSpinner();
      }, 600)
    });

  }

  /**
   * AddFilter
   * @param column
   * @param value
   */
  addFilter(column: TableColumnInterface, value, operateur="equal"){

    if(value && value.value && value.key){
      this.table.addFilter(column, value.key, operateur, value.value, this.serie);
    }else{
      if(column.key == 'num'){
        if(value.indexOf(',') > -1){
          this.table.addFilter(column, value.split(','), 'in', null, this.serie);
        }else{
          this.table.addFilter(column, value, operateur, null, this.serie);
        }
      }else{
        this.table.addFilter(column, value, operateur, null, this.serie);
      }

    }
  }

  callBackResetFilterField($event: TableColumnInterface){
    // Here on supprima la value dans le select, input ... des filtres !
    this.table.clearFilter($event, this.serie, false)
    this.table.filter();
    if(this[$event.key + 'Select']){
      //this[$event.key + 'Select'].clearModel()
    }
  }


  clearFilter(column: TableColumnInterface){
    this.table.clearFilter(column);
  }

  // Filtre sur API !
  // filter(value: string, $event, operator="like") {
  //   if(!this.filtre || !this.filtre['where']){ this.filtre['where'] = {} } // init
  //   if(value && $event){
  //     if($event.target && $event.target.value){
  //       this.filtre['where'] = filterBuilder.buildTerm(value, $event.target.value, operator) ;
  //     } else { // cas du select !
  //       this.filtre['where'] = filterBuilder.buildTerm(value, $event, operator) ;
  //     }
  //   }
  //   this.load();
  // }

  ngOnInit(): void {
  }

  openModaleChangeSuivi(historique:IntraDoHistorique, index){
    const activeModal = this.modalService.open(UpdateRappelAutoModaleDirective, { size: 'xl', centered: false });
    activeModal.componentInstance.suivi = historique;
    activeModal.componentInstance.clickeventUpdateSuivi.subscribe((ret:IntraDoHistorique) => {
      if (ret) {
        const histoIndex = this.elementsSubscribed.findIndex(h => h.id === historique.id);
        if(histoIndex > -1){
          // Replace Here !
          this.elementsSubscribed[histoIndex] = ret;
        }
        activeModal.close();
      }else{
        this._toastr.error('Une erreur s\'est produite', 'Problème');
      }
    });
  }

}
