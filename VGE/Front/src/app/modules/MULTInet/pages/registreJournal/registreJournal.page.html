<div class="content-body">
  <div class="row" *ngIf="(user|hasRight:'admin') || (user|canMulti:operation?.responsablesRelations:'see')">
    <div class="col-12">
      <a class="btn btn-social btn-block mb-1 btn-soundcloud text-center" [routerLink]="['/MULTInet', 'sinistres', ido]">
        <span class="ft-chevron-right"></span><strong>Accéder aux sinistres de l'opération {{operation?.libelle}} </strong>
      </a>
    </div>
  </div>

  <div class="row">
    <div class="col-12 text-center mb-1">

      <exportMULTIDownload
        *ngIf="(commentaires$ | async)?.length > 0"
        [class]="'btn btn-blue btn-social btn-sm mL5 mR5'"
        [label]="'Exporter le registre MULTInet'"
        [typeExport]="'registreJournal'"
        [refresh]="true"
        [operationId]="operation?.id"
      ></exportMULTIDownload>
      <button type="button" *ngIf="ido" [routerLink]="['/MULTInet', 'registre-du-journal', 'ajout', 'new', ido]" class="btn btn-success btn-social btn-sm mR5"><i class="ft-plus"></i> Poser une question d'ordre général à VGE</button>
      <button type="button" *ngIf="ido && ((user|hasRight:'admin') || (user|canMulti:operation?.responsablesRelations:'add'))" [routerLink]="['/MULTInet', 'sinistre', 'new', ido]" class="btn btn-danger btn-social btn-sm"><i class="ft-plus"></i> Déclarer un sinistre</button>

    </div>
  </div>

  <section id="timeline" class="timeline-left timeline-wrapper">
    <ul class="timeline mt-3">
      <li class="timeline-line"></li>

      <li class="timeline-item mt-1 pB0" *ngIf="(commentaires$ | async)?.length == 0">
        <div class="timeline-badge"></div>
        <div class="timeline-card card border-grey border-lighten-2">
          <div class="card-header">
            <div class="media">
              <div class="media-left">
              </div>
              <div class="media-body ml-1">
                <h4 class="card-title">Aucun commentaire</h4>
              </div>
            </div>
          </div>
        </div>
      </li>

      <li class="timeline-item mt-1 pB0" *ngFor="let object of (commentaires$ | async)">

        <div class="timeline-badge">
          <span class="bg-darken-1" [ngClass]="{'bg-success':object.comment, 'bg-primary':object?.photo, 'bg-secondary':object?.file}">
            <i class="" [ngClass]="{'ft-message-circle':object.comment, 'ft-image':object?.photo, 'ft-file':object?.file}"></i>
          </span>
        </div>
        <div class="timeline-card card border-grey border-lighten-2">

          <div class="card-header" *ngIf="object?.people?.societes && object?.people?.societes.length > 0">
            <div class="media">
              <div class="media-left">
                <a href="javascript:void(0)">
                  <span class="avatar avatar-md avatar-busy"><img [src]="object?.people?.societes[0].image" alt=""></span>
                  <i></i>
                </a>
              </div>
              <div class="media-body ml-1">
                <h4 class="card-title"><a href="javascript:void(0)"><people [people]="object?.people"></people></a></h4>
                <p class="card-subtitle text-muted mb-0 pt-1">
                  <span class="font-small-3">{{object.date|momentDateFormat:'YYYY-MM-DD HH:mm:ss':'LLLL'}}</span>
                  <span class="badge badge-pill badge-success float-right" *ngIf="object.comment">Commentaire</span>
                  <span class="badge badge-pill badge-primary float-right" *ngIf="object?.photo">Photo</span>
                  <span class="badge badge-pill badge-secondary float-right" *ngIf="object?.file">Fichier</span>
                </p>
              </div>
            </div>
          </div>

          <div class="card-header" *ngIf="!object?.people?.societes || object?.people?.societes.length ==0">
            <h4 class="card-title"><a href="javascript:void(0)"><people [people]="object?.people"></people></a></h4>
            <p class="card-subtitle text-muted mb-0 pt-1">
              <span class="font-small-3">{{object.date|momentDateFormat:'YYYY-MM-DD HH:mm:ss':'LLLL'}}</span>
            </p>
          </div>

          <div class="card-content">
            <div class="card-content">
              <div class="card-body py-0 pb-1">
                <div class="card-text pD15 pL15 preventDisplay" [innerHTML]="object.comment"></div>

                <ng-container *ngIf="object?.file">
                  <fileDownload [label]="(object?.file|getFilename).filename" [type]="'file'" [lien]="object?.file" [icon]="'ft-file'" [class]="'btn btn-outline-info'"></fileDownload>
                </ng-container>

                <ng-container *ngIf="object?.photo">
                  <fileDownload [label]="(object?.photo|getFilename).filename" [type]="'image'" [tooltip]="'Télécharger la photo'" [lien]="object?.photo" [icon]="'ft-file'" [class]="'btn btn-outline-info'"></fileDownload>
                </ng-container>

              </div>
            </div>
            <div class="card-footer px-0 py-0" *ngIf="(user|hasRight:'admin') || (object?.peopleId === user?.id)">
              <div class="card-body">
                <button type="submit"
                        class="btn btn-danger btn-sm btn-social"
                        mwlConfirmationPopover
                        [popoverTitle]="'Suppression de commentaire'"
                        [popoverMessage]="'Êtes vous sur de vouloir supprimer ce commentaire ?'"
                        placement="right"
                        (confirm)="delete(object)"
                >
                  <i class="ft-trash-2"></i> Supprimer le commentaire
                </button>
              </div>
            </div>
          </div>
        </div>
      </li>
    </ul>
  </section>

</div>
