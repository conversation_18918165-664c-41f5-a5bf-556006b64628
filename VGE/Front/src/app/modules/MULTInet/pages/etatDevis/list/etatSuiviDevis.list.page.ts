import {Component, OnInit} from "@angular/core";
import {Router} from "@angular/router";
import {AppSessionService} from "../../../../../common/services/app.session.service";
import {ToastrService} from "ngx-toastr";
import {LoopBackAuth} from "../../../../../shared/sdk/services/core";
import {Observable} from "rxjs";
import {IntraMultiDevisCategorieApi} from "../../../../../shared/sdk/services/custom";
import {TableColumnInterface} from "../../../../shared/interfaces/table.column.interface";
import {share} from "rxjs/operators";
import {ListPageInterface} from "../../../../shared/interfaces/list.page.interface";
import {AppGlobalEventManagerService} from "../../../../../common/services/app.globalEventManager.service";
import {LoopBackFilter} from "../../../../../shared/sdk";

@Component({
  selector: 'EtatSuiviDevisListPage',
  templateUrl: './etatSuiviDevis.list.page.html'
})
export class EtatSuiviDevisListPage implements OnInit, ListPageInterface {

  public etatsSuiviSinistre$: Observable<any> = new Observable<any>();
  public columns: Array<TableColumnInterface>;
  public hiddenColumns;
  public filtre: LoopBackFilter = {};

  constructor(
    public router: Router,
    public _sessionService: AppSessionService,
    public _toastr: ToastrService,
    public authService: LoopBackAuth,
    public intraDoEtatSuiviApiService: IntraMultiDevisCategorieApi,
    public appGlobalEventManagerService: AppGlobalEventManagerService
  ) {

    /** Titles & Breadcrumbs */
    this.appGlobalEventManagerService.updateTitle('Liste des états suivis sinistres MULTInet');
    this.appGlobalEventManagerService.updateBreadcrumb([{title:'Home', url:'/'}, {title:'Liste des états suivis sinistres MULTInet', url:null}])

    /** Default columns  **/
    // id	Couleur	Libelle	Stop	Process	Ordre du process
    this.columns =
      [
        {key: 'couleur', name:"Couleur", type: 'color', order:false, filter:false},
        {key: 'libelle', name:"Nom", type: 'link', order:false, filter:false},
        {key: 'ordre', name:"Ordre", type: 'order', order:true, filter:false},
      ];
    this.load();

  }

  load() {
    this.etatsSuiviSinistre$ = this.intraDoEtatSuiviApiService.find({order:'ordre ASC'}).pipe(share());

  }

  delete(object: any) {
    if (object && object.id) {
      this.intraDoEtatSuiviApiService.deleteById(object.id).subscribe(
        (res) => {
          this._toastr.success('Suppression de l\'état suivi', 'L\'état suivi a été supprimé avec succès');
          this.load(); // Refresh list
        },
        (err) => {this._toastr.success('Suppression de l\'état suivi', err.message); }
      );
    }
  }

  filter(value: string, $event) {

  }

  ngOnInit(): void {
  }

}
