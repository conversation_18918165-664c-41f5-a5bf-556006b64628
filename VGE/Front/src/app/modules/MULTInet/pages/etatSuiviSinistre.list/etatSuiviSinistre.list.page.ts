import {Component, OnInit} from "@angular/core";
import {Router} from "@angular/router";
import {AppSessionService} from "../../../../common/services/app.session.service";
import {ToastrService} from "ngx-toastr";
import {LoopBackAuth} from "../../../../shared/sdk/services/core";
import {Observable} from "rxjs";
import {IntraMultiEtatSuiviApi, IntraEtatApi} from "../../../../shared/sdk/services/custom";
import {TableColumnInterface} from "../../../shared/interfaces/table.column.interface";
import {share} from "rxjs/operators";
import {ListPageInterface} from "../../../shared/interfaces/list.page.interface";
import {LoopBackFilter} from "../../../../shared/sdk/models";
import {AppGlobalEventManagerService} from "../../../../common/services/app.globalEventManager.service";

@Component({
  selector: 'EtatSuiviSinistreListPage',
  templateUrl: './etatSuiviSinistre.list.page.html'
})
export class EtatSuiviSinistreListPage implements OnInit, ListPageInterface {

  public etatsSuiviSinistre$: Observable<any> = new Observable<any>();
  public columns: Array<TableColumnInterface>;
  public hiddenColumns;
  public filtre: LoopBackFilter = {};

  constructor(
    public router: Router,
    public _sessionService: AppSessionService,
    public _toastr: ToastrService,
    public authService: LoopBackAuth,
    public intraDoEtatSuiviApiService: IntraMultiEtatSuiviApi,
    public appGlobalEventManagerService: AppGlobalEventManagerService
  ) {

    /** Titles & Breadcrumbs */
    this.appGlobalEventManagerService.updateTitle('Liste des états suivis sinistres MULTInet');
    this.appGlobalEventManagerService.updateBreadcrumb([{title:'Home', url:'/'}, {title:'Liste des états suivis sinistres MULTInet', url:null}])

    /** Default columns  **/
    // id	Couleur	Libelle	Stop	Process	Ordre du process
    this.columns =
      [
        {key: 'couleur', name:"Couleur", type: 'color', order:false, filter:false},
        {key: 'libelle', name:"Nom", type: 'link', order:false, filter:false},
        {key: 'order', name:"Ordre du process", type: 'order', explain:'Ordre des process généraux, très important pour permettre le déroulement des changements de statut des réclamations', order:true, filter:false},
      ];
    this.load();

  }

  load() {
    this.etatsSuiviSinistre$ = this.intraDoEtatSuiviApiService.find({order:'order ASC'}).pipe(share());

  }

  delete(object: any) {
    if (object && object.id) {
      this.intraDoEtatSuiviApiService.deleteById(object.id).subscribe(
        (res) => {
          this._toastr.success('Suppression de l\'état suivi', 'L\'état suivi a été supprimé avec succès');
          this.load(); // Refresh list
        },
        (err) => {this._toastr.success('Suppression de l\'état suivi', err.message); }
      );
    }
  }

  filter(value: string, $event) {

  }

  ngOnInit(): void {
  }

}
