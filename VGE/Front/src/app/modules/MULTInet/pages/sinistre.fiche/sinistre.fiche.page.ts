import {ChangeDetectorRef, Component, Inject, OnDestroy, OnInit, ViewChild} from '@angular/core';
import {ActivatedRoute, Router} from '@angular/router';
import {AppSessionService} from '../../../../common/services/app.session.service';
import {ToastrService} from 'ngx-toastr';
import {LoopBackAuth} from '../../../../shared/sdk/services/core';
import {combineLatest, forkJoin, Observable, of} from 'rxjs';
import {
  IntraMultiDevis,
  IntraMultiEtat,
  IntraMultiEtatSuivi,
  IntraMultiExpertise,
  IntraMultiFiles,
  IntraDoFraisavances,
  IntraDoHistorique,
  IntraDoHistoriqueExt,
  IntraMultiOperation,
  IntraMultiPhotos,
  IntraMultiSinistre,
  IntraMultiSinistreDocumentation,
  IntraMultiSinistreExtension,
  IntraEbuzLigneFacture,
  IntraEbuzLigneFactureApi,
  LoopBackFilter,
  MailgunDb,
  People,
  Societes,
  IntraMultiFraisavances,
  IntraMultiHistorique,
  IntraDoDevis,
  IntraDoDevisFacture,
  IntraMultiDevisFacture,
  IntraMultiSinistreDocumentationApi,
  IntraMultiSinistreDocumentationCategorieApi,
  IntraMultiSinistreDocumentationCategorie
} from "../../../../shared/sdk";
import {
  IntraMultiDevisApi, IntraMultiExpertiseApi, IntraDoFraisavancesApi,
  IntraDoHistoriqueApi,
  IntraMultiOperationApi,
  IntraMultiSinistreApi, IntraMultiSinistreExtensionApi,
  IntraReclamationApi
} from "../../../../shared/sdk";
import {TableColumnInterface} from "../../../shared/interfaces/table.column.interface";
import {share, shareReplay} from "rxjs/operators";
import {AppGlobalEventManagerService} from "../../../../common/services/app.globalEventManager.service";
import {AppContainerService} from "../../../../common/services/app.container.service";
import {NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {FormBuilder, FormGroup, Validators} from "@angular/forms";
import {ROLES, usersVieira} from "../../../../datas/roles.helpers";
import moment from 'moment';
import {AppCacheService} from '../../../../common/services/app.cache.service';
import {ChangeEtatSinistreModaleDirective} from '../../directives/modales/sinistre/changeEtatSinistreModale/changeEtatSinistreModale.directive';
import {statusIndemniteOptions, statutIndemniteIndemnite} from '../../models/constsDO';
import {ChangeReferencesModaleDirective} from '../../directives/modales/sinistre/changeReferencesModale/changeReferencesModale.directive';
import {ChangeReferenceModaleDirective} from '../../directives/modales/sinistre/changeReferenceModale/changeReferenceModale.directive';
import {ChangeLibelleModaleDirective} from '../../directives/modales/sinistre/changeLibelleModale/changeLibelleModale.directive';
import {ChangePositionAssurModaleDirective} from '../../directives/modales/sinistre/changePositionAssurModale/changePositionAssurModale.directive';
import {ChangeAvancementModaleDirective} from '../../directives/modales/sinistre/changeAvancementModale/changeAvancementModale.directive';
import {SinistreChangeStatutModaleDirective} from '../../directives/modales/sinistre/changeStatutModale/sinistreChangeStatutModale.directive';
import {ChangeEnjeuModaleDirective} from '../../directives/modales/sinistre/changeEnjeuModale/changeEnjeuModale.directive';
import {AjoutSuiviModaleDirective} from '../../directives/modales/sinistre/ajoutSuiviModale/ajoutSuiviModale.directive';
import {ChangeDateDesignationExpertMultiModaleDirective} from '../../directives/modales/sinistre/changeDateDesignationExpertModale/changeDateDesignationExpertModale.directive';
import {AjoutExtensionModaleDirective} from '../../directives/modales/sinistre/ajoutExtensionModale/ajoutExtensionModale.directive';
import {ChangeSuiviModaleDirective} from '../../directives/modales/historique/updateHistoriqueModale/changeSuiviModale.directive';
import {ChangeReferencesExtensionModaleDirective} from '../../directives/modales/extension/changeReferencesModale/changeReferencesModale.directive';
import {ChangeLibelleModaleExtensionDirective} from '../../directives/modales/extension/changeLibelleModale/changeLibelleModale.directive';
import {ChangePositionAssurExtensionModaleDirective} from '../../directives/modales/extension/changePositionAssurModale/changePositionAssurModale.directive';
import {ChangeAvancementExtensionModaleDirective} from '../../directives/modales/extension/changeAvancementModale/changeAvancementModale.directive';
import {ChangeCourrierInteruptifExtensionModaleDirective} from '../../directives/modales/extension/changeCourrierInteruptifModale/changeCourrierInteruptifModale.directive';
import {ChangeDateDesignationExpertExtensionModaleDirective} from '../../directives/modales/extension/changeDateDesignationExpertModale/changeDateDesignationExpertModale.directive';
import {SinistreChangeStatutExtensionModaleDirective} from '../../directives/modales/extension/changeStatutModale/sinistreChangeStatutModale.directive';
import {ChangeDossierFTPModaleDirective} from '../../directives/modales/sinistre/changeDossierFTPModale/changeDossierFTPModale.directive';
import {ChangeDateModaleDirective} from '../../directives/modales/sinistre/changeDateModale/changeDateModale.directive';
import {ChangeDateModaleExtensionDirective} from '../../directives/modales/extension/changeDateModale/changeDateModale.directive';
import {IndemniteModaleDirective} from '../../directives/modales/indemnite/indemniteModale.directive';
import {DevisModaleDirective} from '../../directives/modales/devis/devisModale.directive';
import {ImageFormDirective} from '../../../../directives/form/image/image.form.directive';
import {FileFormDirective} from '../../../../directives/form/file/file.form.directive';
import {ChangeBilanFTPModaleDirective} from '../../directives/modales/sinistre/changeBilanFTP/changeBilanFTPModale.directive';
import {ChangeBilanFinancierModaleDirective} from '../../directives/modales/sinistre/changeBilanFinancier/changeBilanFinancierModale.directive';
import {FraisModaleDirective} from "../../directives/modales/frais/fraisModale.directive";
import {DOCUMENT} from '@angular/common';
import {PageScrollService} from 'ngx-page-scroll-core';
import {DomSanitizer, SafeResourceUrl} from "@angular/platform-browser";
import {apiParameterInterface} from "../../../shared/interfaces/apiParameter.interface";
import {
  ChangeBooleanModaleDirective
} from "../../directives/modales/sinistre/changeBooleanModale/changeBooleanModale.directive";
import {
  ChangeAnnuiteFacturationModaleDirective
} from "../../directives/modales/sinistre/changeAnnuiteFacturationModale/changeAnnuiteFacturationModale.directive";
import {
  ChangeGarantieNonContestableModaleDirective
} from "../../directives/modales/sinistre/changeGarantieNonContestableModale/changeGarantieNonContestableModale.directive";
import {PeopleWithrightsInterface} from "../../../../common/interfaces/peopleWithrights.interface";
import {
  ChangeTrimestreModaleDirective
} from "../../directives/modales/sinistre/changeTrimestreModale/changeTrimestreModale.directive";
import {DevisVGSModaleDirective} from "../../directives/modales/devisVGS/devisVGSModale.directive";
import {
  ChangeAvenantModaleDirective
} from "../../directives/modales/sinistre/changeAvenantModale/changeAvenantModale.directive";
import {
  ViewMailgunModaleDirective
} from "../../../Admin/directives/modales/mailgun/viewMailgunModale/viewMailgunModale.directive";
import {dossierJ} from "../../../../common/interfaces/dossierJ.interface";
import {FormsDirective} from "../../../../directives/forms/forms.directive";
import {AjoutSuiviDirective} from "../../directives/partials/ajoutSuivi/ajoutSuivi.directive";
import {
  ChangeCategorieDevisModaleDirective
} from "../../directives/modales/changeCategorieDevis/changeCategorieDevisModale.directive";
import {SmileMoodFormModale} from "../../../smilemood/directives/modales/form/smileMood.form.modale";
import {
  IndemniseIndemniteModaleDirective
} from "../../directives/modales/indemniseIndemnite/indemniseIndemniteModale.directive";
import {emlFileInterface, returnSinistreMULTI, StatsOperationMULTI} from "../../interfaces/sinistresMULTI.interface";
import {WYSIWYGOptions} from "../../../../directives/form/text/WYSIWYGOptions";
import {DevisFactureMultiModaleDirective} from "../../directives/modales/devisFacture/devisFactureModale.directive";
import {
  ChangeCourrierInteruptifMultiModaleDirective
} from "../../directives/modales/sinistre/changeCourrierInteruptifModale/changeCourrierInteruptifModale.directive";
import {CdkDragDrop, moveItemInArray, transferArrayItem} from "@angular/cdk/drag-drop";
import {
  CreateMultiCategorieDocumentationModaleDirective
} from "../../directives/modales/sinistre/documentationCategorie/createCategorieDocumentation/createCategorieDocumentationModale.directive";
import {
  ChangeMultiCategorieDocumentationModaleDirective
} from "../../directives/modales/sinistre/documentationCategorie/changeDocumentation/changeCategorieDocumentationModale.directive";
import {
  documentationMultiSinistreByCategorieInterface
} from "../../interfaces/documentationSinistreByCategorie.interface";
import {
  EditMultiCategorieDocumentationModaleDirective
} from "../../directives/modales/sinistre/documentationCategorie/editCategorieDocumentation/editCategorieDocumentationModale.directive";
import {statutIndemniteDevis} from "../../../DOnet/models/constsDO";


@Component({
  selector: 'SinistreFichePage',
  templateUrl: './sinistre.fiche.page.html'
})
export class SinistreFichePage implements OnInit, OnDestroy {

  public operation$: Observable<any> = new Observable<any>();
  public sinistre$: Observable<any> = new Observable<any>();

  public operation: IntraMultiOperation = null;
  public sinistre: IntraMultiSinistre = null;

  public columns: Array<TableColumnInterface>;
  public hiddenColumns;

  public user: PeopleWithrightsInterface;
  public ROLES = ROLES;

  public societes$: Observable<Societes[]> = new Observable<Societes[]>();

  public ido;
  public id;

  public filtreOperations : LoopBackFilter = {};
  public filtreSinistres : LoopBackFilter = {};

  public folder:string = 'sinistre';

  public mode;

  public formOperation: FormGroup;


  public filterHisto: any = null; // filtre sur les etats sinistres !
  public allEtatsSinistres: IntraMultiEtatSuivi[] = [];
  // public allEtatsSinistresExt: IntraMultiEtatSuiviExt[] = [];

  public etats: IntraMultiEtat[] = [];
  public etatsSuivi: IntraMultiEtatSuivi[] = [];
  // public etatsSuiviExt: IntraMultiEtatSuiviExt[] = [];

  public call$: Observable<returnSinistreMULTI> = new Observable<returnSinistreMULTI>();
  public statsDO: StatsOperationMULTI;
  public stats: {nbResponsables: number, nbSocietes: number} = {nbResponsables: 0, nbSocietes: 0};

  public sinistres$: Observable<any[]> = new Observable<any[]>();
  public sinistresSubscribed: IntraMultiSinistre[] = [];


  protected readonly statutDevis = statutIndemniteDevis;
  protected readonly statusIndemniteOptions = statusIndemniteOptions;
  protected readonly statutIndemniteIndemnite = statutIndemniteIndemnite;

  public EMLs: {files?: emlFileInterface[]} = {files: []};


  /**
   * Stats generales
   */
  public nbSinistres: number;
  public nbCX: number;
  public nbDO: number;
  public nbCloture: number;
  public nbEnCours: number;
  public sumEnjeu: number;
  public sumIndemn: number;

  /**
   * Footer tab !
   */
  public moment = moment;
  public years: any[] = [];
  public currentYear: any = new Date().getFullYear();

  public formDoc: FormGroup;
  @ViewChild('formDocDirective', {static: false}) formDocDirective: FormsDirective;
  public columnsFormDoc: Array<TableColumnInterface>;

  /* Add photo */
  public formAddphoto: FormGroup;
  @ViewChild('uploadPhoto', {static: false}) uploadPhoto: ImageFormDirective;
  /* Add file */
  public formAddfile: FormGroup;
  @ViewChild('uploadFile', {static: false}) uploadFile: FileFormDirective;
  public photos: IntraMultiPhotos[] = [];
  public files: IntraMultiFiles[] = [];

  public formRappel: FormGroup;
  public formRappel2: FormGroup;
  public formRappelColumns: TableColumnInterface[];
  public formRappelColumns2: TableColumnInterface[];
  public filterHistoRappels: any = null; // filtre sur les etats sinistres !

  public allDevis: {[id: number]: IntraMultiDevis} = {};
  public allIndemnites: {[id: number]: IntraMultiExpertise} = {};


  public iframePixie: SafeResourceUrl
  public parameters: apiParameterInterface

  public lignesfactures: IntraEbuzLigneFacture[] = [];

  public J30: boolean = false;
  public dossiersJ30: dossierJ[] = [];
  public dossiersJ27: dossierJ[] = [];
  public listDossiersAutorized = [];
  public dossierReadonly: boolean = false;

  @ViewChild('ajoutSuiviDirective', {static: false}) ajoutSuiviDirective: AjoutSuiviDirective;

  viewMode: 'list' | 'grid' = 'list';

  constructor(
    public router: Router,
    public _sessionService: AppSessionService,
    public _toastr: ToastrService,
    public authService: LoopBackAuth,
    public route: ActivatedRoute,
    public intraReclamationService: IntraReclamationApi,
    public appGlobalEventManagerService: AppGlobalEventManagerService,
    public intraDoOperationApiService: IntraMultiOperationApi,
    public intraDoSinistreApiService: IntraMultiSinistreApi,
    public intraDoHistoriqueApiService: IntraDoHistoriqueApi,
    public intraDoSinistreExtensionApiService: IntraMultiSinistreExtensionApi,
    public intraDoDevisApiService: IntraMultiDevisApi,
    // public intraDoEtatSuiviExt: IntraMultiEtatSuiviExtApi,
    public intraDoExpertiseApiService: IntraMultiExpertiseApi,
    public intraDoFraisApiService: IntraDoFraisavancesApi,
    public intraEbuzLigneFactureApiService: IntraEbuzLigneFactureApi,
    public containerService: AppContainerService,
    public modalService: NgbModal,
    public formBuilder: FormBuilder,
    public _cacheService: AppCacheService,
    public ref: ChangeDetectorRef,
    private pageScrollService: PageScrollService,
    @Inject(DOCUMENT) private document: any,
    public sanitizer:DomSanitizer,

    public intraDoSinistreDocumentationCategorieApiService: IntraMultiSinistreDocumentationCategorieApi,
    public intraDoSinistreDocumentationApiService: IntraMultiSinistreDocumentationApi,

  )
  {

    this.appGlobalEventManagerService.openSpinner();

    this.appGlobalEventManagerService.getApiParameters().subscribe((parameters: apiParameterInterface) => {
      this.parameters = parameters
    })

    this._sessionService.logguedAccountEmitter.subscribe((people) => {
      this.user = people;
    })

    this.societes$ = this._cacheService.getSocietes();

    this.formAddphoto = this.formBuilder.group({
      photo: ['', [Validators.required]]
    });

    this.formAddfile = this.formBuilder.group({
      file: ['', [Validators.required]]
    });


    this.appGlobalEventManagerService.J30Emitter.subscribe((j30) => {
      this.J30 = j30;
    })


    this.formDoc = this.formBuilder.group({
      file: ['', [Validators.required]],
      categorie_id: ['', []]
    });


    this.columnsFormDoc =
      [
        {type:"separator", name:"Nouveau fichier documentation", icon:"fa-duotone fa-file-add", exclude:true},
        {key: "file",name: "Fichier", type: "file"},
        {key: "categorie_id",name: "Catégorie", type: "select", selectOptions:{multiple:false, labelBind:'libelle', keyBind:'id', options:[]}},
      ];

  }


  load(){

    let self = this;

    this.call$.subscribe((result:returnSinistreMULTI) => {

      this.sinistresSubscribed = result.sinistres.filter((sinistre) => {return sinistre.enattente == 0;});
      this.sinistres$ = of(this.sinistresSubscribed);
      this.operation = result.operation;
      this.statsDO = result.stats;
      this.nbCloture = result.nbTermine;
      this.nbEnCours = result.nbEncours;
      this.nbCX = result.nbCX;
      this.nbDO = result.nbDO;
      this.nbSinistres = result.nbDO;
      this.sumEnjeu = result.sumEnjeux;
      this.sumIndemn = result.sumIndemnites;
      this.years = result.years;

      this.sinistre$.subscribe((sinistre:IntraMultiSinistre) => {

        this.sinistre = sinistre;

        this._sessionService._currentMultiSinistre.next(this.sinistre)

        /***
         READONLY
           **/
        if(this.J30){
          if(this.sinistre.id && this.listDossiersAutorized.indexOf(this.sinistre.id) > -1){
            this.dossierReadonly = false;
          } else {
            this.dossierReadonly = true;
          }
        } else {
          this.dossierReadonly = false;
        }

        /***
         * Rustine pour overloader les vrais statuts des suivis des historiques EXT !
         */
        // this.intraDoEtatSuiviExt.find({}).subscribe((etatssuiviExt: IntraMultiEtatSuiviExt[]) => {
        //   this.etatsSuiviExt = etatssuiviExt;
        //   if(this.sinistre.historiquesExt && this.sinistre.historiquesExt.length > 0){
        //     this.sinistre.historiquesExt.forEach((histExt: IntraDoHistoriqueExt) => {
        //       histExt.etatSuivi = this.etatsSuiviExt.find((el) => el.id === histExt.idEtatSuivi);
        //     });
        //   }
        // });

        this.buildCategories(sinistre.documentationsByCategorie);

        this.photos = this.sinistre.photos;
        this.files = this.sinistre.files;

        this.allEtatsSinistres = [];
        // this.allEtatsSinistresExt = [];

        if(self.sinistre && self.sinistre.historiques){
          self.sinistre.historiques.forEach((etat) => {
            if(etat && etat.etatSuivi && self.allEtatsSinistres.findIndex((el:IntraMultiEtatSuivi) => {return el.id == etat.etatSuivi.id}) === -1){self.allEtatsSinistres.push(etat.etatSuivi);}
          });
          self.allEtatsSinistres.sort((a, b) => a.libelle.localeCompare(b.libelle));
        }

        // if (self.sinistre && self.sinistre.historiquesExt) {
        //   self.sinistre.historiquesExt.forEach((etat) => {
        //     if (etat && etat.etatSuivi && self.allEtatsSinistresExt.findIndex((el: IntraMultiEtatSuiviExt) => el.id == etat.etatSuivi.id) === -1) {self.allEtatsSinistresExt.push(etat.etatSuivi); }
        //   });
        //   self.allEtatsSinistresExt.sort((a, b) => a.libelle.localeCompare(b.libelle));
        // }


        if (sinistre.indemnites && sinistre.indemnites.length > 0) {
          sinistre.indemnites.forEach((ind) => {
            self.allIndemnites[ind.id] = ind;
          });
        }

        sinistre['totalFactureHT'] = 0;
        sinistre['totalFactureTTC'] = 0;
        sinistre['totalAFacturerHT'] = 0;
        sinistre['totalAFacturerTTC'] = 0;

        if (sinistre.devis && sinistre.devis.length > 0) {
          sinistre.devis.forEach((dev) => {
            self.allDevis[dev.id] = dev;

            sinistre['totalAFacturerHT'] += dev.montantdevis;
            sinistre['totalAFacturerTTC'] += dev.montantTTCdevis;

            dev['totalfactureHT'] = 0;
            dev['totalfactureTTC'] = 0;

            if(dev.factures){
              dev.factures.forEach((facture) => {
                dev['totalfactureHT'] += facture.montantHT;
                dev['totalfactureTTC'] += facture.montantTTC;

                sinistre['totalFactureHT'] += facture.montantHT;
                sinistre['totalFactureTTC'] += facture.montantTTC;

              });
            } else {
              dev['totalfactureHT'] = 0;
              dev['totalfactureTTC'] = 0;
            }

          });
        }

        this.intraDoSinistreApiService.getEMLs(this.sinistre.id).subscribe((files) => {
          this.EMLs = files;

          this.appGlobalEventManagerService.updateTitle('Fiche MULTInet et suivi '+sinistre.referencevge);
          this.appGlobalEventManagerService.updateBreadcrumb([{title:'Home', url:'/'}, {title:'Liste des opérations MULTInet', url:'/MULTInet/operations'}, {title:'Liste des sinistres - opération '+this.operation.libelle, url:'/MULTInet/sinistres/'+this.ido },{title:'Fiche sinistre '+this.sinistre.referencevge, url:null}]);

          this._cacheService.getIntraMultiEtats(false).subscribe((etats: IntraMultiEtat[]) => {

            this.etats = etats;

            this._cacheService.getIntraMultiEtatsSuivi(false).subscribe((etatsSuivi: IntraMultiEtatSuivi[]) => {

              this.etatsSuivi = etatsSuivi;

              setTimeout(() => {
                this.appGlobalEventManagerService.closeSpinner();
              }, 600);
            });
          });
        }, error => {this.appGlobalEventManagerService.closeSpinner();});

        this.loadFactures();

        // this.formRappel = this.formBuilder.group({
        //   rappelAuto: [sinistre.rappelAuto, []]
        // });
        //
        // this.formRappelColumns =
        //   [
        //     {type:"separator", name:"Rappel auto, activation du système de rappel automatique sur ce sinistre à une fréquence de 3 semaines", icon:"fa-duotone fa-cubes", exclude:true},
        //     {key: "rappelAuto",name: "Activer / désactiver le rappel automatique", type: "boolean", required:true, explain:'' },
        //   ]
        //
        // const d = moment(sinistre.nextRappelAuto, 'YYYY-MM-DD').toObject();
        // this.formRappel2 = this.formBuilder.group({
        //   rappelAuto: [sinistre.rappelAuto, []],
        //   nextRappelAuto: [{year:d.years, month:d.months+1, day: d.date}, [Validators.required]],
        // });
        //
        // this.formRappelColumns2 =
        //   [
        //     {type:"separator", name:"Rappel auto, activation du système de rappel automatique sur ce sinistre à une fréquence de 3 semaines", icon:"fa-duotone fa-cubes", exclude:true},
        //     {key: "rappelAuto",name: "Activer / désactiver le rappel automatique", type: "boolean", required:true, explain:'' },
        //     {key: "nextRappelAuto",name: "Date du prochain rappel auto", type: "date", required:true, explain:'' },
        //   ]

      });
    });
  }

  loadFactures(){
    if(ROLES.admin.indexOf(this.user.profil.name) > -1 && this.user.accessEbuiz === 1){
      this.intraEbuzLigneFactureApiService.find({where: {sinistre_id: this.id}, include: ['facture']}).subscribe((lignesfactures: IntraEbuzLigneFacture[]) => {
        this.lignesfactures = lignesfactures;
      })
    }
  }

  loadIndemnitesDevis() {
    this.intraDoSinistreApiService.getIndemnites(this.sinistre.id).subscribe((indemnites: IntraMultiExpertise[]) => {
      this.sinistre.indemnites = indemnites;
      this.loadDevis();
    });
  }

  loadDevis() {
    this.intraDoSinistreApiService.getDevis(this.sinistre.id).subscribe((devis: IntraMultiDevis[]) => {
      this.sinistre.devis = devis;
    });
  }

  loadFrais() {
    this.intraDoSinistreApiService.getFraisavances(this.sinistre.id).subscribe((frais: IntraMultiFraisavances[]) => {
      this.sinistre.fraisavances = frais;
    });
  }

  loadHistoriques(){
    this.intraDoSinistreApiService.getHistoriques(this.sinistre.id, {order:['date ASC', 'id ASC']}).subscribe((historiques: IntraMultiHistorique[]) => {
      // console.log('HIISTOOs', historiques);
      this.sinistre.historiques = historiques;
    });
  }

  loadDocumentations(){

    // Réinitialiser la liste des IDs pour forcer la régénération
    this.dropListIds = [];
    this.intraDoSinistreApiService.getDocumentationsByCategories(this.sinistre.id).subscribe((documentations: documentationMultiSinistreByCategorieInterface) => {
      this.sinistre.documentationsByCategorie = documentations;
      this.ajoutSuiviDirective.refreshDocumentsWithDocuments(documentations);
      this.buildCategories(documentations);
    });

  }

  buildCategories(documents){
    const categoriesOptions = [];
    categoriesOptions.push({id: null, libelle: 'Non classifiée'});

    if(documents.categories && documents.categories.length > 0){
      documents.categories.forEach((categorie) => {
        categoriesOptions.push({id: categorie.id, libelle: categorie.libelle})

        if(categorie.enfants && categorie.enfants.length > 0){
          categorie.enfants.forEach((sousCategorie) => {
            categoriesOptions.push({id: sousCategorie.id, libelle: `${("- ").repeat(2)}${categorie.libelle} / ${sousCategorie.libelle}`})
          })
        }

      })
    }

    const col = this.columnsFormDoc.find((el) => {
      return el.key == 'categorie_id';
    });
    if(col){col.selectOptions.options = JSON.parse(JSON.stringify(categoriesOptions))};
  }

  getParameter(){
    this.route.paramMap.subscribe(params => {
      this.ido = params.get("ido"); // id operation
      this.id = params.get("id"); // id operation

      this.operation$ = this.intraDoOperationApiService.findOne({where:{id: this.ido}}).pipe(shareReplay(1));

      let force = false;
      this.call$ = this._cacheService.getlistSinistresMultiForIdoAndFilter(force, this.ido, this.filtreOperations, this.filtreSinistres);
      this.loadSinistre();
      this.load();
    })
  }

  loadSinistre(force=false){
    var filter = {where:{id: this.id}, include:[{relation: 'documentations', scope:{order: ['date DESC']}},{relation: 'historiques', scope:{order: ['date ASC', 'id ASC']}}, 'filesFtpRights']}
    if(force){
      this.sinistre$ = Object.assign({}, this.intraDoSinistreApiService.findOne(filter).pipe(share()));
    }else{
      this.sinistre$ = this.intraDoSinistreApiService.findOne(filter).pipe(share());
    }
  }


  /*****
   DOCUMENTATIONS BY categorie
   */

  // Ajouter cette propriété à la classe
  private dropListIds: string[] = [];

  // Ajouter cette méthode pour générer les IDs de toutes les listes connectées
  getConnectedDropListIds(): string[] {
    // Générer les IDs seulement si la liste est vide ou si les données ont changé
    if (this.dropListIds.length === 0 && this.sinistre?.documentationsByCategorie) {
      // Ajouter l'ID de la liste des documents non classifiés
      this.dropListIds.push('list-non-classifies');

      // Ajouter les IDs des listes de catégories principales
      if (this.sinistre.documentationsByCategorie.categories) {
        this.sinistre.documentationsByCategorie.categories.forEach(categorie => {
          this.dropListIds.push(`list-cat-${categorie.id}`);

          // Ajouter les IDs des listes de sous-catégories
          if (categorie.enfants) {
            categorie.enfants.forEach(sousCategorie => {
              this.dropListIds.push(`list-souscat-${sousCategorie.id}`);
            });
          }
        });
      }
    }

    return this.dropListIds;
  }

  // Fonction pour gérer le drag & drop
  dropDocument(event: CdkDragDrop<any[]>, targetCategorieId: number | null) {
    // Vérifier si le document est déplacé dans une liste différente
    if (event.previousContainer !== event.container) {
      const doc = event.item.data;


      // Mettre à jour la catégorie du document
      this.intraDoSinistreDocumentationApiService.patchAttributes(doc.id, {categorie_id: targetCategorieId}).subscribe(
        () => {
          // Déplacer l'élément dans l'interface
          transferArrayItem(
            event.previousContainer.data,
            event.container.data,
            event.previousIndex,
            event.currentIndex
          );

          this._toastr.success('Document déplacé avec succès', 'Succès');

          // Recharger les données pour s'assurer que tout est à jour
          this.loadDocumentations();
        },
        error => {
          this._toastr.error('Erreur lors du déplacement du document', 'Erreur');
        }
      );
    } else {
      // Si le document est déplacé dans la même liste, simplement réorganiser
      moveItemInArray(event.container.data, event.previousIndex, event.currentIndex);
    }
  }


  // Fonction pour ouvrir la modale de création de catégorie
  openModaleCreateCategorie(parent_id: number=null) {
    const modalRef = this.modalService.open(CreateMultiCategorieDocumentationModaleDirective, { size: 'lg' });
    modalRef.componentInstance.sinistreId = this.sinistre.id;
    modalRef.componentInstance.parent_id = parent_id;

    modalRef.componentInstance.clickeventUpdateCategorieDocumentation.subscribe((ret:IntraMultiSinistreDocumentationCategorie) => {
      if(ret){
        modalRef.close();
        this._toastr.success('Catégorie créée avec succès', 'Succès');
        this.loadDocumentations();
      }
    });
  }


  openModaleChangeDocumentCategorie(doc){

    const activeModal = this.modalService.open(ChangeMultiCategorieDocumentationModaleDirective, { size: 'lg', centered: false });
    activeModal.componentInstance.doc = doc;
    activeModal.componentInstance.documents = this.sinistre.documentationsByCategorie;
    activeModal.componentInstance.clickeventUpdateDocumentation.subscribe((ret:IntraMultiSinistreDocumentation) => {
      activeModal.close();
      this.loadDocumentations();
    });

  }

  deleteDocumentationFile(file){
    let idfile = file.id;
    this.intraDoSinistreApiService.destroyByIdDocumentations(this.id, idfile).subscribe((res) => {
      this.ajoutSuiviDirective.loadInfos();
      this.loadDocumentations();
      this._toastr.success('Le fichier a bien été supprimé', 'Suppression du fichier')
    }, error => {
      this._toastr.error(error.message, 'Suppression du fichier')
    });
  }

  deleteCategorieDocument(categorie: any){
    this.intraDoSinistreDocumentationCategorieApiService.deleteById(categorie.id).subscribe((res) => {
      this._toastr.success('Catégorie '+ categorie.libelle + ' supprimée avec succès', 'Succès');
      // Recharger les données pour s'assurer que tout est à jour
      this.loadDocumentations();
    }, error => {
      this._toastr.error('Catégorie '+ categorie.libelle + ' non supprimée ' + error.message, 'Erreur');
    });


  }

  // Fonction pour ouvrir la modale d'édition de catégorie
  openModaleEditCategorie(categorieId: number) {

    this.intraDoSinistreDocumentationCategorieApiService.findOne({where: {id: categorieId}}).subscribe((categorie) => {
      const modalRef = this.modalService.open(EditMultiCategorieDocumentationModaleDirective, { size: 'lg' });
      modalRef.componentInstance.sinistreId = this.sinistre.id;
      modalRef.componentInstance.categorie = categorie;

      modalRef.componentInstance.clickeventUpdateCategorieDocumentation.subscribe((ret:IntraMultiSinistreDocumentationCategorie) => {
        if(ret){
          modalRef.close();
          this._toastr.success('Catégorie créée avec succès', 'Succès');
          this.loadDocumentations();
        }
      });
    })
  }

  saveFile(datas:object){

    datas['people_id'] = this.user.id;
    datas['date'] = moment().format('YYYY-MM-DD');

    if(!datas['categorie_id']){
      datas['categorie_id'] = null;
    }

    this.intraDoSinistreApiService.createDocumentations(this.sinistre.id, datas).subscribe((res:IntraMultiSinistreDocumentation) => {
      if(res && !res['message']){
        this._toastr.success('Succès !', 'Ajout d\'un ficher dans les documentations');
        this.formDocDirective.reset();
        this.formDoc.reset();
        this.loadDocumentations();
      }else{
        this._toastr.error(res['message'], 'Ajout d\'un ficher dans les documentations');
      }
    });

  }


  deleteFile(file:IntraMultiFiles){

    let idfile = file.id;
    this.intraDoSinistreApiService.destroyByIdFiles(this.id, idfile).subscribe((res) => {

      this.files = this.files.filter((el) => {
        return el.id !== idfile;
      });

      this._toastr.success('Le fichier a bien été supprimé', 'Suppression du fichier')

    }, error => {
      this._toastr.error(error.message, 'Suppression du fichier')
    });
  }

  /*****
    END DOCUMENTATIONS BY categorie
   */





  GotoSinistre($event:{opId:number, year:number|string, sinistreId:number}){
    if($event && $event.sinistreId && $event.opId){
      this.router.navigate(['/', 'MULTInet', 'sinistre', 'fiche', $event.sinistreId, $event.opId]);
    }
  }


  // openModaleChangeEtat(sinistre:IntraMultiSinistre, index){
  //
  //   const activeModal = this.modalService.open(ChangeEtatSinistreModaleDirective, { size: 'xl', centered: false });
  //   activeModal.componentInstance.operation = this.operation;
  //   activeModal.componentInstance.sinistre = sinistre;
  //   activeModal.componentInstance.clickeventUpdateSinistre.subscribe((ret:IntraMultiSinistre) => {
  //
  //
  //   });
  // }

  public filtreDate = null;
  public filtreEtat = null;
  addFilter(field, value){
    if(field == 'date'){
      this.filtreDate = (value === null) ? null : moment(value).format('DD/MM/YYYY');
    }else if(field == 'etat_suivi_id'){
      this.filtreEtat = (value === null) ? null : value.id;
    }

  }


  saveTextLibre(){
    this.intraDoSinistreApiService.patchAttributes(this.sinistre.id, {'txtlibre': this.sinistre.txtlibre}).subscribe((res) => {
      this._toastr.success('Le texte a été correctement mis à jour', 'Mise à jour du texte libre')
    });
  }

  deleteSuivi(historique:IntraDoHistorique, index){
    this.intraDoHistoriqueApiService.deleteById(historique.id).subscribe(
      (res) => {
        this._toastr.success('Suppression du suivi', 'Le suivi a été supprimé avec succès');

        this.sinistre.historiques.splice(index, 1);

      },
      (err) => {this._toastr.success('Suppression du suivi', err.message);}
    );
  }

  deleteExtension(extension){
    let idExtTodelete = extension.id;
    this.intraDoSinistreExtensionApiService.deleteById(idExtTodelete).subscribe(
      (res) => {
        this._toastr.success('Suppression de l\'extension', 'L\'extension a été supprimé avec succès');

        let extensionIndex = this.sinistre.extensions.findIndex((elExtension) => {
          return idExtTodelete == elExtension.id;
        });

        if(extensionIndex > -1){
          this.sinistre.extensions.splice(extensionIndex, 1);
        }


      },
      (err) => {this._toastr.success('Suppression de l\'extension', err.message);}
    );
  }

  deleteDevis(devis, index){

    let idTodelete = devis.id;
    this.intraDoDevisApiService.deleteById(idTodelete).subscribe(
      (res) => {
        this._toastr.success('Suppression du devis', 'Le devis a été supprimé avec succès');

        let devisIndex = this.sinistre.devis.findIndex((elDevis) => {
          return idTodelete == elDevis.id;
        });

        if(devisIndex > -1){
          this.sinistre.devis.splice(devisIndex, 1);
          this.loadIndemnitesDevis();
        }

      },
      (err) => {this._toastr.success('Suppression du devis', err.message);}
    );

  }

  deleteIndemnite(indemnite, index){

    let idTodelete = indemnite.id;
    this.intraDoExpertiseApiService.deleteById(idTodelete).subscribe(
      (res) => {
        this._toastr.success('Suppression de l\'indemnité', 'L\'indemnité a été supprimé avec succès');

        let Index = this.sinistre.indemnites.findIndex((el) => {
          return idTodelete == el.id;
        });

        if(Index > -1){
          this.sinistre.indemnites.splice(Index, 1);
          this.loadIndemnitesDevis();
        }

      },
      (err) => {this._toastr.success('Suppression de l\'indemnité', err.message);}
    );

  }

  deleteFrais(frais: IntraDoFraisavances, index: number){

    let idTodelete = frais.id;
    this.intraDoFraisApiService.deleteById(idTodelete).subscribe(
      (res) => {
        this._toastr.success('Suppression de ces frais avancés', 'Les frais avancés ont été supprimé avec succès');
        let Index = this.sinistre.fraisavances.findIndex((el) => {
          return idTodelete == el.id;
        });

        if(Index > -1){
          this.sinistre.fraisavances.splice(Index, 1);
          this.loadFrais();
        }
      },
      (err) => {this._toastr.success('Suppression ces frais avancés', err.message);}
    );

  }


  deleteBilanFTP(){
    this.intraDoSinistreApiService.patchAttributes(this.sinistre.id, {'ftpbilan':null}).subscribe((res) => {
      this._toastr.success('Suppression du bilan FTP', 'Le bilan FTP a été supprimé avec succès');
      this.sinistre.ftpbilan = null;
    })
  }

  deleteBilanFile(){
    this.intraDoSinistreApiService.patchAttributes(this.sinistre.id, {'filebilan':null}).subscribe((res) => {
      this._toastr.success('Suppression du fichier bilan', 'Le fichier bilan a été supprimé avec succès');
      this.sinistre.filebilan = null;
    })
  }

  openModaleChangeReferences(sinistre:IntraMultiSinistre){
    const activeModal = this.modalService.open(ChangeReferencesModaleDirective, { size: 'lg', centered: false });
    this.returnSaveModaleReloadLine(activeModal, sinistre, true);
  }

  openModaleChangeReference(sinistre: IntraMultiSinistre, field: string) {
    const activeModal = this.modalService.open(ChangeReferenceModaleDirective, { size: 'lg', centered: false });
    this.returnSaveModaleReloadLine(activeModal, sinistre, true);
  }

  openModaleChangeLibelle(sinistre: IntraMultiSinistre) {
    const activeModal = this.modalService.open(ChangeLibelleModaleDirective, { size: 'lg', centered: false });
    this.returnSaveModaleReloadLine(activeModal, sinistre, true);
  }

  openModaleChangeBoolean(sinistre: IntraMultiSinistre) {
    const activeModal = this.modalService.open(ChangeBooleanModaleDirective, { size: 'lg', centered: false });
    this.returnSaveModaleReloadLine(activeModal, sinistre, true);
  }

  openModaleChangeAvenant(sinistre: IntraMultiSinistre) {
    const activeModal = this.modalService.open(ChangeAvenantModaleDirective, { size: 'lg', centered: false });
    this.returnSaveModaleReloadLine(activeModal, sinistre, true);
  }

  openModaleChangePositionAssur(sinistre:IntraMultiSinistre){
    const activeModal = this.modalService.open(ChangePositionAssurModaleDirective, { size: 'xl', centered: false });
    activeModal.componentInstance.operation = this.operation;
    this.returnSaveModaleReloadAll(activeModal, sinistre, false);
  }

  openModaleChangeTauxAvancement(sinistre:IntraMultiSinistre){
    const activeModal = this.modalService.open(ChangeAvancementModaleDirective, { size: 'lg', centered: false });
    activeModal.componentInstance.operation = this.operation;
    this.returnSaveModaleReloadLine(activeModal, sinistre, false);
  }

  openModaleChangeCourrierInteruptif(sinistre:IntraMultiSinistre){
    const activeModal = this.modalService.open(ChangeCourrierInteruptifMultiModaleDirective, { size: 'lg', centered: false });
    activeModal.componentInstance.operation = this.operation;
    this.returnSaveModaleReloadLine(activeModal, sinistre, false);
  }

  openModaleChangeAnnuitesFacturation(sinistre:IntraMultiSinistre){
    const activeModal = this.modalService.open(ChangeAnnuiteFacturationModaleDirective, { size: 'lg', centered: false });
    activeModal.componentInstance.operation = this.operation;
    this.returnSaveModaleReloadAll(activeModal, sinistre, false);
  }

  openModaleChangepositionNonGarantieFacturation(sinistre:IntraMultiSinistre){
    const activeModal = this.modalService.open(ChangeGarantieNonContestableModaleDirective, { size: 'lg', centered: false });
    activeModal.componentInstance.operation = this.operation;
    this.returnSaveModaleReloadAll(activeModal, sinistre, false);
  }

  openModaleChangeTrimestreFacturation(sinistre:IntraMultiSinistre){
    const activeModal = this.modalService.open(ChangeTrimestreModaleDirective, { size: 'lg', centered: false });
    activeModal.componentInstance.sinistre = sinistre;
    this.returnSaveModaleReloadAll(activeModal, sinistre, false);
  }

  openModaleChangeBilanFinancier(sinistre:IntraMultiSinistre){
    const activeModal = this.modalService.open(ChangeBilanFinancierModaleDirective, { size: 'lg', centered: false });
    this.returnSaveModaleReloadLine(activeModal, sinistre, false);
  }

  openModaleChangeBilanFTP(sinistre:IntraMultiSinistre){
    const activeModal = this.modalService.open(ChangeBilanFTPModaleDirective, { size: 'lg', centered: false });
    activeModal.componentInstance.operation = this.operation;
    this.returnSaveModaleReloadLine(activeModal, sinistre, false);
  }

  openModaleChangeDateDesignationExpert(sinistre:IntraMultiSinistre){
    const activeModal = this.modalService.open(ChangeDateDesignationExpertMultiModaleDirective, { size: 'lg', centered: false });
    activeModal.componentInstance.operation = this.operation;
    this.returnSaveModaleReloadLine(activeModal, sinistre, false);
  }

  openModaleChangeEtat(sinistre:IntraMultiSinistre, etatId:number){

    const activeModal = this.modalService.open(ChangeEtatSinistreModaleDirective, { size: 'xl', centered: false });
    activeModal.componentInstance.operation = this.operation;
    activeModal.componentInstance.etatId = etatId;
    this.returnSaveModaleReloadAll(activeModal, sinistre, true);
  }

  /** En cours / terminé **/
  openModaleChangeStatut(sinistre:IntraMultiSinistre){
    const activeModal = this.modalService.open(SinistreChangeStatutModaleDirective, { size: 'lg', centered: false });
    this.returnSaveModaleReloadLine(activeModal, sinistre, true);
  }

  openModaleChangeEnjeu(sinistre:IntraMultiSinistre){
    const activeModal = this.modalService.open(ChangeEnjeuModaleDirective, { size: 'lg', centered: false });
    this.returnSaveModaleReloadLine(activeModal, sinistre, false);
  }

  openModaleAjoutSuivi(sinistre:IntraMultiSinistre){
    const activeModal = this.modalService.open(AjoutSuiviModaleDirective, { size: 'xl', centered: false });
    activeModal.componentInstance.operation = this.operation;
    this.returnSaveModaleReloadHistoriques(activeModal, sinistre, false);
  }

  openModaleShowDevis(sinistre:IntraMultiSinistre, devis:IntraMultiDevis){

  }

  /**
   * Retour directive dans la modale
   * @param ret
   */
  public returnSaveModaleReloadHistoriques(activeModal, sinistre:IntraMultiSinistre, reloadFull=true){
    activeModal.componentInstance.sinistre = sinistre;
    activeModal.componentInstance.clickeventAddSuiviSinistre.subscribe((ret:IntraDoHistorique) => {

      if(ret){
        activeModal.close();
        this._cacheService.deleteCachelistSinistresDoForIdoAndFilter();
        this.loadHistoriques();

      }else{
        this._toastr.error('Une erreur s\'est produite', 'Problème');
      }
    })
  }

  /**
   * Retour directive sur la fiche
   * @param ret
   */
  nextAddSuivisinistre(ret:IntraDoHistorique){
      if(ret){
        this._cacheService.deleteCachelistSinistresDoForIdoAndFilter();
        this.loadHistoriques();
        this.loadDocumentations();
      }else{
      this._toastr.error('Une erreur s\'est produite', 'Problème');
    }
  }


  openModaleChangeDossierFTP(sinistre:IntraMultiSinistre, folderName:string, folderLibelle:string){
    const activeModal = this.modalService.open(ChangeDossierFTPModaleDirective, { size: 'lg', centered: false });
    activeModal.componentInstance.operation = this.operation;
    activeModal.componentInstance.folderName = folderName;
    activeModal.componentInstance.folderLibelle = folderLibelle;
    this.returnSaveModaleReloadLine(activeModal, sinistre, true);
  }

  openModaleChangeDate(sinistre:IntraMultiSinistre, dateName:string, dateLibelle:string){
    const activeModal = this.modalService.open(ChangeDateModaleDirective, { size: 'lg', centered: false });
    activeModal.componentInstance.operation = this.operation;
    activeModal.componentInstance.dateName = dateName;
    activeModal.componentInstance.dateLibelle = dateLibelle;
    this.returnSaveModaleReloadLine(activeModal, sinistre, true);
  }

  openModaleEditIndemnite(sinistre:IntraMultiSinistre, indemnite: IntraMultiExpertise){
    const activeModal = this.modalService.open(IndemniteModaleDirective, { size: 'xl', centered: false });
    activeModal.componentInstance.operation = this.operation;
    activeModal.componentInstance.sinistre = sinistre;
    activeModal.componentInstance.indemniteObject = indemnite;

    activeModal.componentInstance.clickeventUpdateExpertise.subscribe((ret:IntraMultiExpertise) => {
      if(ret) {
        activeModal.close();

        let index = this.sinistre.indemnites.findIndex(value => {return value.id == ret.id});
        if(index > -1){
          this.sinistre.indemnites.splice(index, 1, ret);
          this.loadIndemnitesDevis();
        }
      }
    });
  }

  openModaleIndemniseIndemnite(sinistre:IntraMultiSinistre, indemnite: IntraMultiExpertise){
    const activeModal = this.modalService.open(IndemniseIndemniteModaleDirective, { size: 'xl', centered: false });
    activeModal.componentInstance.indemniteObject = indemnite;

    activeModal.componentInstance.clickeventUpdateExpertise.subscribe((ret:IntraMultiExpertise) => {
      if(ret) {
        activeModal.close();

        let index = this.sinistre.indemnites.findIndex(value => {return value.id == ret.id});
        if(index > -1){
          this.sinistre.indemnites.splice(index, 1, ret);
          this.loadIndemnitesDevis();
        }
      }
    });
  }

  openModaleCreateIndemnite(sinistre:IntraMultiSinistre){
    const activeModal = this.modalService.open(IndemniteModaleDirective, { size: 'xl', centered: false });
    activeModal.componentInstance.operation = this.operation;
    activeModal.componentInstance.sinistre = sinistre;
    activeModal.componentInstance.clickeventUpdateExpertise.subscribe((ret:IntraMultiExpertise) => {
      if(ret) {
        activeModal.close();
        this.sinistre.indemnites.push(ret);
        this.loadIndemnitesDevis();
      }
    });
  }



  openModaleEditFrais(sinistre:IntraMultiSinistre, frais: IntraMultiFraisavances){
    const activeModal = this.modalService.open(FraisModaleDirective, { size: 'xl', centered: false });
    activeModal.componentInstance.operation = this.operation;
    activeModal.componentInstance.sinistre = sinistre;
    activeModal.componentInstance.fraisObject = frais;

    activeModal.componentInstance.clickeventUpdateFrais.subscribe((ret:IntraMultiFraisavances) => {
      if(ret) {
        activeModal.close();

        let index = this.sinistre.fraisavances.findIndex(value => {return value.id == ret.id});
        if(index > -1){
          this.sinistre.fraisavances.splice(index, 1, ret);
          this.loadFrais();
        }
      }
    });
  }

  openModaleCreateFrais(sinistre:IntraMultiSinistre){
    const activeModal = this.modalService.open(FraisModaleDirective, { size: 'xl', centered: false });
    activeModal.componentInstance.operation = this.operation;
    activeModal.componentInstance.sinistre = sinistre;
    activeModal.componentInstance.clickeventUpdateFrais.subscribe((ret:IntraMultiFraisavances) => {
      if(ret) {
        activeModal.close();
        this.sinistre.fraisavances.push(ret);
        this.loadFrais();
      }
    });
  }



  openModaleCreateDevis(sinistre:IntraMultiSinistre){
    const activeModal = this.modalService.open(DevisModaleDirective, { size: 'xl', centered: false });
    activeModal.componentInstance.operation = this.operation;
    activeModal.componentInstance.sinistre = sinistre;
    activeModal.componentInstance.clickeventUpdateDevis.subscribe((ret:IntraMultiDevis) => {
      if(ret) {
        activeModal.close();
        this.loadIndemnitesDevis();
      }
    });
  }


  openModaleCreateDevisVGS(sinistre:IntraMultiSinistre){
    const activeModal = this.modalService.open(DevisVGSModaleDirective, { size: 'xl', centered: false });
    activeModal.componentInstance.operation = this.operation;
    activeModal.componentInstance.sinistre = sinistre;
    activeModal.componentInstance.clickeventUpdateDevis.subscribe((ret:IntraMultiDevis) => {
      if(ret) {
        activeModal.close();
        this.loadHistoriques();
      }
    });
  }

  openModaleEditDevis(sinistre:IntraMultiSinistre, devis:IntraMultiDevis){
    const activeModal = this.modalService.open(DevisModaleDirective, { size: 'xl', centered: false });
    activeModal.componentInstance.operation = this.operation;
    activeModal.componentInstance.sinistre = sinistre;
    activeModal.componentInstance.devis = devis;

    activeModal.componentInstance.clickeventUpdateDevis.subscribe((ret:IntraMultiDevis) => {
      if(ret) {
        activeModal.close();


        let index = this.sinistre.devis.findIndex(value => {return value.id == ret.id});
        if(index > -1){
          this.sinistre.devis.splice(index, 1, ret);
          this.loadIndemnitesDevis();
        }
      }
    });
  }

  public returnSaveModaleReloadAll(activeModal, sinistre:IntraMultiSinistre, reloadFull=true){
    activeModal.componentInstance.sinistre = sinistre;
    activeModal.componentInstance.clickeventUpdateSinistre.subscribe((ret:IntraMultiSinistre) => {
      if(ret){
        activeModal.close();

        this._cacheService.deleteCachelistSinistresDoForIdoAndFilter();

        /* Get Relations ! */
        let fileFTPRights = this.sinistre.filesFtpRights;

        this.sinistre = ret;
        this.sinistre.filesFtpRights = fileFTPRights;

        this._cacheService.deleteCachelistSinistresDoForIdoAndFilter();
        this.loadHistoriques();

        this.loadFactures();

      }else{
        this._toastr.error('Une erreur s\'est produite', 'Problème');
      }
    })
  }

  public returnSaveModaleReloadLine(activeModal, sinistre:IntraMultiSinistre, reloadFull=true){
    activeModal.componentInstance.sinistre = sinistre;
    activeModal.componentInstance.clickeventUpdateSinistre.subscribe((ret:IntraMultiSinistre) => {
      if(ret){
        activeModal.close();

        this._cacheService.deleteCachelistSinistresDoForIdoAndFilter();

        /* Get Relations ! */
        let histos = this.sinistre.historiques;
        let fileFTPRights = this.sinistre.filesFtpRights;

        this.sinistre = ret;
        this.sinistre.historiques = histos;
        this.sinistre.filesFtpRights = fileFTPRights;

      }else{
        this._toastr.error('Une erreur s\'est produite', 'Problème');
      }
    })
  }

  initFacture(){
    this.intraDoSinistreApiService.patchAttributes(this.sinistre.id, {nbAnneeFac: this.sinistre.nbAnneeFac}).subscribe((res:IntraMultiSinistre) => {
      if(res && !res['message']){
        this._toastr.success('Succès !', 'Initialisation de la facturation');
        this.loadFactures();
      }else{
        this._toastr.error(res['message'], 'Initialisation de la facturation');
      }
    });
  }

  openModaleChangeReferencesExtension(extension:IntraMultiSinistreExtension){
    const activeModal = this.modalService.open(ChangeReferencesExtensionModaleDirective, { size: 'lg', centered: false });
    this.returnSaveModaleExtensionReloadLine(activeModal, extension, true);
  }

  openModaleChangeLibelleExtension(extension:IntraMultiSinistreExtension){
    const activeModal = this.modalService.open(ChangeLibelleModaleExtensionDirective, { size: 'lg', centered: false });
    this.returnSaveModaleExtensionReloadLine(activeModal, extension, true);
  }

  openModaleChangePositionAssurExtension(extension:IntraMultiSinistreExtension){
    const activeModal = this.modalService.open(ChangePositionAssurExtensionModaleDirective, { size: 'xl', centered: false });
    activeModal.componentInstance.operation = this.operation;
    this.returnSaveModaleExtensionReloadLine(activeModal, extension, false);
  }

  openModaleChangeTauxAvancementExtension(extension:IntraMultiSinistreExtension){
    const activeModal = this.modalService.open(ChangeAvancementExtensionModaleDirective, { size: 'lg', centered: false });
    activeModal.componentInstance.operation = this.operation;
    this.returnSaveModaleExtensionReloadLine(activeModal, extension, false);
  }

  openModaleChangeCourrierInteruptifExtension(extension:IntraMultiSinistreExtension){
    const activeModal = this.modalService.open(ChangeCourrierInteruptifExtensionModaleDirective, { size: 'lg', centered: false });
    activeModal.componentInstance.operation = this.operation;
    this.returnSaveModaleExtensionReloadLine(activeModal, extension, false);
  }

  openModaleChangeDateDesignationExpertExtension(extension:IntraMultiSinistreExtension){
    const activeModal = this.modalService.open(ChangeDateDesignationExpertExtensionModaleDirective, { size: 'lg', centered: false });
    activeModal.componentInstance.operation = this.operation;
    this.returnSaveModaleExtensionReloadLine(activeModal, extension, false);
  }

  /** En cours / terminé **/
  openModaleChangeStatutExtension(extension:IntraMultiSinistreExtension){
    const activeModal = this.modalService.open(SinistreChangeStatutExtensionModaleDirective, { size: 'lg', centered: false });
    this.returnSaveModaleExtensionReloadLine(activeModal, extension, true);
  }
  openModaleChangeDateExtension(extension:IntraMultiSinistreExtension, dateName:string, dateLibelle:string){
    const activeModal = this.modalService.open(ChangeDateModaleExtensionDirective, { size: 'lg', centered: false });
    activeModal.componentInstance.operation = this.operation;
    activeModal.componentInstance.dateName = dateName;
    activeModal.componentInstance.dateLibelle = dateLibelle;
    this.returnSaveModaleExtensionReloadLine(activeModal, extension, true);
  }


  public returnSaveModaleExtensionReloadLine(activeModal, extension:IntraMultiSinistreExtension, reloadFull=true){
    activeModal.componentInstance.sinistre = extension;
    activeModal.componentInstance.clickeventUpdateSinistreExtension.subscribe((ret:IntraMultiSinistreExtension) => {
      if(ret){
        activeModal.close();

        let index = this.sinistre.extensions.findIndex((el) => {return el.id == extension.id});
        if(index > -1){
          this.sinistre.extensions.splice(index, 1, ret)
        }

      }else{
        this._toastr.error('Une erreur s\'est produite', 'Problème');
      }
    })
  }

  openModaleAjoutExtension(sinistre:IntraMultiSinistre){
    const activeModal = this.modalService.open(AjoutExtensionModaleDirective, { size: 'xl', centered: false });
    activeModal.componentInstance.operation = this.operation;
    activeModal.componentInstance.sinistre = sinistre;
    activeModal.componentInstance.clickeventUpdateSinistreExtension.subscribe((ret:IntraMultiSinistreExtension) => {
      if (ret) {
        activeModal.close();

        this.sinistre.extensions.push(ret);

      }else{
        this._toastr.error('Une erreur s\'est produite', 'Problème');
      }
    });
  }

  openModaleChangeSuivi(historique:IntraDoHistorique, index){
    const activeModal = this.modalService.open(ChangeSuiviModaleDirective, { size: 'xl', centered: false });
    activeModal.componentInstance.operation = this.operation;
    activeModal.componentInstance.suivi = historique;
    activeModal.componentInstance.clickeventUpdateSuivi.subscribe((ret:IntraDoHistorique) => {
      if (ret) {

        activeModal.close();
        // this.sinistre.historiques[index] = ret;
        this.loadHistoriques();


      }else{
        this._toastr.error('Une erreur s\'est produite', 'Problème');
      }
    });
  }

  openLogsMailgun(histoId: number){
    this.intraDoHistoriqueApiService.getMailguns(histoId, {where: {"entity": "IntraDoHistorique"}, order: "date DESC"}).subscribe((mailguns) => {
      const activeModal = this.modalService.open(ViewMailgunModaleDirective, { size: 'xl', centered: false });
      activeModal.componentInstance.libelle = 'Plateforme MULTInet';
      activeModal.componentInstance.mailguns = mailguns;
    })
  }


  setActualisationeRelaunch(histo, value){
    this.intraDoSinistreApiService.updateByIdHistoriques(this.sinistre.id, histo.id, {principal: value}).subscribe((histoUpdated) => {
      let histos = this.sinistre.historiques;
      let histoToReplace = histos.findIndex((h) => h.id === histo.id);
      if(histoToReplace > -1){
        histos.splice(histoToReplace, 1, histoUpdated);
      }

      this.sinistre.historiques = histos;
    })
  }

  addPhoto(){

    let datas = this.formAddphoto.getRawValue();
    datas['date'] = moment().utc(true);
    datas['peopleId'] = this.user.id;

    this.intraDoSinistreApiService.createPhotos(this.id, datas).subscribe((res) => {

      this._toastr.success('Succès !', 'Création de la photo')
      this.photos.push(res);

      this.formAddphoto.reset();
      this.uploadPhoto.reset();

      Object.keys(this.formAddphoto.controls).forEach(key => {
        this.formAddphoto.get(key).setErrors(null) ;
      });


    }, error => {
      this.formAddphoto.reset();
      this.uploadPhoto.reset();
      // console.log(error);
      this._toastr.error(error.message, 'Création de la photo')
    });
  }

  deletePhoto(photo:IntraMultiPhotos){

    let idphoto = photo.id;
    this.intraDoSinistreApiService.destroyByIdPhotos(this.id, idphoto).subscribe((res) => {

      this.photos = this.photos.filter((el) => {
        return el.id !== idphoto;
      });

      this._toastr.success('La photo a bien été supprimée', 'Suppression de la photo')

    }, error => {
      this._toastr.error(error.message, 'Suppression de la photo')
    });
  }

  addFile(){

    let datas = this.formAddfile.getRawValue();
    datas['date'] = moment().utc(true);
    datas['type'] = 1; // Upload
    datas['peopleId'] = this.user.id;

    this.intraDoSinistreApiService.createFiles(this.id, datas).subscribe((res) => {

      this._toastr.success('Succès !', 'Création du fichier')
      this.files.push(res);

      this.formAddfile.reset();
      this.uploadFile.reset();

      Object.keys(this.formAddfile.controls).forEach(key => {
        this.formAddfile.get(key).setErrors(null) ;
      });


    }, error => {
      // console.log(error);
      this.formAddfile.reset();
      this.uploadFile.reset();
      this._toastr.error(error.message, 'Création du fichier')
    });
  }

  openModaleAddDevisFacture(devis: IntraMultiDevis){

    const activeModal = this.modalService.open(DevisFactureMultiModaleDirective, { size: 'lg', centered: false });
    activeModal.componentInstance.devis = devis;
    activeModal.componentInstance.clickeventUpdateDevis.subscribe((ret:IntraMultiDevisFacture) => {
      activeModal.close();
      this.load()
    });

  }

  openModaleEditDevisFacture(devisFacture: IntraMultiDevisFacture, devis: IntraDoDevis){
    const activeModal = this.modalService.open(DevisFactureMultiModaleDirective, { size: 'lg', centered: false });
    activeModal.componentInstance.devis = devis;
    activeModal.componentInstance.devisFacture = devisFacture;
    activeModal.componentInstance.clickeventUpdateDevis.subscribe((ret:IntraMultiDevisFacture) => {
      activeModal.close();
      this.load()
    });
  }

  deleteFacture(devisFacture: IntraDoDevisFacture, devis: IntraDoDevis){
    this.intraDoDevisApiService.destroyByIdFactures(devis.id, devisFacture.id).subscribe((res) => {
      this._toastr.success('La facture a été supprimée', 'Suppression de la facture')
      this.load()
    })
  }



  // updateRappelAuto($event){
  //
  //   const rappel = (this.formRappel.controls['rappelAuto'].value ? 1 : 0);
  //   let dateRappel;
  //   let dateRappelString;
  //
  //   console.log($event)
  //
  //   if($event['nextRappelAuto']){
  //     dateRappel = moment({years: $event['nextRappelAuto'].year, months: $event['nextRappelAuto'].month -1, days: $event['nextRappelAuto'].day});
  //     dateRappelString = dateRappel.format('YYYY-MM-DD');
  //   } else {
  //     dateRappel = moment().add(3,'weeks');
  //     dateRappelString = dateRappel.format('YYYY-MM-DD');
  //   }
  //
  //   let datas = {};
  //
  //   if(rappel === 1){
  //     datas = {'rappelAuto': rappel, 'nextRappelAuto': dateRappelString}
  //   } else {
  //     datas = {'rappelAuto': rappel, 'nextRappelAuto': null}
  //   }
  //
  //   this.intraDoSinistreApiService.patchAttributes(this.sinistre.id, datas).subscribe((res) => {
  //     this._toastr.success('Le rappel auto de ce sinistre a été mis à jour', 'Mise à jour du rappel Automatique')
  //     this.sinistre.rappelAuto = rappel;
  //     this.sinistre.nextRappelAuto = dateRappel.toDate();
  //   });
  //
  //
  // }


  goToSuivi(){
    this.pageScrollService.scroll({
      document: this.document,
      scrollTarget: '#writeSuivi',
      duration: 1000,
      scrollOffset: 55
    });
  }

  annotateImage(img, modale){
    this.iframePixie = this.sanitizer.bypassSecurityTrustResourceUrl(`${this.parameters.containerImage.pixie.url}${encodeURIComponent(img.photo)}&type=image`);
    this.modalService.open(modale, {size: 'xl'})
  }

  getSafeHtml(content: string) {
    return this.sanitizer.bypassSecurityTrustHtml(content);
  }

  closeModaleEdit(modal){
    // Done -> reload images
    this.reloadPhotos();
    modal.dismiss();
  }

  reloadPhotos(){
    this.intraDoSinistreApiService.getPhotos(this.id).subscribe((res) => {
      this.photos = res;
    })
  }

  openModaleChangeCategorie(devis, type: 'categorie'|'montants'){
    const activeModal = this.modalService.open(ChangeCategorieDevisModaleDirective, { size: 'lg', centered: false });
    activeModal.componentInstance.devis = devis;
    activeModal.componentInstance.type = type;
    activeModal.componentInstance.clickeventUpdateDevis.subscribe((ret:IntraMultiDevis) => {
      if(ret){
        activeModal.close();

        this.load();

        // this.intraDoSinistreApiService.getDevis(this.sinistre.id).subscribe((devis) => {
        //   this.sinistre.devis = devis;

          // // Initialiser les totaux
          // this.sinistre.devisByCategorie = {
          //   totalHT: 0,
          //   totalTTC: 0,
          //   facturesMax: 0,
          //   totalExpert: 0,
          //   categories: {}
          // };
          //
          // // Grouper les devis par catégorie
          // this.sinistre.devis.forEach(devis => {
          //   const categorieId = devis.categorie_id;
          //   const categorieLibelle = devis.categorie.libelle || 'Sans catégorie';
          //
          //   // Calculer HT et TTC pour ce devis
          //   let ht = 0;
          //   let ttc = 0;
          //
          //   if (devis.total && devis.pourcentage) {
          //     ht = devis.total * (devis.pourcentage / 100);
          //     ttc = devis.total * (devis.pourcentage / 100) * (1 + (devis.TVA / 100));
          //   } else {
          //     ht = devis.montantdevis;
          //     ttc = devis.montantTTCdevis;
          //   }
          //
          //   // Ajouter aux totaux globaux
          //   this.sinistre.devisByCategorie.totalHT += ht;
          //   this.sinistre.devisByCategorie.totalTTC += ttc;
          //   this.sinistre.devisByCategorie.totalExpert += devis.TTC ? ttc : ht;
          //
          //   // Créer ou mettre à jour la catégorie
          //   if (!this.sinistre.devisByCategorie.categories[categorieLibelle]) {
          //     this.sinistre.devisByCategorie.categories[categorieLibelle] = {
          //       nb: 0,
          //       devis: [],
          //       totalHT: 0,
          //       totalTTC: 0,
          //       totalExpert:0
          //     };
          //   }
          //
          //   // Ajouter aux totaux de la catégorie
          //   this.sinistre.devisByCategorie.categories[categorieLibelle].totalHT += ht;
          //   this.sinistre.devisByCategorie.categories[categorieLibelle].totalTTC += ttc;
          //   this.sinistre.devisByCategorie.categories[categorieLibelle].devis.push(devis);
          // });
        //
        // })

      }else{
        this._toastr.error('Une erreur s\'est produite', 'Problème');
      }
    })
  }

  ngOnInit(): void {
    let self = this;

    combineLatest([this.appGlobalEventManagerService.listSinistresJ30Emitter, this.appGlobalEventManagerService.listSinistresJ27Emitter]).subscribe((dossiers) => {

      this.dossiersJ30 = dossiers[0];
      this.dossiersJ27 = dossiers[1];

      this.listDossiersAutorized = [
        ...this.dossiersJ30.map((dossier: any) => dossier.sinistreId),
        ...this.dossiersJ27.map((dossier: any) => dossier.sinistreId)
      ];

      this.getParameter();
    })


  }

  ngOnDestroy(){
    this._sessionService._currentMultiSinistre.next(null)
  }

  openModaleCreateSmileMood(sinistre: IntraMultiSinistre){
    const peoples = [];
    this.operation.responsablesRelations.forEach((responsable) => {
      if(responsable.role && responsable.role.code && responsable.role.code != 'MVEC' && responsable.role.code != 'VGE'){
        peoples.push(responsable.people);
      }
    })

    const activeModal = this.modalService.open(SmileMoodFormModale, {
      size: 'lg4',
      centered: false
    });
    activeModal.componentInstance.context = 'sinistreMulti';
    activeModal.componentInstance.destinataires = peoples;
    activeModal.componentInstance.sinistreMulti = sinistre;
    activeModal.componentInstance.libelle = `Sondage sinistre multi-risques référence ${this.sinistre.referencevge}, opération ${this.sinistre.operation.libelle}`;
    activeModal.componentInstance.subject = `Sondage sinistre multi-risques référence ${this.sinistre.referencevge}, opération ${this.sinistre.operation.libelle}`;
  }


  protected readonly usersVieira = usersVieira;
	protected readonly froalaOptions = WYSIWYGOptions;
}
