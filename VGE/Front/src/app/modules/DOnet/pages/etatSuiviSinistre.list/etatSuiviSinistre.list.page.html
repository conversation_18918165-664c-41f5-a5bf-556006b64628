<!-- Buttons TOP -->
<div class="row">
  <div class="col-12 mb-1 text-right">

    <button class="btn btn-success btn-social btn-sm mL5" [routerLink]="['/DOnet','etats-suivis-sinistre', 'edit', 'new']"><i class="ft-plus-square"></i> Ajouter un état</button>

  </div>
</div>
<!-- / Buttons TOP -->

<div class="content-body">

  <selfTablePaginate
    [data]="etatsSuiviSinistre$"
    [columns]="columns"
    [defaultKeyColumn]="'order'"
    [idKey]="'id'"
    [actionsTemplate]="actions"

    #list
    list="DOEtatsuivisinistre">
  </selfTablePaginate>

  <!--    [bodyTopTemplate]="bodyTop"-->
  <!--    [bodyBottomTemplate]="bodyBottom"-->
  <!--    [headerTopTemplate]="headerTop"-->
  <!--    [headerBottomTemplate]="headerBottom"-->
  <!--    [footerTopTemplate]="footerTop"-->
  <!--    [footerBottomTemplate]="footerBottom"-->
  <ng-template #actions let-object let-orderWay="orderWay" let-orderField="orderField">
    <div class="btn-group" role="group" aria-label="Basic example">
      <button type="button" [ngbTooltip]="'Modifier'" class="btn btn-icon btn-sm btn-success" [routerLink]="['/DOnet','etats-suivis-sinistre', 'edit', object.id]"><i class="ft-edit-3"></i></button>
      <button type="button" [ngbTooltip]="'Supprimer'"
              class="btn btn-icon btn-sm btn-danger"
              mwlConfirmationPopover
              [popoverTitle]="'Suppression Etat suivi DOnet'"
              [popoverMessage]="'Êtes vous sur de vouloir supprimer '+object.libelle+' ?'"
              placement="left"
              (confirm)="delete(object)"
      >
        <i class="fa-duotone fa-trash-alt"></i>
      </button>
    </div>

  </ng-template>

</div>
