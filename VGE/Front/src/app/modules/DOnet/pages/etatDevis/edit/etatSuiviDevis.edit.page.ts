import {Component, OnInit} from "@angular/core";
import {Activated<PERSON>oute, Router} from "@angular/router";
import {ToastrService} from "ngx-toastr";
import {LoopBackAuth} from "../../../../../shared/sdk/services/core";
import {
  IntraDoDevisCategorieApi,
  IntraDoEtatSuiviApi,
  IntraEtatApi,
  PeopleApi,
  SocietesApi
} from "../../../../../shared/sdk/services/custom";
import {IntraDoEtatSuivi, IntraEtat, People, Societes} from "../../../../../shared/sdk/models";
import {Observable, of, forkJoin} from "rxjs";
import {catchError, map, share} from "rxjs/operators";
import {TableColumnInterface} from "../../../../shared/interfaces/table.column.interface";
import {AppGlobalEventManagerService} from "../../../../../common/services/app.globalEventManager.service";
import {FormBuilder, FormGroup, Validators} from "@angular/forms";
import {AppCacheService} from "../../../../../common/services/app.cache.service";
import {EditPageInterface} from "../../../../shared/interfaces/edit.page.interface";
import {apiParameterInterface} from "../../../../shared/interfaces/apiParameter.interface";


@Component({
  selector: 'EtatSuiviDevisEditPage',
  templateUrl: './etatSuiviDevis.edit.page.html'
})
export class EtatSuiviDevisEditPage implements OnInit, EditPageInterface {

  public etat$: Observable<any>;
  public columns: Array<TableColumnInterface>;
  public hiddenColumns;
  public form: FormGroup;
  public id;
  public object = null;
  public modelDef;
  public apiParameters: apiParameterInterface;

  public folder = 'etatsuivisinistre'; // For Uploads !
  public mode = null;


  constructor(
    public router: Router,
    public route: ActivatedRoute,
    public formBuilder: FormBuilder,
    public _toastr: ToastrService,
    public authService: LoopBackAuth,
    public ApiService: IntraDoDevisCategorieApi,
    public appGlobalEventManagerService: AppGlobalEventManagerService,
    public appCacheService: AppCacheService

  ) {

    let self = this;

    this.appGlobalEventManagerService.getApiParameters().subscribe((parameters:apiParameterInterface) => {
      this.apiParameters = parameters
    });

    this.form = this.formBuilder.group({
      libelle: ['', [Validators.required]],
      couleur: ['', [Validators.required]],
      ordre: ['', [Validators.pattern('[0-9]+')]],
    });

    this.columns =
      [
        {type:"separator", name:"État", icon:"fa-duotone fa-bookmark", exclude:true},

        {key: "libelle",name: "Libelle", type: "input"},
        {key: "couleur",name: "Couleur", type: "color"},
        {key: "ordre",name: "Ordre", type: "input", typeInput:'number'},
      ];

    this.modelDef = IntraDoEtatSuivi.getModelDefinition();

  }

  saveObject(datas:object){

    if(this.mode == 'edit'){
      this.ApiService.patchAttributes(this.id, datas).pipe(
        map((res) => res),
        catchError((err) => {return of(err)})
      ).subscribe((res) => {
        if(res && !res.message){

          this._toastr.success('Succès !', 'Edition de l\'état')
          this.router.navigate(['/DOnet','etats-devis']);

        }else{
          this._toastr.error(res.message, 'Edition de l\'état');
        }
      });
    }else{
      /* Create */


      this.ApiService.create(datas).pipe(
        map((res) => res),
        catchError((err) => {return of(err)})
      ).subscribe((res) => {
        if(res && !res.message){

          this._toastr.success('Succès !', 'Création de l\'état')
          this.router.navigate(['/DOnet','etats-devis']);

        }else{
          this._toastr.error(res.message, 'Création de l\'état');
        }
      });
    }
  }

  getParameter(){
    this.route.paramMap.subscribe(params => {
      this.id = params.get("id");
      if(this.id === 'new'){
        this.mode = 'create';
        this.etat$ = of(null);
      }else{
        this.mode = 'edit';
        this.etat$ = this.ApiService.findById(this.id).pipe(share());
      }
    })
  }

  ngOnInit(): void {

    let self = this;

    this.getParameter();

    this.etat$.subscribe((etat:IntraEtat) => {
      if(etat){
        this.object = etat;

        /** Set Values **/
        this.columns.forEach((el) => {
          if(el.type != 'separator'  && (self.object[el.key] || self.object[el.key] === 0)){
            self.form.controls[el.key].setValue(self.object[el.key]);
          }
        })

        this.appGlobalEventManagerService.updateTitle('Edition état '+this.object.libelle);
        this.appGlobalEventManagerService.updateBreadcrumb([{title:'Home', url:'/'}, {title:'Liste des états suivis sinistres DOnet', url:'/DOnet/etats-devis'}, {title:'Edition état '+this.object.libelle, url:null }])
      }else{
        this.appGlobalEventManagerService.updateTitle('Création état');
        this.appGlobalEventManagerService.updateBreadcrumb([{title:'Home', url:'/'}, {title:'Liste des états suivis sinistres DOnet', url:'/DOnet/etats-devis'}, {title:'Création état ', url:null }])
      }
    });

  }

}
