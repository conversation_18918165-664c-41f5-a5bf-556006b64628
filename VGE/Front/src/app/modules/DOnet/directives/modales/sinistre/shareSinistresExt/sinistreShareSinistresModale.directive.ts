import {Component, EventEmitter, Input, OnInit, Output, ViewChild} from "@angular/core";
import {IntraDoOperation, IntraDoSinistre} from "../../../../../../shared/sdk/models";
import {TableColumnInterface} from "../../../../../shared/interfaces/table.column.interface";
import {FormBuilder, FormGroup, Validators} from "@angular/forms";
import {NgbActiveModal, NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {AppCacheService} from "../../../../../../common/services/app.cache.service";
import {ToastrService} from "ngx-toastr";
import {IntraDoSinistreApi} from "../../../../../../shared/sdk/services/custom";
import {forkJoin} from "rxjs";

@Component({
  selector: 'SinistreShareSinistresModaleDirective',
  templateUrl: './sinistreShareSinistresModale.directive.html',
})
export class SinistreShareSinistresModaleDirective implements OnInit{

  @Input() sinistresSubscribed: IntraDoSinistre[];
  @Input() operation: IntraDoOperation;
  @Output() clickeventUpdateSinistre = new EventEmitter<{[idsinistre:number]:number}>();

  public columnsFormVGI: Array<TableColumnInterface>;
  public columnsFormVGS: Array<TableColumnInterface>;

  public folder = 'sinistre'; // For Uploads !
  public formVGI: FormGroup;
  public formVGS: FormGroup;

  constructor(
    public activeModal: NgbActiveModal,
    public modalService: NgbModal,
    public _appCacheService: AppCacheService,
    public formBuilder: FormBuilder,
    public _toastr: ToastrService,
    public doSinistreApiService: IntraDoSinistreApi,
  ) {

    this.columnsFormVGI =
      [
        {type:"separator", name:"Liste des sinistres partagés avec VGI", icon:"fa-duotone fa-cog", exclude:true},
      ];

    this.columnsFormVGS =
      [
        {type:"separator", name:"Liste des sinistres partagés avec VGS", icon:"fa-duotone fa-cog", exclude:true},
      ];

  }


  saveVGI(datas:{[idsinistre:number]:number}){
    let self = this;
    if(Object.keys(datas).length > 0){
      const forks = [];
      Object.keys(datas).forEach((idsinistre) => {
        forks.push(
          this.doSinistreApiService.patchAttributes(idsinistre, {isVGI: datas[idsinistre]})
        )
      })
      forkJoin(forks).subscribe((res) => {
        this._toastr.success('Succès !', 'Modification du partage des sinistres VGI');
        this.clickeventUpdateSinistre.emit(datas);
        this.activeModal.close();
      })
    }
  }

  saveVGS(datas:{[idsinistre:number]:number}){
    let self = this;
    if(Object.keys(datas).length > 0){
      const forks = [];
      Object.keys(datas).forEach((idsinistre) => {
        forks.push(
          this.doSinistreApiService.patchAttributes(idsinistre, {isVGS: datas[idsinistre]})
        )
      })
      forkJoin(forks).subscribe((res) => {
        this._toastr.success('Succès !', 'Modification du partage des sinistres VGS');
        this.clickeventUpdateSinistre.emit(datas);
        this.activeModal.close();
      })
    }
  }

  ngOnInit(): void {

    const objFormVGI = {};
    const objFormVGS = {};

    if(this.sinistresSubscribed.length > 0){
      this.sinistresSubscribed.forEach((sinistre) => {

        this.columnsFormVGI.push(
          {
            type: "boolean",
            key: sinistre.id+'',
            name: sinistre.referencevge,
          }
        )

        this.columnsFormVGS.push(
          {
            type: "boolean",
            key: sinistre.id+'',
            name: sinistre.referencevge,
          }
        )

        if(sinistre.isVGI){
          objFormVGI[sinistre.id] = [true, []]
        } else {
          objFormVGI[sinistre.id] = [false, []]
        }

        if(sinistre.isVGS){
          objFormVGS[sinistre.id] = [true, []]
        } else {
          objFormVGS[sinistre.id] = [false, []]
        }

      })
    }

    this.formVGI = this.formBuilder.group(
      objFormVGI
    );

    this.formVGS = this.formBuilder.group(
      objFormVGS
    );

    // this.form.controls['state'].setValue(this.sinistre.state);

  }


}
