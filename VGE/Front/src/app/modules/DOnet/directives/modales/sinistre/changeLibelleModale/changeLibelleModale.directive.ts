import {Component, EventEmitter, Input, OnInit, Output, ViewChild} from "@angular/core";
import { IntraDoSinistre} from "../../../../../../shared/sdk/models";
import {TableColumnInterface} from "../../../../../shared/interfaces/table.column.interface";
import {FormBuilder, FormGroup, Validators} from "@angular/forms";
import {NgbActiveModal, NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {AppCacheService} from "../../../../../../common/services/app.cache.service";
import {ToastrService} from "ngx-toastr";
import {IntraDoSinistreApi} from "../../../../../../shared/sdk/services/custom";

@Component({
  selector: 'ChangeLibelleModaleDirective',
  templateUrl: './changeLibelleModale.directive.html',
})
export class ChangeLibelleModaleDirective implements OnInit{

  @Input() sinistre: IntraDoSinistre;
  @Output() clickeventUpdateSinistre = new EventEmitter<IntraDoSinistre>();

  public columnsForm: Array<TableColumnInterface>;

  public allsocietes: Array<{key:string, text:string}> = [];

  public folder = 'sinistre'; // For Uploads !
  public form: FormGroup;

  constructor(
    public activeModal: NgbActiveModal,
    public modalService: NgbModal,
    public _appCacheService: AppCacheService,
    public formBuilder: FormBuilder,
    public _toastr: ToastrService,
    public doSinistreApiService: IntraDoSinistreApi,
  ) {

    this.form = this.formBuilder.group({
      description: ['', [Validators.required]],
    });


    this.columnsForm =
      [
        {type:"separator", name:"Libellé et description du sinistre", icon:"fa-duotone fa-cog", exclude:true},
        {key: "description",name: "Libellé du sinistre", type: "text"},
      ];
  }


  save(datas:object){
    let self = this;

    this.doSinistreApiService.patchAttributes(this.sinistre.id, datas).subscribe((res:IntraDoSinistre) => {
      if(res && !res['message']){
        this._toastr.success('Succès !', 'Modification du libellé du sinistre');
        this.clickeventUpdateSinistre.emit(res);
      }else{
        this._toastr.error(res['message'], 'Modification du libellé du sinistre');
      }
    });

  }

  ngOnInit(): void {
    let self = this;

    this.form.controls['description'].setValue(this.sinistre.description);


  }


}
