import {Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild} from "@angular/core";
import {FormBuilder, FormGroup, Validators} from "@angular/forms";
import {NgbActiveModal, NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {ToastrService} from "ngx-toastr";
import moment from 'moment';
import {IntraDoEtatSuivi, IntraDoHistorique, IntraDoOperation, People} from "../../../../../../shared/sdk/models";
import {TableColumnInterface} from "../../../../../shared/interfaces/table.column.interface";
import {ROLES} from "../../../../../../datas/roles.helpers";
import {AppSessionService} from "../../../../../../common/services/app.session.service";
import {AppCacheService} from "../../../../../../common/services/app.cache.service";
import {IntraDoHistoriqueApi} from "../../../../../../shared/sdk/services/custom";
import {PeopleWithrightsInterface} from "../../../../../../common/interfaces/peopleWithrights.interface";

@Component({
  selector: 'ChangeSuiviModaleDirective',
  templateUrl: './changeSuiviModale.directive.html',
})
export class ChangeSuiviModaleDirective implements OnInit, OnChanges{

  @Input() suivi: IntraDoHistorique = null;
  @Input() operation: IntraDoOperation;
  @Output() clickeventUpdateSuivi = new EventEmitter<IntraDoHistorique>();

  public columnsForm: Array<TableColumnInterface>;

  public allsocietes: Array<{key:string, text:string}> = [];

  public folder = 'historique'; // For Uploads !
  public form: FormGroup;

  public etatsSuivis = [];

  public user: PeopleWithrightsInterface;
  public ROLES = ROLES;

  constructor(
    public activeModal: NgbActiveModal,
    public modalService: NgbModal,
    public _sessionService: AppSessionService,
    public _appCacheService: AppCacheService,
    public formBuilder: FormBuilder,
    public _toastr: ToastrService,
    public doHistoriqueApiService: IntraDoHistoriqueApi
  ) {

    this._sessionService.logguedAccountEmitter.subscribe((people) => {
      this.user = people;
    });

    this.form = this.formBuilder.group({
      idEtatSuivi:[null, [Validators.required]],
      comment: [null, []],
      date: ['', [Validators.required]],
      ftp:['', []],
      file:['', []],
      rappelAuto: [0, []],
      nextRappelAuto: [null, []],
    });

    this.columnsForm =
      [
        {type:"separator", name:"Modification d'un suivi", icon:"fa-duotone fa-cog", exclude:true},
        {key: "idEtatSuivi",name: "Etat du suivi", type: "select", selectOptions:{multiple:false, labelBind:'text', keyBind:'key', options:[]}},
        {key: "comment",name: "Actualisation du suivi sinistre", type: "text"},
        {key: "date",name: "Date du suivi", type: "date"},
        {key: "ftp",name: "Document FTP", type: "ftp", ftpOptions:{type:"file", folder:null}}, // a maj en fonction
        {key: "file",name: "Fichier", type: "file"},


      ];

  }


  updateParamsWithOperation(){
    let self = this;
    /**
     * Set root Folder to FTP !
     */
    if(self.operation && self.operation.dossierftp){
      let col = this.columnsForm.find((el) => {
        return el.key == 'ftp';
      });
      if(col){col.ftpOptions.folder = self.operation.dossierftp};
    }

  }

  save(datas:object){
    let self = this;

    const date = moment({years: datas['date'].year, months: datas['date'].month -1, days: datas['date'].day, hours: 12});
    datas['date'] = date;

    if(datas['nextRappelAuto']){
      const nextRappelAuto = moment({years: datas['nextRappelAuto'].year, months: datas['nextRappelAuto'].month -1, days: datas['nextRappelAuto'].day});
      datas['nextRappelAuto'] = nextRappelAuto.format('YYYY-MM-DD');
    }

    datas['id_etat_suivi'] = datas['idEtatSuivi'];

    this.doHistoriqueApiService.patchAttributes(this.suivi.id, datas).subscribe((res:IntraDoHistorique) => {
      if(res && !res['message']){

        this._toastr.success('Succès !', 'Modification du suivi');
        this.clickeventUpdateSuivi.emit(res);

      }else{
        this._toastr.error(res['message'], 'Modification du suivi');
      }
    });

  }

  ngOnChanges(changes: SimpleChanges) {
    if(changes.suivi.currentValue){
      if(this.suivi.to){
        this.enableRappelAuto();
      } else {
        this.disableRappelAuto();
      }
    }
  }


  ngOnInit(): void {
    let self = this;

    this._appCacheService.getIntraDoEtatsSuivi(false, null).subscribe((res:IntraDoEtatSuivi[]) => {
      res.forEach((el:IntraDoEtatSuivi) => {
        self.etatsSuivis.push({
          key: el.id,
          text: el.libelle
        });
      });

      let col = this.columnsForm.find((el) => {
        return el.key == 'idEtatSuivi';
      });
      if(col){col.selectOptions.options = self.etatsSuivis};

    });

    if(this.operation){
      this.updateParamsWithOperation();
    }

    if(this.suivi){
      this.form.controls['idEtatSuivi'].setValue(this.suivi.idEtatSuivi);

      let date = moment(this.suivi.date, 'YYYY-MM-DD').toObject();
      this.form.controls['date'].setValue({year:date.years, month:date.months+1, day: date.date});

      this.form.controls['comment'].setValue(this.suivi.comment);
      this.form.controls['file'].setValue( this.suivi.file);
      this.form.controls['ftp'].setValue(this.suivi.ftp);

      if(this.suivi.to){
        this.enableRappelAuto();
      } else {
        this.disableRappelAuto()
      }

    }
  }

  private disableRappelAuto(){
    delete this.columnsForm[this.columnsForm.findIndex((e) => e.key === 'sepRappelAuto')];
    delete this.columnsForm[this.columnsForm.findIndex((e) => e.key === 'rappelAuto')];
    delete this.columnsForm[this.columnsForm.findIndex((e) => e.key === 'nextRappelAuto')];
  }

  private enableRappelAuto(){

    this.columnsForm.push(...[
      {key: "sepRappelAuto", type:"separator", name:"Rappel auto, activation du système de rappel automatique sur cette actualisation", icon:"fa-duotone fa-bell", exclude:true},
      {key: "rappelAuto",name: "Activer / désactiver le rappel automatique", type: "boolean", required:true, explain:'' },
      {key: "nextRappelAuto",name: "Date du prochain rappel auto", type: "date", required:true, explain:'Par défaut toutes les 3 semaines.' },
    ])

    if(this.suivi.rappelAuto){
      this.form.controls['rappelAuto'].setValue(this.suivi.rappelAuto);
      const d = moment(this.suivi.nextRappelAuto, 'YYYY-MM-DD').toObject();
      this.form.controls['nextRappelAuto'].setValue({year:d.years, month:d.months+1, day: d.date});
    } else {
      const d = moment().add(3, 'weeks').toObject();
      this.form.controls['nextRappelAuto'].setValue({year:d.years, month:d.months+1, day: d.date});
    }

  }

}
