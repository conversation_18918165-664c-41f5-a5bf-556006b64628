import {Component, EventEmitter, Input, OnInit, Output, ViewChild} from "@angular/core";
import {IntraDoEtatSuivi, IntraDoOperation, IntraDoSinistre} from "../../../../../../shared/sdk/models";
import {TableColumnInterface} from "../../../../../shared/interfaces/table.column.interface";
import {FormBuilder, FormGroup, Validators} from "@angular/forms";
import {NgbActiveModal, NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {AppCacheService} from "../../../../../../common/services/app.cache.service";
import {ToastrService} from "ngx-toastr";
import {IntraDoSinistreApi} from "../../../../../../shared/sdk/services/custom";
import moment from 'moment';

@Component({
  selector: 'ChangeEnjeuModaleDirective',
  templateUrl: './changeEnjeuModale.directive.html',
})
export class ChangeEnjeuModaleDirective implements OnInit{

  @Input() sinistre: Partial<IntraDoSinistre>;
  @Output() clickeventUpdateSinistre = new EventEmitter<IntraDoSinistre>();

  public columnsForm: Array<TableColumnInterface>;


  public folder = 'sinistre'; // For Uploads !
  public form: FormGroup;

  constructor(
    public activeModal: NgbActiveModal,
    public modalService: NgbModal,
    public _appCacheService: AppCacheService,
    public formBuilder: FormBuilder,
    public _toastr: ToastrService,
    public doSinistreApiService: IntraDoSinistreApi
  ) {

    this.form = this.formBuilder.group({
      enjeu: ['', [Validators.required, Validators.pattern('[0-9.,]+')]],
    });


    this.columnsForm =
      [
        {type:"separator", name:"Changement du montant de l'enjeu du sinistre", icon:"fa-duotone fa-cog", exclude:true},
        {key: "enjeu",name: "Enjeu du sinistre", type: "input", typeInput:'number'},
      ];
  }


  save(datas:object){
    let self = this;

    /***
     * Get infos IntraDoHistorique !
     */

    // datas['mdate'] = moment().format('YYYY-MM-DD');

    this.doSinistreApiService.patchAttributes(this.sinistre.id, datas).subscribe((res:IntraDoSinistre) => {
      if(res && !res['message']){

        this._toastr.success('Succès !', 'Changement du montant de l\'enjeu');
        this.clickeventUpdateSinistre.emit(res);

      }else{
        this._toastr.error(res['message'], 'Changement du montant de l\'enjeu');
      }
    });

  }

  ngOnInit(): void {
    if(this.sinistre && this.sinistre.enjeu) {
      this.form.controls['enjeu'].setValue(this.sinistre.enjeu);
    }
  }


}
