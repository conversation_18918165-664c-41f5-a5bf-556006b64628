<div class="form-group row" [formGroup]="(form) ? form : null" [ngClass]="{'validate': f && f[formControlName].valid, 'error': submitted && f && f[formControlName].errors}">
  <label [for]="name+range" class="col-md-3 label-control">{{label}} <span class="text-danger" *ngIf="required === true">*</span></label>
  <div class="col-md-9">
    <small *ngIf="explain"><i [innerHTML]="explain"></i></small>
    <div class="controls">
      <ng-select
        #select
        [items]="options"
        [multiple]="false"
        [id]="name+range"
        [attr.closeOnSelect]="true"
        [searchable]="true"
        [bindLabel]="labelBind"
        [bindValue]="keyBind"
        [groupBy]="groupBy"
        [attr.selectableGroup]="selectableGroup"
        [attr.selectableGroupAsModel]="selectableGroupAsModel"
        [placeholder]="label"
        formControlName="{{formControlName}}"
      >
      </ng-select>
    </div>

    <p class="mt-1" *ngIf="submitted && f[formControlName].errors" class="parsley-errors-list filled">
      <span *ngIf="f && f[formControlName].errors.required" class="text-danger">{{label}} est obligatoire</span>
    </p>

    <button type="button" class="btn btn-info btn-sm btn-block mT10" (click)="openListIntervenantsModale()"><i class="fa-duotone fa-user-tie"></i> {{libelleBtn}}</button>

  </div>
</div>
