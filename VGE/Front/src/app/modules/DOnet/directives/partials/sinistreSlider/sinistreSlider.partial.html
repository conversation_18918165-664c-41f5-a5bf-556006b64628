<div id="colDO" *ngIf="currentSinistre" #colRight2 class="colRight sticky-wrapper" [ngClass]="{'open': colRight2IsActive}">
  <div class="handler text-center" (click)="toggleSidebar2()">
    <span class="badge vertical-text mB5" [ngClass]="{'badge-warning': nbAlertes > 0, 'badge-success': nbAlertes == 0}" [ngbTooltip]="nbAlertes+' alertes actives'" placement="left">{{nbAlertes}}</span><br/>
    <span class="vertical-text">Alertes {{currentSinistre?.referencevge}}</span><br/>
  </div>
  <div class="sidebar h-100">
    <div class="sidebar-content card h-100">
      <div class="card-body h-100">

<!--        <perfect-scrollbar>-->
          <h4 class="card-title text-center">Paramètres sinistre {{currentSinistre?.referencevge}}</h4>
          <div id="accordionWrapDO" class="h-100" role="tablist" aria-multiselectable="true">
            <div class="card collapse-icon accordion-icon-rotate left py-0 px-0">
              <div id="headingDO" class="card-header py-0">
                <a data-toggle="collapse" href="#accordionDO" aria-expanded="false" aria-controls="accordion21" class="card-title lead fs-13">Alertes sinistre</a>
              </div>
              <div id="accordionDO" role="tabpanel" data-parent="#accordionWrapJ" aria-labelledby="headingJ35" class="collapse show">
                <div class="card-content">
                  <div class="card-body row">

                    <table class="table table-compact table-sm table-striped fs-12">
                      <tbody>
                        <tr *ngIf="currentSinistre?.alertes?.bilan">
                          <th class="text-{{currentSinistre?.alertes?.bilan?.type}}">Bilan financier</th>
                          <td>
                            <span [ngClass]="{'badge-success': currentSinistre?.alertes?.bilan?.type === 'success', 'badge-warning': currentSinistre?.alertes?.bilan?.type === 'warning', 'badge-danger': currentSinistre?.alertes?.bilan?.type === 'danger'}" class="badge">
                              <i [ngClass]="{'fa-check-circle': currentSinistre?.alertes?.bilan?.type === 'success', 'fa-exclamation-triangle': currentSinistre?.alertes?.bilan?.type === 'warning', 'fa-warning': currentSinistre?.alertes?.bilan?.type === 'danger'}" class="fa fa-duotone fa-2x"></i>
                            </span>
                          </td>
                          <td>
                            <span *ngIf="currentSinistre?.alertes?.bilan?.reason" [innerHTML]="currentSinistre?.alertes?.bilan?.reason"></span>
                            <span *ngIf="currentSinistre?.alertes?.bilan?.detail"><br/>{{currentSinistre?.alertes?.bilan?.detail}}</span>
                          </td>
                          <td></td>
                        </tr>

                        <tr>
                          <th class="text-{{currentSinistre?.alertes?.indemnites?.type}}">Indemnités</th>
                          <td>
                            <span [ngClass]="{'badge-success': currentSinistre?.alertes?.indemnites?.type === 'success', 'badge-warning': currentSinistre?.alertes?.indemnites?.type === 'warning', 'badge-danger': currentSinistre?.alertes?.indemnites?.type === 'danger'}" class="badge">
                              <i [ngClass]="{'fa-check-circle': currentSinistre?.alertes?.indemnites?.type === 'success', 'fa-exclamation-triangle': currentSinistre?.alertes?.indemnites?.type === 'warning', 'fa-warning': currentSinistre?.alertes?.indemnites?.type === 'danger'}" class="fa fa-duotone fa-2x"></i>
                            </span>
                          </td>
                          <td>
                            <span *ngIf="currentSinistre?.alertes?.indemnites?.reason" [innerHTML]="currentSinistre?.alertes?.indemnites?.reason"></span>
                            <span *ngIf="currentSinistre?.alertes?.indemnites?.detail"><br/>{{currentSinistre?.alertes?.indemnites?.detail}}</span>
                          </td>
                          <td></td>
                        </tr>

                        <tr>
                          <th class="text-{{currentSinistre?.alertes?.devis?.type}}">Devis</th>
                          <td>
                            <span [ngClass]="{'badge-success': currentSinistre?.alertes?.devis?.type === 'success', 'badge-warning': currentSinistre?.alertes?.devis?.type === 'warning', 'badge-danger': currentSinistre?.alertes?.devis?.type === 'danger'}" class="badge">
                              <i [ngClass]="{'fa-check-circle': currentSinistre?.alertes?.devis?.type === 'success', 'fa-exclamation-triangle': currentSinistre?.alertes?.devis?.type === 'warning', 'fa-warning': currentSinistre?.alertes?.devis?.type === 'danger'}" class="fa fa-duotone fa-2x"></i>
                            </span>
                          </td>
                          <td>
                            <span *ngIf="currentSinistre?.alertes?.devis?.reason" [innerHTML]="currentSinistre?.alertes?.devis?.reason"></span>
                            <span *ngIf="currentSinistre?.alertes?.devis?.detail"><br/>{{currentSinistre?.alertes?.devis?.detail}}</span>
                          </td>
                          <td></td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>

              <!--                  <div id="headingCGPT" class="card-header py-0">-->
              <!--                    <a data-toggle="collapse" href="#accordionCGPT" aria-expanded="false" aria-controls="accordion21" class="card-title lead fs-13">Chat GPT</a>-->
              <!--                  </div>-->
              <!--                  <div id="accordionCGPT" role="tabpanel" data-parent="#accordionWrapCGPT" aria-labelledby="headingCGPT" class="collapse show">-->
              <!--                    <div class="card-content">-->
              <!--                      <div class="card-body row">-->



              <!--                      </div>-->
              <!--                    </div>-->
              <!--                  </div>-->

            </div>
          </div>

<!--        </perfect-scrollbar>-->
      </div>
    </div>

  </div>
</div>
