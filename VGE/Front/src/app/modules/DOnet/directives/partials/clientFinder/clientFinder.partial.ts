import {Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges, ViewChild} from "@angular/core";
import {NgSelectComponent} from "@ng-select/ng-select";
import {
  IntraDoOperationApi,
  Societes
} from "../../../../../shared/sdk";
import {AppCacheService} from "../../../../../common/services/app.cache.service";
import {Observable} from "rxjs";
import moment from 'moment';
import 'moment-duration-format';

export interface clientDo {
  client: string
  clientId: number
  fond?: string
  fondId?: number
  fondParentId?: number
  fonds?: clientFondDo[]
}

export interface clientFondDo {
  clientId: number
  client: string
  fond: string
  fondParent: string
  fondParentId: number
  fonds?: clientFondDo[]
}

@Component({
  selector: 'clientFinder',
  templateUrl: './clientfinder.partial.html'
})
export class ClientFinderPartial implements OnInit, OnChanges{

  protected readonly moment = moment;

  public clients$ : Observable<{[client: string]: clientDo}[]> = new Observable<{[client: string]: clientDo}[]>(); // step 0
  public clientsSubs: clientDo[];

  @ViewChild('select', {static:false}) select: NgSelectComponent
  @Input() showButtonsLinks: boolean = true;

  @Output('selectClientEmitter') selectClientEmitter: EventEmitter<clientDo | clientFondDo> = new EventEmitter();
  @Output('listLoaded') listLoadedEmitter: EventEmitter<boolean> = new EventEmitter();

  public currentClient: clientDo | clientFondDo = null;

  constructor(
    public _cacheService: AppCacheService,
    public _operationApiService: IntraDoOperationApi

  ) {

  }

  load(){
    this.clients$ = this._cacheService.getListClientsDo();
  }

  reset(){
    this.currentClient = null;
  }

  selectClient(){
    this.selectClientEmitter.emit(this.currentClient)
  }

  ngOnInit() {

    this.load();

    this.clients$.subscribe((clientsFonds) => {
      const arr = []
      for(let cli of Object.keys(clientsFonds)){
        clientsFonds[cli]['client'] = cli;
        arr.push(clientsFonds[cli]);

        if(clientsFonds[cli]['fonds']){
          clientsFonds[cli]['fonds'].forEach(fond => {
            fond.client = fond.fondParent + ' > ' + fond.client
            arr.push(fond);
          })
        }
      }
      this.clientsSubs = JSON.parse(JSON.stringify(arr))

      this.listLoadedEmitter.emit(true);

    })

  }

  ngOnChanges(changes: SimpleChanges) {
  }


}
