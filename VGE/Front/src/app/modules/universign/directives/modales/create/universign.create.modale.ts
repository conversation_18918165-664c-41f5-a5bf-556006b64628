import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import {
  IntraDevDevelopmentApi, People, Universign,
} from '../../../../../shared/sdk';
import moment from 'moment';
import {AppGlobalEventManagerService} from "../../../../../common/services/app.globalEventManager.service";
import {AppSessionService} from "../../../../../common/services/app.session.service";
import {PeopleWithrightsInterface} from "../../../../../common/interfaces/peopleWithrights.interface";
import {ToastrService} from "ngx-toastr";
import {FormBuilder} from "@angular/forms";
import {AppCacheService} from "../../../../../common/services/app.cache.service";

@Component({
  selector: 'UniversignCreateModale',
  templateUrl: './universign.create.modale.html'
})
export class UniversignCreateModaleDirective implements OnInit {

  @Output() createUniversignObject = new EventEmitter<Universign>();

  public user: PeopleWithrightsInterface;

  protected moment = moment;

  public folder:string = 'universign';

  public peoples: Partial<People>[] | People[] = [];

  constructor(
    public activeModal: NgbActiveModal,
    public _sessionService: AppSessionService,
    private _apiService: IntraDevDevelopmentApi,
    private toastr: ToastrService,
    public formBuilder: FormBuilder,
    private appGlobalEventManagerService: AppGlobalEventManagerService,
    private _cacheService: AppCacheService,
  ) {

    this._sessionService.logguedAccountEmitter.subscribe((people) => {
      this.user = people;
    })

    this._cacheService.getPeoples().subscribe((clients) => {
      this.peoples = clients;
    })

  }


  ngOnInit() {
  }

}
