import {Component, EventEmitter, OnInit, Output} from "@angular/core";
import {IntraEbuzOperation} from "../../../../../../shared/sdk/models";
import {TableColumnInterface} from "../../../../../shared/interfaces/table.column.interface";
import {FormBuilder, FormGroup, Validators} from "@angular/forms";
import {NgbActiveModal, NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {ToastrService} from "ngx-toastr";
import {IntraEbuzOperationApi} from "../../../../../../shared/sdk/services/custom";
import {Observable} from "rxjs";

@Component({
  selector: 'CreateEcheancierModaleDirective',
  templateUrl: './createEcheancierModale.directive.html',
})
export class CreateEcheancierModaleDirective implements OnInit{

  @Output() clickeventAddEcheancier = new EventEmitter<IntraEbuzOperation>();

  public columnsForm: Array<TableColumnInterface>;

  public operations$: Observable<IntraEbuzOperation[]> = new Observable<IntraEbuzOperation[]>();
  public operations: IntraEbuzOperation[] = [];

  public folder = 'operations'; // For Uploads !
  public form: FormGroup;

  constructor(
    public activeModal: NgbActiveModal,
    public modalService: NgbModal,
    public formBuilder: FormBuilder,
    public _toastr: ToastrService,
    public _apiService: IntraEbuzOperationApi,
  ) {

    this.columnsForm =
      [
        {type:"separator", name: "Ajout d'un échéancier sur l'opération", icon:"fa-duotone fa-cog", exclude:true},
        {key: "operation",name: 'Opérations', type: "select", selectOptions:{multiple: false, labelBind: 'libelle', keyBind: 'id', options: []}},
      ];

    this.form = this.formBuilder.group({
      operation: ['', [Validators.required]],
    });

    this.operations$ = this._apiService.find({where: {and: [{echeancierGPA: 0}, {isgpa: 1}]}, fields: ['id', 'libelle']});
    this.operations$.subscribe((ops: IntraEbuzOperation[]) => {
      this.operations = ops;

      const element = this.columnsForm.find((el) => el.key === 'operation')
      element.selectOptions.options = this.operations;
    })
  }

  save(datas:object){
    let self = this;

    const operationId = datas['operation'];
    const operation = this.operations.find((el) => el.id === operationId);
    if(!operation.contrats){
      this._toastr.error('Impossible de créer l\'échéancier, celui ci n\'a pas de contrat rattaché', 'Ajout de d\'échancier sur une opération');
      return;
    }

    this._apiService.patchAttributes(operation.id, {echeancierGPA: 1}).subscribe((res:IntraEbuzOperation) => {
      if(res && !res['message']){
        this._toastr.success('Succès !', 'Ajout de d\'échancier sur une opération');
        this.clickeventAddEcheancier.emit(res);
      }else{
        this._toastr.error(res['message'], 'Ajout de d\'échancier sur une opération');
      }
    });

  }

  ngOnInit(): void {
    let self = this;


  }


}
