import {Component, EventEmitter, Input, OnInit, Output} from "@angular/core";
import {Form<PERSON>uilder, FormGroup, Validators} from "@angular/forms";
import {NgbActiveModal, NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {ToastrService} from "ngx-toastr";
import moment from 'moment';
import {IntraEbuzDepense, IntraEbuzDepenseApi, People} from "../../../../../../shared/sdk";
import {AppSessionService} from "../../../../../../common/services/app.session.service";
import {ROLES} from "../../../../../../datas/roles.helpers";
import {TableColumnInterface} from "../../../../../shared/interfaces/table.column.interface";
import {PeopleWithrightsInterface} from "../../../../../../common/interfaces/peopleWithrights.interface";

@Component({
  selector: 'DepensePayeeModale',
  templateUrl: './depensePayee.directive.html',
})
export class DepensePayeeDirective implements OnInit{

  @Input('depense') depense: IntraEbuzDepense = null;
  @Output() clickeventUpdate = new EventEmitter<IntraEbuzDepense>();

  public columnsForm: Array<TableColumnInterface>;

  public folder = 'depenses'; // For Uploads !
  public form: FormGroup;

  public user: PeopleWithrightsInterface;
  public ROLES = ROLES;

  constructor(
    public activeModal: NgbActiveModal,
    public modalService: NgbModal,
    public _sessionService: AppSessionService,
    public formBuilder: FormBuilder,
    public _toastr: ToastrService,
    public _depenseApiService: IntraEbuzDepenseApi
  ) {
    this._sessionService.logguedAccountEmitter.subscribe((people) => {
      this.user = people;
    });
  }

  save(datas:object){
    let self = this;

    let date = moment({years: datas['datePaye'].year, months: datas['datePaye'].month -1, days: datas['datePaye'].day, hours: 12});
    let pay = datas['pay'];

    let datasdepense = {
      datePaye: (pay === true) ? null :date,
    }

    if(this.depense){
        this._depenseApiService.patchAttributes(this.depense.id, datasdepense).subscribe((res:IntraEbuzDepense) => {
          if(res && !res['message']){
            this._toastr.success('Succès !', 'Date de paiement mis à jour');
            this.clickeventUpdate.emit(res);
          }else{
            this._toastr.error(res['message'], 'Date de paiement mis à jour');
          }
        });
    }
  }

  ngOnInit(): void {
    let self = this;


    this.form = this.formBuilder.group({
      datePaye: ['', [Validators.required]],
      pay: [false, []],
    });

    this.columnsForm =
      [
        {type:"separator", name: "Définir la date de paiement de la dépense", icon:"fa-duotone fa-money-bill-alt", exclude:true},
        {key: "datePaye",name: "Date du paiement", type: "date"},
        {key: "pay",name: "Cocher pour supprimer le fait que ce soit payé", type: "boolean"},
      ];


    /* Set Values ! */
    if(this.depense){
      let date = moment(this.depense.datePaye, 'YYYY-MM-DD').toObject();
      this.form.controls['datePaye'].setValue({year: date.years, month:date.months+1, day: date.date});
      this.form.controls['pay'].setValue(false);
    }
  }
}
