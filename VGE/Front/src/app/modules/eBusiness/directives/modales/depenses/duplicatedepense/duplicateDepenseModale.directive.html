
<div class="modal-header">
  <h4 class="modal-title">
    Dupliquer une {{(depense.type === -1) ? 'recette': 'dépense'}}
  </h4>
  <button type="button" class="close" aria-label="Close" (click)="activeModal.dismiss('Cross click')">
    <span aria-hidden="true">&times;</span>
  </button>
</div>

<globalForms
  [data]="null"
  [columns]="columnsForm"
  [form]="form"
  (sendDatas)="save($event)"
  [folder]="folder"
  [back]="null"
  [bodyBottomTemplate]="bottom"
  (changeValue)="key($event)"
>
</globalForms>

<ng-template #bottom >
  <table class="table-hover table table-bordered w-100" *ngIf="depense?.file || depense?.file2 || depense?.file3 || depense?.file4 || depense?.file5">
    <thead>
      <tr>
        <th colspan="5" class="text-center">Suppression de fichiers</th>
      </tr>
      <tr>
        <th>Fichier</th>
        <th>Fichier 2</th>
        <th>Fichier 3</th>
        <th>Fichier 4</th>
        <th>Fichier 5</th>
      </tr>
    </thead>
    <tr>
      <td>
        <button type="button" class="btn btn-outline-danger"
                *ngIf="depense?.file"
                mwlConfirmationPopover
                [popoverTitle]="'Suppression du fichier ?'"
                [popoverMessage]="'Êtes vous sur de vouloir supprimer ce fichier ?'"
                placement="left"
                (confirm)="deleteFile('file')"
        >
          <i class="ft-trash-2"></i>
        </button>
      </td>
      <td>
        <button type="button" class="btn btn-outline-danger"
                *ngIf="depense?.file2"
                mwlConfirmationPopover
                [popoverTitle]="'Suppression du fichier ?'"
                [popoverMessage]="'Êtes vous sur de vouloir supprimer ce fichier ?'"
                placement="left"
                (confirm)="deleteFile('file2')"
        >
          <i class="ft-trash-2"></i>
        </button>
      </td>
      <td>
        <button type="button" class="btn btn-outline-danger"
                *ngIf="depense?.file3"
                mwlConfirmationPopover
                [popoverTitle]="'Suppression du fichier ?'"
                [popoverMessage]="'Êtes vous sur de vouloir supprimer ce fichier ?'"
                placement="left"
                (confirm)="deleteFile('file3')"
        >
          <i class="ft-trash-2"></i>
        </button>
      </td>
      <td>
        <button type="button" class="btn btn-outline-danger"
                *ngIf="depense?.file4"
                mwlConfirmationPopover
                [popoverTitle]="'Suppression du fichier ?'"
                [popoverMessage]="'Êtes vous sur de vouloir supprimer ce fichier ?'"
                placement="left"
                (confirm)="deleteFile('file4')"
        >
          <i class="ft-trash-2"></i>
        </button>
      </td>
      <td>
        <button type="button" class="btn btn-outline-danger"
                *ngIf="depense?.file5"
                mwlConfirmationPopover
                [popoverTitle]="'Suppression du fichier ?'"
                [popoverMessage]="'Êtes vous sur de vouloir supprimer ce fichier ?'"
                placement="left"
                (confirm)="deleteFile('file5')"
        >
          <i class="ft-trash-2"></i>
        </button>
      </td>
    </tr>
  </table>

</ng-template>
