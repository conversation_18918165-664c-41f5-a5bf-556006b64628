import {Component, EventEmitter, Input, OnInit, Output, SimpleChanges, ViewChild} from "@angular/core";
import {
  IntraEbuzContratProspect, IntraEbuzContratProspectCommercialEtat, IntraEbuzContratProspectPrestation
} from "../../../../../../shared/sdk/models";
import {TableColumnInterface} from "../../../../../shared/interfaces/table.column.interface";
import {FormBuilder, FormGroup, Validators} from "@angular/forms";
import {NgbActiveModal, NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {AppCacheService} from "../../../../../../common/services/app.cache.service";
import {ToastrService} from "ngx-toastr";
import {
  IntraEbuzContratProspectApi,
  IntraEbuzContratProspectCommercialEtatApi
} from "../../../../../../shared/sdk/services/custom";
import {ROLES} from "../../../../../../datas/roles.helpers";
import {AppSessionService} from "../../../../../../common/services/app.session.service";
import {PeopleWithrightsInterface} from "../../../../../../common/interfaces/peopleWithrights.interface";
import moment from 'moment';
import {catchError, map, shareReplay} from "rxjs/operators";
import {etatsContratProspectOptions} from "../../../../models/constsEb";
import {forkJoin, of} from "rxjs";

@Component({
  selector: 'ChangeEtatSinistreModaleDirective',
  templateUrl: './changeEtatPropositionModale.directive.html',
})
export class ChangeEtatPropositionModaleDirective implements OnInit{

  @Input() proposition: IntraEbuzContratProspect;
  @Output() clickeventUpdateProposition = new EventEmitter<IntraEbuzContratProspect>();

  public columnsForm: Array<TableColumnInterface>;

  public allsocietes: Array<{key:string, text:string}> = [];

  public folder = 'proposition'; // For Uploads !
  public form: FormGroup;

  public responsables = [];

  public user: PeopleWithrightsInterface;
  public ROLES = ROLES;

  constructor(
    public activeModal: NgbActiveModal,
    public modalService: NgbModal,
    public _sessionService: AppSessionService,
    public _appCacheService: AppCacheService,
    public formBuilder: FormBuilder,
    public _toastr: ToastrService,
    public etatCommercialApiService: IntraEbuzContratProspectCommercialEtatApi,
    public apiService: IntraEbuzContratProspectApi
  ) {

    this._sessionService.logguedAccountEmitter.subscribe((people) => {
      this.user = people;
    });

    this._sessionService.logguedAccountEmitter.subscribe((people) => {
      this.user = people;
    });

    this.form = this.formBuilder.group({
      libelle: ['', [Validators.required]],
      description: ['', []],
      operation_id: [(this.proposition) ? this.proposition.operation_id : null, []],
      subject: ['', []],
      etat_id: ['', [Validators.required]],
      etat: ['', ],
      date: ['', [Validators.required]],

      ICCsigne: ['', []],

      responsablecompta_id: ['', []],
      responsablecompta2_id: ['', []],
      responsablecompta3_id: ['', []],
      responsablecompta4_id: ['', []],
      responsablecompta5_id: ['', []],

      file: ['', [Validators.required]],
      file2: ['', []],
      file3: ['', []],
      file4: ['', []],
      file5: ['', []],

      lignes: [[], []]

    });

    this.columnsForm =
      [
        {type:"separator", name:"Modification état proposition", icon:"fa-duotone fa-cog", exclude:true},

        {key: 'libelle', name:"Nom", type: 'input', order:false, filter:false},
        {key: 'operation_id', name:"Opération", explain:"Opération prospect", type: 'selectEbuzOperation', extradatas: null, order:false, filter:true, selectOptions:{options:[], keyBind: 'key', labelBind: 'text'}, exclude:true},
        {key: 'etat', name:"Statut", type: 'select', order:false, filter:true, selectOptions:{options: etatsContratProspectOptions, keyBind: 'key', labelBind: 'text'}, exclude:true},
        {key: 'etat_id', name:"État", type: 'select', order:false, filter:true, selectOptions:{options:[], keyBind: 'id', labelBind: 'libelle'}, exclude:true},

        {key: 'description', name:"Description", explain:'Description, explications, note, interne.', type: 'text', order:false, filter:false},
        {key: 'subject', name:"Objet par défaut du mail", explain:'', type: 'input', order:false, filter:false},

        {key: 'date', name:"Date de la proposition", type: 'date', order:false, filter:false},

        {type:"separator", name:"ICC", icon:"fa-duotone fa-cog", exclude:true},
        {key: 'ICCsigne', name:"Indice ICC prévu", explain: '', type: 'input', typeInput:'number', order:false, filter:false},

        {type:"separator", name:"Contacts. Nécessaire pour les envois d'email", icon:"fa-duotone fa-users", exclude:true},
        {key: 'responsablecompta_id', name:"Responsable 1", type: 'selectPeople', order:false, filter:false},
        {key: 'responsablecompta2_id', name:"Responsable 2", type: 'selectPeople', order:false, filter:false},
        {key: 'responsablecompta3_id', name:"Responsable 3", type: 'selectPeople', order:false, filter:false},
        {key: 'responsablecompta4_id', name:"Responsable 4", type: 'selectPeople', order:false, filter:false},
        {key: 'responsablecompta5_id', name:"Responsable 5", type: 'selectPeople', order:false, filter:false},


        {type:"separator", name:"Fichiers joints", icon:"fa-duotone fa-files", exclude:true},
        {key: 'file', name:"Proposition jointe", type: 'file', order:false, filter:false},
        {key: 'file2', name:"Fichier joint 1", type: 'file', order:false, filter:false},
        {key: 'file3', name:"Fichier joint 2", type: 'file', order:false, filter:false},
        {key: 'file4', name:"Fichier joint 3", type: 'file', order:false, filter:false},
        {key: 'file5', name:"Fichier joint 4", type: 'file', order:false, filter:false},

        {type:"separator", name:"Prestations", icon:"fa-duotone fa-cubes", exclude:true},
        {key: "lignes", name: "Lignes", type: "lignePrestationProspect", selectOptions:{options:[]}, exclude:true, readonly: false},

      ];
  }



  save(datas:object){

    // console.log(datas);
    //
    // let date = moment({years: datas['date'].year, months: datas['date'].month -1, days: datas['date'].day, hours: 12});//.format('YYYY-MM-DD');
    // datas['date'] = date;
    //
    // this.apiService.patchAttributes(this.proposition.id, datas).subscribe((res:IntraEbuzContratProspect) => {
    //   if(res && !res['message']){
    //     this.apiService.findOne({where: {id: this.proposition.id}}).subscribe((propUpdated:IntraEbuzContratProspect) => {
    //       this._toastr.success('Succès !', 'Modification de la proposition');
    //       this.clickeventUpdateProposition.emit(propUpdated);
    //     })
    //   }else{
    //     this._toastr.error(res['message'], 'Modification de la proposition');
    //   }
    // });

    if(!datas['responsablecompta_id']){
      this._toastr.warning('Attention vous devez définir un contact de comptabilité', 'Formulaire sur la création de contrat prospect')
      return;
    }

    if(datas['date']){
      let date = moment({years: datas['date'].year, months: datas['date'].month -1, days: datas['date'].day, hours: 12});//.format('YYYY-MM-DD');
      datas['date'] = date;
    }

    if(datas['debut']){
      let debut = moment({years: datas['debut'].year, months: datas['debut'].month -1, days: datas['debut'].day, hours: 12});//.format('YYYY-MM-DD');
      datas['debut'] = debut;
    }

    if(datas['fin']){
      let fin = moment({years: datas['fin'].year, months: datas['fin'].month -1, days: datas['fin'].day, hours: 12});//.format('YYYY-MM-DD');
      datas['fin'] = fin;
    }

    // let dateSigne = moment({years: datas['dateSigne'].year, months: datas['dateSigne'].month -1, days: datas['dateSigne'].day, hours: 12});//.format('YYYY-MM-DD');
    // datas['dateSigne'] = dateSigne;

    if(datas['lignes']){
      const err : string[] = []
      datas['lignes'].forEach((ligne: IntraEbuzContratProspectPrestation, index: number) => {
        if((!ligne.prestation_id || !ligne.montant || !ligne.recurenceFacture || !ligne.typeMontant) && ligne.typeMontant !== 'pourcent'){
          err.push('Une prestation n\'a pas été complétée, elle n\'est pas valide, veuillez compléter')
        }
      })

      if(err.length > 0){
        this._toastr.warning(err.join('<br/>'),'Enregistrement des prestations', {enableHtml: true})
        return;
      }
    }

    this.apiService.patchAttributes(this.proposition.id, datas).pipe(
      map((res) => res),
      catchError((err) => {return of(err)})
    ).subscribe((res) => {
      if(res && !res.message){

        if(datas && datas['lignes'] && datas['lignes'].length > 0){

          const aModifier: IntraEbuzContratProspectPrestation[] = [];
          const aCreer: IntraEbuzContratProspectPrestation[] = [];

          datas['lignes'].forEach((ligne: IntraEbuzContratProspectPrestation) => {
            ligne.date = moment().format('YYYY-MM-DD');
            delete ligne.prestation
            delete ligne.contratProspect;

            delete ligne.etat_id;

            if(ligne.id){
              aModifier.push(ligne)
            }else{
              aCreer.push(ligne)
            }
          })

          const forkSubscribes = [];
          if(aCreer.length > 0){
            aCreer.forEach((aCre: IntraEbuzContratProspectPrestation) => {
              forkSubscribes.push(this.apiService.createPrestations(this.proposition.id, aCre))
            })
          }

          if(aModifier.length > 0){
            aModifier.forEach((aMod: IntraEbuzContratProspectPrestation) => {
              forkSubscribes.push(this.apiService.updateByIdPrestations(this.proposition.id, aMod.id, aMod));
            })
          }

          if(forkSubscribes.length > 0) {
            forkJoin(forkSubscribes).subscribe((r) => {
              this.apiService.findOne({where: {id: this.proposition.id}}).subscribe((propUpdated:IntraEbuzContratProspect) => {
                this._toastr.success('Succès !', 'Modification de la proposition');
                this.clickeventUpdateProposition.emit(propUpdated);
              })
            });
          }
        } else {
          // aucunes prestations
          this.apiService.findOne({where: {id: this.proposition.id}}).subscribe((propUpdated:IntraEbuzContratProspect) => {
            this._toastr.success('Succès !', 'Modification de la proposition');
            this.clickeventUpdateProposition.emit(propUpdated);
          })
        }

      }else{
        this._toastr.error(res.message, 'Edition du contrat prospect');
      }
    });

  }

  ngOnInit(): void {

    this.etatCommercialApiService.find({order: 'order ASC'}).subscribe((etats:IntraEbuzContratProspectCommercialEtat[]) => {

      let col = this.columnsForm.find((el) => {
        return el.key == 'etat_id';
      });
      if(col){col.selectOptions.options = etats};

    });


    if(this.proposition){

      /** Set Values **/
      this.columnsForm.forEach((el) => {

        if(el.type != 'separator' && el.exclude != true && (this.proposition[el.key] || this.proposition[el.key] === 0)){
          if(el.type =="date"){

            if(this.proposition[el.key]) {
              let date = moment(this.proposition[el.key], 'YYYY-MM-DD').toObject();
              this.form.controls[el.key].setValue({year:date.years, month:date.months+1, day: date.date});
            }

          }else if(el.type =="select") {
            this.form.controls[el.key].setValue(this.proposition[el.key]+"");
          }else{
            this.form.controls[el.key].setValue(this.proposition[el.key]);
          }
        } else {
          if(el.type =="date"){
            // Validation !!
            this.form.controls[el.key].setValue(null);
          }
        }
      })

      this.form.controls['libelle'].setValue(this.proposition.libelle);

      this.form.controls['operation_id'].setValue(this.proposition.operation_id);

      this.form.controls['description'].setValue(this.proposition.description);
      this.form.controls['etat_id'].setValue(this.proposition.etat_id);

      let date = moment(this.proposition.date, 'YYYY-MM-DD').toObject();
      this.form.controls['date'].setValue({year:date.years, month:date.months+1, day: date.date});

      this.form.controls['etat'].setValue(this.proposition.etat);

      this.form.controls['lignes'].setValue(this.proposition.prestations);
      let colLignes = this.columnsForm.find((el) => {
        return el.key == 'lignes';
      });
      if(colLignes){
        colLignes.selectOptions.options = [];
        colLignes.selectOptions.options = this.proposition.prestations
      }

      this.columnsForm.find((e) => e.key === 'operation_id').extradatas = this.proposition['op'];

      this.columnsForm.find((e) => e.key === 'responsablecompta_id').extradatas = this.proposition.comptable;
      this.columnsForm.find((e) => e.key === 'responsablecompta2_id').extradatas = this.proposition.comptable2;
      this.columnsForm.find((e) => e.key === 'responsablecompta3_id').extradatas = this.proposition.comptable3;
      this.columnsForm.find((e) => e.key === 'responsablecompta4_id').extradatas = this.proposition.comptable4;
      this.columnsForm.find((e) => e.key === 'responsablecompta5_id').extradatas = this.proposition.comptable5;

      this.form.controls['ICCsigne'].setValue(this.proposition.ICCsigne);
      this.form.controls['responsablecompta_id'].setValue(this.proposition.responsablecompta_id);
      this.form.controls['responsablecompta2_id'].setValue(this.proposition.responsablecompta2_id);
      this.form.controls['responsablecompta3_id'].setValue(this.proposition.responsablecompta3_id);
      this.form.controls['responsablecompta4_id'].setValue(this.proposition.responsablecompta4_id);
      this.form.controls['responsablecompta5_id'].setValue(this.proposition.responsablecompta5_id);
    }
  }
}
