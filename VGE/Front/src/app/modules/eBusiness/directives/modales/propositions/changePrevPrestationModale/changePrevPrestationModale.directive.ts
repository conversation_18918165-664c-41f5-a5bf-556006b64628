import {Component, EventEmitter, Input, OnInit, Output, SimpleChanges, ViewChild} from "@angular/core";
import {
  IntraEbuzContratProspect, IntraEbuzContratProspectCommercialEtat, IntraEbuzContratProspectPrestation
} from "../../../../../../shared/sdk/models";
import {TableColumnInterface} from "../../../../../shared/interfaces/table.column.interface";
import {FormBuilder, FormGroup, Validators} from "@angular/forms";
import {NgbActiveModal, NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {AppCacheService} from "../../../../../../common/services/app.cache.service";
import {ToastrService} from "ngx-toastr";
import {
  IntraEbuzContratProspectApi,
  IntraEbuzContratProspectCommercialEtatApi,
  IntraEbuzContratProspectPrestationApi
} from "../../../../../../shared/sdk/services/custom";
import {ROLES} from "../../../../../../datas/roles.helpers";
import {AppSessionService} from "../../../../../../common/services/app.session.service";
import {PeopleWithrightsInterface} from "../../../../../../common/interfaces/peopleWithrights.interface";
import moment from 'moment';
import {forkJoin, of} from "rxjs";

@Component({
  selector: 'ChangePrevPrestationModaleDirective',
  templateUrl: './changePrevPrestationModale.directive.html',
})
export class ChangePrevPrestationModaleDirective implements OnInit{

  @Input() prestation: IntraEbuzContratProspectPrestation;
  @Output() clickeventUpdateProposition = new EventEmitter<IntraEbuzContratProspect>();

  public columnsForm: Array<TableColumnInterface>;

  public folder = 'prestation'; // For Uploads !
  public form: FormGroup;

  public user: PeopleWithrightsInterface;
  public ROLES = ROLES;

  constructor(
    public activeModal: NgbActiveModal,
    public modalService: NgbModal,
    public _sessionService: AppSessionService,
    public _appCacheService: AppCacheService,
    public formBuilder: FormBuilder,
    public _toastr: ToastrService,
    public apiService: IntraEbuzContratProspectPrestationApi,
    public apiProspectService: IntraEbuzContratProspectApi,
    public etatCommercialApiService: IntraEbuzContratProspectCommercialEtatApi,
  ) {

    this._sessionService.logguedAccountEmitter.subscribe((people) => {
      this.user = people;
    });

    this._sessionService.logguedAccountEmitter.subscribe((people) => {
      this.user = people;
    });

    this.form = this.formBuilder.group({
      lignes: [[], []]
    });

    this.columnsForm =
      [
        {type:"separator", name:"Prestations", icon:"fa-duotone fa-cubes", exclude:true},
        {key: "lignes", name: "Lignes", type: "lignePrestationProspect", selectOptions:{options:[]}, extradatas:{hideBtnAddLignes:true}, exclude:true, readonly: false},
      ];
  }



  save(datas:object){
    if(datas['lignes']){
      const err : string[] = []
      datas['lignes'].forEach((ligne: IntraEbuzContratProspectPrestation, index: number) => {
        if((!ligne.prestation_id || !ligne.montant || !ligne.recurenceFacture || !ligne.typeMontant) && ligne.typeMontant !== 'pourcent'){
          err.push('Une prestation n\'a pas été complétée, elle n\'est pas valide, veuillez compléter')
        }
      })

      if(err.length > 0){
        this._toastr.warning(err.join('<br/>'),'Enregistrement des prestations', {enableHtml: true})
        return;
      }
    }
    if(datas && datas['lignes'] && datas['lignes'].length > 0){

      const aModifier: IntraEbuzContratProspectPrestation[] = [];

      datas['lignes'].forEach((ligne: IntraEbuzContratProspectPrestation) => {
        ligne.date = moment().format('YYYY-MM-DD');
        delete ligne.prestation
        delete ligne.contratProspect;
        if(ligne.id){
          aModifier.push(ligne)
        }
      })
      const forkSubscribes = [];
      if(aModifier.length > 0){
        aModifier.forEach((aMod: IntraEbuzContratProspectPrestation) => {
          forkSubscribes.push(this.apiService.updateAttributes(aMod.id, aMod));
        })
      }

      if(forkSubscribes.length > 0) {
        forkJoin(forkSubscribes).subscribe((r) => {
          this.apiProspectService.findOne({where: {id: this.prestation.contrat_id}}).subscribe((propUpdated:IntraEbuzContratProspect) => {
            this._toastr.success('Succès !', 'Modification de la proposition');
            this.clickeventUpdateProposition.emit(propUpdated);
          })
        });
      }
    }
  }

  ngOnInit(): void {

    this.etatCommercialApiService.find({order: 'order ASC'}).subscribe((etats:IntraEbuzContratProspectCommercialEtat[]) => {
      let col = this.columnsForm.find((el) => {
        return el.key == 'etat_id';
      });
      if(col){col.selectOptions.options = etats};
    });

    if(this.prestation) {
      this.form.controls['lignes'].setValue([this.prestation]);
      let colLignes = this.columnsForm.find((el) => {
        return el.key == 'lignes';
      });
      if (colLignes) {
        colLignes.selectOptions.options = [];
        colLignes.selectOptions.options = [this.prestation]
      }
    }

  }
}
