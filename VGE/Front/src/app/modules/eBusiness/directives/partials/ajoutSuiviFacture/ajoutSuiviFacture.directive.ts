import {Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges} from "@angular/core";
import {Form<PERSON>uilder, FormGroup, Validators} from "@angular/forms";
import {ToastrService} from "ngx-toastr";
import moment from 'moment';
import {AppSessionService} from "../../../../../common/services/app.session.service";
import {AppCacheService} from "../../../../../common/services/app.cache.service";
import {
  IntraEbuzFactureApi,
  IntraEbuzFactureHistoriqueApi,
  IntraEbuzFilesFacture,
  IntraEbuzOperationApi,
  LoopBackAuth,
  PeopleApi
} from "../../../../../shared/sdk";
import {
  IntraEbuzFacture, IntraEbuzFactureHistorique,
  People
} from "../../../../../shared/sdk";
import {ROLES} from "../../../../../datas/roles.helpers";
import {TableColumnInterface} from "../../../../shared/interfaces/table.column.interface";
import {PeopleWithrightsInterface} from "../../../../../common/interfaces/peopleWithrights.interface";
import {apiParameterInterface} from "../../../../shared/interfaces/apiParameter.interface";
import {AppGlobalEventManagerService} from "../../../../../common/services/app.globalEventManager.service";

@Component({
  selector: 'ajoutSuiviFacture',
  templateUrl: './ajoutSuiviFacture.directive.html',
})
export class AjoutSuiviFactureDirective implements OnInit, OnChanges{

  @Input() facture: IntraEbuzFacture;
  @Input() type: 'Envoi'|'Relance' = 'Relance';
  @Input() files: IntraEbuzFilesFacture[] = [];
  @Output() clickeventAddSuiviFacture = new EventEmitter<IntraEbuzFactureHistorique>();

  public columnsForm: Array<TableColumnInterface>;
  public columnsFormEmail: Array<TableColumnInterface>;

  public allsocietes: Array<{key: string, text: string}> = [];

  public folder = 'factureHistorique'; // For Uploads !
  public formEmail: FormGroup;

  public admins = [];

  public responsables = [];

  public apiParameters:apiParameterInterface;

  public user: PeopleWithrightsInterface;
  public ROLES = ROLES;

  constructor(
    public _sessionService: AppSessionService,
    public _appCacheService: AppCacheService,
    public formBuilder: FormBuilder,
    public _toastr: ToastrService,
    public factureApiService: IntraEbuzFactureApi,
    public globalEventManagerService :AppGlobalEventManagerService,
    public historiqueApiService: IntraEbuzFactureHistoriqueApi,
    public _peopleApi: PeopleApi,
    public authService: LoopBackAuth,
    public operationApi: IntraEbuzOperationApi
  ) {

    // this._sessionService.logguedAccountEmitter.subscribe((people) => {
    //   this.user = people;
    // });

    this.globalEventManagerService.getApiParameters().subscribe((parameters:apiParameterInterface) => {
      this.apiParameters = parameters;

      this.user = this.authService.getCurrentUserData();


      this.formEmail = this.formBuilder.group({
        destinataires:[null, [Validators.required]],
        destinatairescc: [null, []],
        destinatairescopie: [null, []],
        admins:[null, []],
        expediteurEmail:[null, [Validators.required]],
        expediteurNom:[null, []],
        subject:[null, [Validators.required]],
        libelle: ['', [Validators.required]],
        emailbody:['', [Validators.required]],
        signature: ['', []],

        files:['', []],
        file:['', []],
        file2:['', []],

        file3:['', []],
        file4:['', []],
        file5:['', []],
        file6:['', []],


        rappelAuto: [1, []],
        nextRappelAuto: ['', [Validators.required]],

      });


      const signatures = []
      if(this.user.signature){
        signatures.push({key:'signature', text:'Signature générale', view: this.user.signature})
      }
      if(this.user.signatureVGE){
        signatures.push({key:'signatureVGE', text:'Signature VGE', view: this.user.signatureVGE})
      }
      if(this.user.signatureVGI){
        signatures.push({key:'signatureVGI', text:'Signature VGI', view: this.user.signatureVGI})
      }
      if(this.user.signatureVGS){
        signatures.push({key:'signatureVGS', text:'Signature VGS', view: this.user.signatureVGS})
      }

      this.columnsFormEmail =
        [
          {type:"separator", name: (this.type) + " historisée", icon:"fa-duotone fa-enveloppe", exclude:true},
          {key: "destinataires",name: "Destinataires", type: "select", selectOptions:{multiple:true, labelBind:'text', keyBind:'key', selectableGroupAsModel: true, selectableGroup: true, groupBy:'societe', options:[]}},
          {key: "destinatairescc",name: "Destinataires CC", type: "select", selectOptions:{multiple:true, labelBind:'text', keyBind:'key', selectableGroupAsModel: true, selectableGroup: true, groupBy:'societe', options:[]}},
          {key: "destinatairescopie",name: "Destinataires en copie cachée", type: "select", selectOptions:{multiple:true, labelBind:'text', keyBind:'key', selectableGroupAsModel: true, selectableGroup: true, groupBy:'societe', options:[]}},
          {key: "admins",name: "Admin(s) en copie", type: "select", selectOptions:{multiple:true, labelBind:'text', keyBind:'key', selectableGroupAsModel: true, selectableGroup: true, groupBy:'societe', options:[]}},
          {key: "expediteurEmail",name: "Email Expediteur", type: "input", typeInput:'email'},
          {key: "expediteurNom",name: "Nom expediteur", type: "input"},
          {key: "subject",name: "Objet du mail", type: "input", explain:""},
          {key: "libelle",name: "Historique "+(this.type == 'Relance' ? 'd\'une relance' : 'd\'un envoi')+" de facture", type: "text"},
          {key: "emailbody",name: "Contenu du mail", type: "text"},

          {key: "files",name: "Pièces jointes au mail", type: "files"},
          {key: "file",name: "Fichier lié facture",  type: "select", selectOptions:{multiple:false, labelBind:'text', keyBind:'key', selectableGroupAsModel: true, selectableGroup: true, groupBy:'type', options:[]}},
          {key: "file2",name: "Fichiers 2 lié facture",  type: "select", selectOptions:{multiple:false, labelBind:'text', keyBind:'key', selectableGroupAsModel: true, selectableGroup: true, groupBy:'type', options:[]}},
          {key: "file3",name: "Fichiers 3 lié facture",  type: "select", selectOptions:{multiple:false, labelBind:'text', keyBind:'key', selectableGroupAsModel: true, selectableGroup: true, groupBy:'type', options:[]}},
          {key: "file4",name: "Fichiers 4 lié facture",  type: "select", selectOptions:{multiple:false, labelBind:'text', keyBind:'key', selectableGroupAsModel: true, selectableGroup: true, groupBy:'type', options:[]}},
          {key: "file5",name: "Fichiers 5 lié facture",  type: "select", selectOptions:{multiple:false, labelBind:'text', keyBind:'key', selectableGroupAsModel: true, selectableGroup: true, groupBy:'type', options:[]}},
          {key: "file6",name: "Fichiers 6 lié facture",  type: "select", selectOptions:{multiple:false, labelBind:'text', keyBind:'key', selectableGroupAsModel: true, selectableGroup: true, groupBy:'type', options:[]}},

          {key: "signature",name: "Signature du mail", type: "select", selectOptions:{multiple:false, labelBind:'text', keyBind:'key', options:signatures}},
          {type:"separator", name:"Rappel auto, activation du système de rappel automatique sur cette facture", icon:"fa-duotone fa-bell", exclude:true},
          {key: "rappelAuto",name: "Activer / désactiver le rappel automatique", type: "boolean", required:true, explain:'Sous réserve d\'avoir une actualisation avec envoi de mail (comprenant des destinataires)' },
          {key: "nextRappelAuto",name: "Date du prochain rappel auto", type: "date", required:true, explain:'Par défaut toutes les mois.' },
        ];

    });


  }

  saveEmail(datas){
    let self = this;

    if(datas['rappelAuto']){
      const nextRappelAuto = moment({years: datas['nextRappelAuto'].year, months: datas['nextRappelAuto'].month -1, days: datas['nextRappelAuto'].day});
      datas['nextRappelAuto'] = nextRappelAuto.format('YYYY-MM-DD');
    } else {
      datas['rappelAuto'] = 0;
      delete datas['nextRappelAuto'];
    }

    const tos = [];
    datas['destinataires'].forEach((responsableId) => {
      let responsable = self.responsables.find((responsableTo) => {return responsableTo.id == responsableId});
      if(responsable){
        tos.push(responsable.email)
      }
    });

    let destCC = [];
    if(datas['destinatairescc']){
      destCC = datas['destinatairescc'];
    }
    if(datas['admins']){
      destCC = destCC.concat(...datas['admins'])
    }

    let histoDatas = {
      type: 'complexEmail',
      destinataires: datas['destinataires'],
      destinatairescc: datas['destinatairescc'],
      destinatairescopie: datas['destinatairescopie'],
      admins: datas['admins'],
      expediteurEmail: datas['expediteurEmail'],
      expediteurNom: datas['expediteurNom'],
      subject: datas['subject'],
      libelle: datas['libelle'],
      emailbody: datas['emailbody'],
      signature: datas['signature'],
      date: moment().utc(true).toISOString(),
      facture_id: this.facture.id,
      factureId: this.facture.id,
      typeEnvoi: this.type,
      files:datas['files'],

      file:datas['file'],
      file2:datas['file2'],
      file3:datas['file3'],
      file4:datas['file4'],
      file5:datas['file5'],
      file6:datas['file6'],

      dest: JSON.stringify({
        to: tos.join(', '),
        destinataires: datas['destinataires'],
        destinatairescc: destCC,
        destinatairescopie: datas['destinatairescopie'],
        expediteurEmail: datas['expediteurEmail'],
        expediteurNom: datas['expediteurNom'],
      }),
      rappelAuto: !!(datas['rappelAuto']),
      nextRappelAuto: datas['nextRappelAuto'],
    }

    this.historiqueApiService.create(histoDatas).subscribe((histo:any) => {

        this._toastr.success('Succès !', 'Ajout '+(this.type == 'Relance' ? 'd\'une relance' : 'd\'un envoi')+' sous forme de mail');

        // const d = moment().add(1,'month').toObject();
        // this.formEmail.controls['nextRappelAuto'].setValue({year:d.years, month:d.months+1, day: d.date});
        //
        // this.formEmail.controls['expediteurEmail'].setValue(this.user.email);
        // this.formEmail.controls['expediteurNom'].setValue(this.user.firstname+' '+this.user.lastname+((this.user.societes && this.user.societes.length > 0) ? (' | ' + this.user.societes[0].nom) : ''));
        //
        // this.formEmail.controls['signature'].setValue(this.user.signature);


        this.loadInfos();
        this.clickeventAddSuiviFacture.emit(histo);

        this.formEmail.reset();
        Object.keys(this.formEmail.controls).forEach(key => {
          this.formEmail.get(key).setErrors(null) ;
        });

    }, error => {this._toastr.error(error, 'Ajout d\'un suivi sous forme de mail');});

  }

  majHTMLbody(){
    const HTML = `Bonjour,<br/><br/>
      Nous vous prions de trouver en annexe, la version électronique de :<br/>
      <ul>
          <li><strong>Notre facture d’honoraires référencée en objet concernant l'opération visée supra.</strong></li>
      </ul>
      Dont nous laissons le règlement à vos bons soins, dans les meilleurs délais possibles.<br><br>

      <strong>Nous vous prions d’indiquer le numéro de notre facture lors de la réalisation du virement bancaire.</strong><br/><br/>

      Nous privilégions la transmission des factures par voie dématérialisée et ne doublerons pas d’un envoi postal (sauf contre-indication de votre part).<br><br>
      Nous vous en souhaitons bonne réception et vous remercions par avance de votre diligence afin de donner les instructions de règlement dans les meilleurs délais possibles.<br><br>
      Bien cordialement,
    `;
    this.formEmail.controls['emailbody'].setValue(HTML)
  }

  majSubject(facture){
    this.facture = facture;
    if(this.facture.factureContrat === 0){
      this.formEmail.controls['subject'].setValue('Cabinet VGE - eBusiness - '+this.type+ ' facture ' + ((this.facture.numreal) ? this.facture.numreal : ('PR-'+this.facture.num) ) + ' ' + this.facture.operation.libelle + ' - ' + this.facture.client.nom + ' - Pour règlement');
    } else {
      this.formEmail.controls['subject'].setValue('Cabinet VGE - eBusiness - '+this.type+ ' facture ' + ((this.facture.numreal) ? this.facture.numreal : ('PR-'+this.facture.num) ) + ' - ' + this.facture.contrat.libelle + ' - ' + this.facture.client.nom + ' - Pour règlement');
    }

  }

  ngOnInit(): void {
    let self = this;
    this.loadInfos()
  }

  ngOnChanges(changes: SimpleChanges): void {
    if(changes.files && changes.files.currentValue){
      this.loadFilesOptions()
    }

  }

  loadFilesOptions(){

    // FILES !!!
    const filesOptions = [];

    if(this.files && this.files.length > 0){

      for(let file of this.files){
        filesOptions.push({
          text: file.file.replace(this.apiParameters.VGE.PATHS.file.fullpath, '').replace('factures/', ''),
          key: file.file.replace(this.apiParameters.VGE.PATHS.file.fullpath, ''),
          type: 'Fichiers joints facture'
        })
      }

      let colf1 = this.columnsFormEmail.find((el) => {return el.key == 'file';});
      let colf2 = this.columnsFormEmail.find((el) => {return el.key == 'file2';});
      let colf3 = this.columnsFormEmail.find((el) => {return el.key == 'file3';});
      let colf4 = this.columnsFormEmail.find((el) => {return el.key == 'file4';});
      let colf5 = this.columnsFormEmail.find((el) => {return el.key == 'file5';});
      let colf6 = this.columnsFormEmail.find((el) => {return el.key == 'file6';});

      if(colf1){colf1.selectOptions.options = filesOptions}
      if(colf2){colf2.selectOptions.options = filesOptions}
      if(colf3){colf3.selectOptions.options = filesOptions}
      if(colf4){colf4.selectOptions.options = filesOptions}
      if(colf5){colf5.selectOptions.options = filesOptions}
      if(colf6){colf6.selectOptions.options = filesOptions}

    }
  }


  loadInfos() {

    const facture = this.facture;

    if (facture) {

      // const colDest = this.columnsFormEmail.find((e) => e.key === 'destinataires')
      // if(colDest){
      //   colDest.explain = facture.operation.contrat.comptable.firstname+' '+facture.operation.contrat.comptable.lastname+((facture.operation.contrat.comptable.societes && facture.operation.contrat.comptable.societes.length > 0) ? (' | ' + facture.operation.contrat.comptable.societes[0].nom) : '')
      // }

      const signatures = []
      if(this.user.signature){
        signatures.push({key:'signature', text:'Signature générale', view: this.user.signature})
      }
      if(this.user.signatureVGE){
        signatures.push({key:'signatureVGE', text:'Signature VGE', view: this.user.signatureVGE})
      }
      if(this.user.signatureVGI){
        signatures.push({key:'signatureVGI', text:'Signature VGI', view: this.user.signatureVGI})
      }
      if(this.user.signatureVGS){
        signatures.push({key:'signatureVGS', text:'Signature VGS', view: this.user.signatureVGS})
      }

      this._peopleApi.find({where: {and:[{profilid: 2}, {accessEbuiz: 1}]}}).subscribe((admins) => {

        const optAdmin = [];

        if(admins){
          admins.forEach((admin: People) => {
            let nom = ((admin && admin.societes && admin.societes.length > 0) ? admin.societes[0].nom : '') + ' (' + admin.firstname + ' ' + admin.lastname + ')';
            optAdmin.push({
              key: admin.id,
              societe: ((admin.societes && admin.societes.length > 0) ? admin.societes[0].nom : ''),
              text: nom
            })
          })
        }

        // console.log(facture)


        if(facture.factureContrat === 0){

          const resps = [];
          if(facture.contrat){
            if(facture.contrat.comptable){
              const c1: People = facture.contrat.comptable;
              let nom = ((c1 && c1.societes && c1.societes.length > 0) ? c1.societes[0].nom : '') + ' (' + c1.firstname + ' ' + c1.lastname + ')';
              resps.push({
                key: c1.id,
                societe: ((c1.societes && c1.societes.length > 0) ? c1.societes[0].nom : ''),
                text: nom
              })
            }

            if(facture.contrat.comptable2){
              const c1: People = facture.contrat.comptable2;
              let nom = ((c1 && c1.societes && c1.societes.length > 0) ? c1.societes[0].nom : '') + ' (' + c1.firstname + ' ' + c1.lastname + ')';
              resps.push({
                key: c1.id,
                societe: ((c1.societes && c1.societes.length > 0) ? c1.societes[0].nom : ''),
                text: nom
              })
            }

            if(facture.contrat.comptable3){
              const c1: People = facture.contrat.comptable3;
              let nom = ((c1 && c1.societes && c1.societes.length > 0) ? c1.societes[0].nom : '') + ' (' + c1.firstname + ' ' + c1.lastname + ')';
              resps.push({
                key: c1.id,
                societe: ((c1.societes && c1.societes.length > 0) ? c1.societes[0].nom : ''),
                text: nom
              })
            }

            if(facture.contrat.comptable4){
              const c1: People = facture.contrat.comptable4;
              let nom = ((c1 && c1.societes && c1.societes.length > 0) ? c1.societes[0].nom : '') + ' (' + c1.firstname + ' ' + c1.lastname + ')';
              resps.push({
                key: c1.id,
                societe: ((c1.societes && c1.societes.length > 0) ? c1.societes[0].nom : ''),
                text: nom
              })
            }

            if(facture.contrat.comptable5){
              const c1: People = facture.contrat.comptable5;
              let nom = ((c1 && c1.societes && c1.societes.length > 0) ? c1.societes[0].nom : '') + ' (' + c1.firstname + ' ' + c1.lastname + ')';
              resps.push({
                key: c1.id,
                societe: ((c1.societes && c1.societes.length > 0) ? c1.societes[0].nom : ''),
                text: nom
              })
            }
          }

          const operation = this.facture.operation;

          this._appCacheService.getListResponsablesRelationsOperationEbusiness(operation.id).subscribe((responsablesRelations) => {
            if(responsablesRelations && responsablesRelations.length > 0){
              responsablesRelations.forEach(el => {
                if(el && el.people && el.people.active) {
                  let nom = ((el.people && el.people.societes && el.people.societes.length > 0) ? el.people.societes[0].nom : '') + ' (' + el.people.firstname + ' ' + el.people.lastname + ((el.role && el.role.nom) ? ' [' + el.role.nom + ']' : '') + ')';
                  resps.push({
                    key: el.people.id,
                    societe: ((el.people.societes && el.people.societes.length > 0) ? el.people.societes[0].nom : ''),
                    text: nom
                  });
                }
              });
            }

            this.responsables = resps;

            this.columnsFormEmail =
              [
                {type:"separator", name: (this.type == 'Relance' ? 'Relance historisée' : 'Envoi historisé') , icon:"fa-duotone fa-enveloppe", exclude:true},

                {key: "destinataires",name: "Destinataires", type: "select", selectOptions:{multiple:true, labelBind:'text', keyBind:'key', selectableGroupAsModel: true, selectableGroup: true, groupBy:'societe', options:resps}},
                {key: "destinatairescc",name: "Destinataires CC", type: "select", selectOptions:{multiple:true, labelBind:'text', keyBind:'key', selectableGroupAsModel: true, selectableGroup: true, groupBy:'societe', options:resps}},
                {key: "destinatairescopie",name: "Destinataires en copie cachée", type: "select", selectOptions:{multiple:true, labelBind:'text', keyBind:'key', selectableGroupAsModel: true, selectableGroup: true, groupBy:'societe', options:resps}},
                {key: "admins",name: "Admin(s) en copie", type: "select", selectOptions:{multiple:true, labelBind:'text', keyBind:'key', selectableGroupAsModel: true, selectableGroup: true, groupBy:'societe', options:optAdmin}},
                {key: "expediteurEmail",name: "Email Expediteur", type: "input", typeInput:'email'},
                {key: "expediteurNom",name: "Nom expediteur", type: "input"},
                {key: "subject",name: "Objet du mail", type: "input", explain:""},
                {key: "libelle",name: "Historique "+(this.type == 'Relance' ? 'd\'une relance' : 'd\'un envoi')+" de facture", type: "text"},
                {key: "emailbody",name: "Contenu du mail", type: "text"},

                {key: "files",name: "Pièces jointes au mail", type: "files"},
                {key: "file",name: "Fichier lié facture",  type: "select", selectOptions:{multiple:false, labelBind:'text', keyBind:'key', selectableGroupAsModel: true, selectableGroup: true, groupBy:'type', options:[]}},
                {key: "file2",name: "Fichiers 2 lié facture",  type: "select", selectOptions:{multiple:false, labelBind:'text', keyBind:'key', selectableGroupAsModel: true, selectableGroup: true, groupBy:'type', options:[]}},

                {key: "file3",name: "Fichiers 3 lié facture",  type: "select", selectOptions:{multiple:false, labelBind:'text', keyBind:'key', selectableGroupAsModel: true, selectableGroup: true, groupBy:'type', options:[]}},
                {key: "file4",name: "Fichiers 4 lié facture",  type: "select", selectOptions:{multiple:false, labelBind:'text', keyBind:'key', selectableGroupAsModel: true, selectableGroup: true, groupBy:'type', options:[]}},
                {key: "file5",name: "Fichiers 5 lié facture",  type: "select", selectOptions:{multiple:false, labelBind:'text', keyBind:'key', selectableGroupAsModel: true, selectableGroup: true, groupBy:'type', options:[]}},
                {key: "file6",name: "Fichiers 6 lié facture",  type: "select", selectOptions:{multiple:false, labelBind:'text', keyBind:'key', selectableGroupAsModel: true, selectableGroup: true, groupBy:'type', options:[]}},


                {key: "signature",name: "Signature du mail", type: "select", selectOptions:{multiple:false, labelBind:'text', keyBind:'key', options:signatures}},
                {type:"separator", name:"Rappel auto, activation du système de rappel automatique sur cette facture", icon:"fa-duotone fa-bell", exclude:true},
                {key: "rappelAuto",name: "Activer / désactiver le rappel automatique", type: "boolean", required:true, explain:'Sous réserve d\'avoir une actualisation avec envoi de mail (comprenant des destinataires)' },
                {key: "nextRappelAuto",name: "Date du prochain rappel auto", type: "date", required:true, explain:'Par défaut toutes les mois.' },
              ];

            /* Set Values ! */

            this.loadFilesOptions();

            const d = moment().add(1,'month').toObject();
            this.formEmail.controls['nextRappelAuto'].setValue({year:d.years, month:d.months+1, day: d.date});


            this.formEmail.controls['expediteurEmail'].setValue('<EMAIL>'); /*this.user.email*/
            this.formEmail.controls['expediteurNom'].setValue(this.user.firstname+' '+this.user.lastname+((this.user.societes && this.user.societes.length > 0) ? (' | ' + this.user.societes[0].nom) : ''));

            this.formEmail.controls['signature'].setValue('signature');
            // this.formEmail.controls['signature'].setValue(this.user.signature);

            this.majSubject(this.facture);
            this.majHTMLbody();

          });

        } else {

          const resps = [];

          if(facture.contrat){
            if(facture.contrat.comptable){
              const c1: People = facture.contrat.comptable;
              let nom = ((c1 && c1.societes && c1.societes.length > 0) ? c1.societes[0].nom : '') + ' (' + c1.firstname + ' ' + c1.lastname + ')';
              resps.push({
                key: c1.id,
                societe: ((c1.societes && c1.societes.length > 0) ? c1.societes[0].nom : ''),
                text: nom
              })
            }

            if(facture.contrat.comptable2){
              const c1: People = facture.contrat.comptable2;
              let nom = ((c1 && c1.societes && c1.societes.length > 0) ? c1.societes[0].nom : '') + ' (' + c1.firstname + ' ' + c1.lastname + ')';
              resps.push({
                key: c1.id,
                societe: ((c1.societes && c1.societes.length > 0) ? c1.societes[0].nom : ''),
                text: nom
              })
            }

            if(facture.contrat.comptable3){
              const c1: People = facture.contrat.comptable3;
              let nom = ((c1 && c1.societes && c1.societes.length > 0) ? c1.societes[0].nom : '') + ' (' + c1.firstname + ' ' + c1.lastname + ')';
              resps.push({
                key: c1.id,
                societe: ((c1.societes && c1.societes.length > 0) ? c1.societes[0].nom : ''),
                text: nom
              })
            }

            if(facture.contrat.comptable4){
              const c1: People = facture.contrat.comptable4;
              let nom = ((c1 && c1.societes && c1.societes.length > 0) ? c1.societes[0].nom : '') + ' (' + c1.firstname + ' ' + c1.lastname + ')';
              resps.push({
                key: c1.id,
                societe: ((c1.societes && c1.societes.length > 0) ? c1.societes[0].nom : ''),
                text: nom
              })
            }

            if(facture.contrat.comptable5){
              const c1: People = facture.contrat.comptable5;
              let nom = ((c1 && c1.societes && c1.societes.length > 0) ? c1.societes[0].nom : '') + ' (' + c1.firstname + ' ' + c1.lastname + ')';
              resps.push({
                key: c1.id,
                societe: ((c1.societes && c1.societes.length > 0) ? c1.societes[0].nom : ''),
                text: nom
              })
            }
          }

          this.columnsFormEmail =
            [
              {type:"separator", name: (this.type == 'Relance' ? 'Relance historisée' : 'Envoi historisé') , icon:"fa-duotone fa-enveloppe", exclude:true},

              {key: "destinataires",name: "Destinataires", type: "select", selectOptions:{multiple:true, labelBind:'text', keyBind:'key', selectableGroupAsModel: true, selectableGroup: true, groupBy:'societe', options:resps}},
              {key: "destinatairescc",name: "Destinataires CC", type: "select", selectOptions:{multiple:true, labelBind:'text', keyBind:'key', selectableGroupAsModel: true, selectableGroup: true, groupBy:'societe', options:resps}},
              {key: "destinatairescopie",name: "Destinataires en copie cachée", type: "select", selectOptions:{multiple:true, labelBind:'text', keyBind:'key', selectableGroupAsModel: true, selectableGroup: true, groupBy:'societe', options:resps}},
              {key: "admins",name: "Admin(s) en copie", type: "select", selectOptions:{multiple:true, labelBind:'text', keyBind:'key', selectableGroupAsModel: true, selectableGroup: true, groupBy:'societe', options:optAdmin}},
              {key: "expediteurEmail",name: "Email Expediteur", type: "input", typeInput:'email'},
              {key: "expediteurNom",name: "Nom expediteur", type: "input"},
              {key: "subject",name: "Objet du mail", type: "input", explain:""},
              {key: "libelle",name: "Historique "+(this.type == 'Relance' ? 'd\'une relance' : 'd\'un envoi')+" de facture", type: "text"},
              {key: "emailbody",name: "Contenu du mail", type: "text"},

              {key: "files",name: "Pièces jointes au mail", type: "files"},
              {key: "file",name: "Fichier lié facture",  type: "select", selectOptions:{multiple:false, labelBind:'text', keyBind:'key', selectableGroupAsModel: true, selectableGroup: true, groupBy:'type', options:[]}},
              {key: "file2",name: "Fichiers 2 lié facture",  type: "select", selectOptions:{multiple:false, labelBind:'text', keyBind:'key', selectableGroupAsModel: true, selectableGroup: true, groupBy:'type', options:[]}},

              {key: "file3",name: "Fichiers 3 lié facture",  type: "select", selectOptions:{multiple:false, labelBind:'text', keyBind:'key', selectableGroupAsModel: true, selectableGroup: true, groupBy:'type', options:[]}},
              {key: "file4",name: "Fichiers 4 lié facture",  type: "select", selectOptions:{multiple:false, labelBind:'text', keyBind:'key', selectableGroupAsModel: true, selectableGroup: true, groupBy:'type', options:[]}},
              {key: "file5",name: "Fichiers 5 lié facture",  type: "select", selectOptions:{multiple:false, labelBind:'text', keyBind:'key', selectableGroupAsModel: true, selectableGroup: true, groupBy:'type', options:[]}},
              {key: "file6",name: "Fichiers 6 lié facture",  type: "select", selectOptions:{multiple:false, labelBind:'text', keyBind:'key', selectableGroupAsModel: true, selectableGroup: true, groupBy:'type', options:[]}},

              {key: "signature",name: "Signature du mail", type: "select", selectOptions:{multiple:false, labelBind:'text', keyBind:'key', options:signatures}},
              {type:"separator", name:"Rappel auto, activation du système de rappel automatique sur cette facture", icon:"fa-duotone fa-bell", exclude:true},
              {key: "rappelAuto",name: "Activer / désactiver le rappel automatique", type: "boolean", required:true, explain:'Sous réserve d\'avoir une actualisation avec envoi de mail (comprenant des destinataires)' },
              {key: "nextRappelAuto",name: "Date du prochain rappel auto", type: "date", required:true, explain:'Par défaut toutes les mois.' },
            ];

          /* Set Values ! */

          this.loadFilesOptions();

          const d = moment().add(1,'month').toObject();
          this.formEmail.controls['nextRappelAuto'].setValue({year:d.years, month:d.months+1, day: d.date});

          // this.formEmail.controls['expediteurEmail'].setValue(this.user.email);
          this.formEmail.controls['expediteurEmail'].setValue('<EMAIL>'); /*this.user.email*/
          this.formEmail.controls['expediteurNom'].setValue(this.user.firstname+' '+this.user.lastname+((this.user.societes && this.user.societes.length > 0) ? (' | ' + this.user.societes[0].nom) : ''));

          // this.formEmail.controls['signature'].setValue(this.user.signature);
          this.formEmail.controls['signature'].setValue('signature');

          this.majSubject(this.facture);
          this.majHTMLbody();

        }

      })
    }
  }


}
