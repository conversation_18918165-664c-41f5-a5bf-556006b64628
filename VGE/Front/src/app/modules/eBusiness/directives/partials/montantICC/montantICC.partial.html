<ng-container *ngIf="!explain">
  <ng-container *ngIf="montant > 0 && (montant *  currentICC / contratICC) < montant">{{montant|currency: 'EUR':'symbol':'0.2-2':'fr-FR'}}</ng-container>
  <ng-container *ngIf="montant > 0 && (montant *  currentICC / contratICC) >= montant">{{(montant *  currentICC / contratICC)|currency: 'EUR':'symbol':'0.2-2':'fr-FR'}}</ng-container>
  <span class="text-danger" *ngIf="montant === 0 || montant === null">Prestation non définie dans le contrat</span>
</ng-container>

<ng-container *ngIf="explain">
  <ng-container *ngIf="montant > montant *  currentICC / contratICC">ICC ne s'applique pas dans le cas d'une baisse</ng-container>
  <ng-container *ngIf="montant > 0 && montant <= montant *  currentICC / contratICC">{{montant}}*{{currentICC}}/{{contratICC}}</ng-container>
  <span class="text-danger" *ngIf="montant === 0 || montant === null">Calcul impossible, prestation non définie dans le contrat</span>
</ng-container>
