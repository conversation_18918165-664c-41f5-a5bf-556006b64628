import {Component, Input} from "@angular/core";
import {IntraEbuzFacture, IntraEbuzFactureApi} from "../../../../../shared/sdk";
import moment from 'moment';
import 'moment-duration-format';
import {NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {
  ChangeAdresseFacturationModaleDirective
} from "../../modales/operation/changeAdresseFacturationModale/changeAdresseFacturationModale.directive";

@Component({
  selector: 'factureTemplate',
  templateUrl: './facture.template.html',
})
export class FactureTemplate{

  public moment = moment;

  @Input() facture: IntraEbuzFacture;

  constructor(
    public modalService: NgbModal,
    public _apiService: IntraEbuzFactureApi,
  ) {}

  defineAdresse(){
    const activeModal = this.modalService.open(ChangeAdresseFacturationModaleDirective, { size: 'lg', centered: false });
    activeModal.componentInstance.facture = this.facture;
    activeModal.componentInstance.clickeventUpdateFacture.subscribe(() => {
      this._apiService.findById(this.facture.id).subscribe((res: IntraEbuzFacture) => {
        this.facture = res;
      });
      activeModal.dismiss();
    });
  }


}
