<div class="row">
  <div class="col-6"></div>
  <div class="col-6 mb-1 mT20 text-right">
    <!--    <button class="btn btn-info btn-social btn-sm mL5" [routerLink]="['/eBusiness','factures']"><i class="fa-duotone fa-file-invoice"></i> Factures</button>-->
    <!--    <button class="btn btn-info btn-social btn-sm mL5" [routerLink]="['/eBusiness', 'depenses']"><i class="fa-duotone fa-file-invoice"></i> Dépenses</button>-->

    <exporteBuizDownload
      [ngbTooltip]="'Exporter les Prévisionnels prospects ?'"
      placement="left"
      [class]="'btn btn-blue btn-social btn-sm mL5'"
      [operationId]=""
      [typeExport]="'prevProspect'"
      [label]="'Exporter les Prévisionnels prospects'"
    ></exporteBuizDownload>

  </div>
</div>


<div echarts [options]="options" class="demo-chart"></div>

<accordion [isAnimated]="false"  [closeOthers]="true">
  <accordion-group heading="" [isOpen]="false">

    <button class="btn btn-link btn-block d-flex justify-content-between" accordion-heading type="button">
      <div class="">Détails par opération</div>
    </button>

    <div class="row">
      <div class="col-12">
        <div class="card">
          <div class="card-content">

            <div class="w-100" style="overflow: auto;height: 800px;">

              <table class="table table-bordered table-compact table-xs mB0 fixedTopRow">

                <thead>
                <tr class="sticky">
                  <th class="pl-0 pr-0 text-left">Clients / Opérations</th>
                  <th class="pl-0 pr-0 text-center text-nowrap" width="10%">Prestations</th>
                  <ng-container *ngFor="let y of prevProspects?.years">
                    <th class="pl-0 pr-0 text-center" colspan="4" style="min-width: 15%">Montants {{y}}</th>
                  </ng-container>
                </tr>
                <tr class="sticky sticky2">
                  <td>
                    <div class="w-100" style="z-index: 100">
                      <ng-select
                        class="w-100"
                        #selectRevenus
                        [items]="optionsOperations"
                        [multiple]="true"
                        [attr.closeOnSelect]="true"
                        [searchable]="true"
                        [bindLabel]="'text'"
                        [bindValue]="'key'"
                        [placeholder]="'Filter'"
                        [selectableGroup]="true"
                        [selectableGroupAsModel]="true"
                        [groupBy]="'client'"
                        (change)="filterOperation($event)"
                        (clear)="filterOperation(null)"
                      >
                      </ng-select>
                    </div>
                  </td>
                  <td style="min-width: 200px !important;"></td>
                  <ng-container *ngFor="let y of prevProspects?.years">
                    <td class="pl-0 pr-0 text-center" [placement]="'top'" [container]="'body'" [ngbTooltip]="'Estimation facturation prospect provenant des contrats prospects'">Contr. prosp.</td>
                    <td class="pl-0 pr-0 text-center" [placement]="'top'" [container]="'body'" [ngbTooltip]="'Estimation facturation prévisionnelle provenant des contrats prospects'">Contr. prosp. prévi.</td>
                    <td class="pl-0 pr-0 text-center" [placement]="'top'" [container]="'body'" [ngbTooltip]="'Factures prévisionnelles'">Prév</td>
                    <td class="pl-0 pr-0 text-center" [placement]="'top'" [container]="'body'" [ngbTooltip]="'Facturés et factures réelles'">Réel</td>
                  </ng-container>
                </tr>
                </thead>
                <tbody>


                <ng-container *ngFor="let operationId of prevProspects?.orderOperations">
                  <tr *ngIf="prevProspects?.operations[operationId]">
                    <td class="sticky">{{prevProspects?.operations[operationId]?.client}} {{prevProspects?.operations[operationId]?.libelle}}</td>
                    <td class="sticky sticky2">
                      <ul class="list-unstyled" *ngIf="prevProspects?.operations[operationId]?.categories">
                        <li *ngFor="let cat of prevProspects?.operations[operationId]?.categories">{{prestations[cat]?.libelle}}</li>
                      </ul>
                    </td>
                    <ng-container *ngFor="let y of prevProspects?.years">
                      <td class="bg-prospect">
                        <ul class="list-unstyled">
                          <ng-container *ngFor="let cat of prevProspects?.operations[operationId]?.categories">
                            <li>{{(prevProspects?.operations[operationId]?.recettesPros[y]?.categories[cat]?.ht) ? ((prevProspects?.operations[operationId]?.recettesPros[y]?.categories[cat].ht|currency: 'EUR':'symbol':'0.2-2':'fr-FR')) : '&nbsp;'}}</li>
                          </ng-container>
                        </ul>
                      </td>
                      <td class="bg-prospect-prev">
                        <ul class="list-unstyled">
                          <ng-container *ngFor="let cat of prevProspects?.operations[operationId]?.categories">
                            <li>{{(prevProspects?.operations[operationId]?.recettesProsPrevi[y]?.categories[cat]?.ht) ? ((prevProspects?.operations[operationId]?.recettesProsPrevi[y]?.categories[cat].ht|currency: 'EUR':'symbol':'0.2-2':'fr-FR')) : '&nbsp;'}}</li>
                          </ng-container>
                        </ul>
                      </td>
                      <td class="bg-prev">
                        <ul class="list-unstyled">
                          <ng-container *ngFor="let cat of prevProspects?.operations[operationId]?.categories">
                            <li>{{(prevProspects?.operations[operationId]?.recettesPrev[y]?.categories[cat]?.ht) ? ((prevProspects?.operations[operationId]?.recettesPrev[y]?.categories[cat].ht|currency: 'EUR':'symbol':'0.2-2':'fr-FR')) : '&nbsp;'}}</li>
                          </ng-container>
                        </ul>
                      </td>
                      <td class="bg-reel">
                        <ul class="list-unstyled">
                          <ng-container *ngFor="let cat of prevProspects?.operations[operationId]?.categories">
                            <li>{{(prevProspects?.operations[operationId]?.recettes[y]?.categories[cat]?.ht) ? ((prevProspects?.operations[operationId]?.recettes[y]?.categories[cat].ht|currency: 'EUR':'symbol':'0.2-2':'fr-FR')) : '&nbsp;'}}</li>
                          </ng-container>
                        </ul>
                      </td>
                    </ng-container>
                  </tr>
                </ng-container>

                </tbody>
              </table>

            </div>

          </div>
        </div>
      </div>
    </div>
  </accordion-group>
  <accordion-group heading="" [isOpen]="false">

    <button class="btn btn-link btn-block d-flex justify-content-between" accordion-heading type="button">
      <div class="">Détails par catégories</div>
    </button>

    <div class="row">
      <div class="col-12">
        <div class="card">
          <div class="card-content">

            <div class="w-100" style="overflow: auto;height: 800px;">

              <table class="table table-bordered table-compact table-xs mB0 fixedTopRow">

                <thead>
                <tr class="sticky">
                  <th class="pl-0 pr-0 text-left">Type prestation</th>
                  <ng-container *ngFor="let y of prevProspects?.years">
                    <th class="pl-0 pr-0 text-center" colspan="4" style="min-width: 15%">Montants {{y}}</th>
                  </ng-container>
                </tr>
                <tr class="sticky sticky2">
                  <td>
                  </td>
                  <ng-container *ngFor="let y of prevProspects?.years">
                    <td class="pl-0 pr-0 text-center" [placement]="'top'" [container]="'body'" [ngbTooltip]="'Estimation facturation prospect provenant des contrats prospects'">Contr. prosp.</td>
                    <td class="pl-0 pr-0 text-center" [placement]="'top'" [container]="'body'" [ngbTooltip]="'Estimation facturation prévisionnelle provenant des contrats prospects'">Contr. prosp. prévi.</td>
                    <td class="pl-0 pr-0 text-center" [placement]="'top'" [container]="'body'" [ngbTooltip]="'Factures prévisionnelles'">Prév</td>
                    <td class="pl-0 pr-0 text-center" [placement]="'top'" [container]="'body'" [ngbTooltip]="'Facturés et factures réelles'">Réel</td>
                  </ng-container>
                </tr>
                </thead>
                <tbody>
                <tr *ngFor="let cat of prevProspects?.categories">
                  <td class="sticky"><h6><strong>Total {{prestations[cat]?.libelle}}</strong></h6></td>
                  <ng-container *ngFor="let y of prevProspects?.years">
                    <td class="pl-0 pr-0 text-center bg-prospect"><h6><strong>{{(prevProspects?.totaux?.recettesPros[y] && prevProspects?.totaux?.recettesPros[y]?.categories && prevProspects?.totaux?.recettesPros[y]?.categories[cat]) ? (prevProspects?.totaux?.recettesPros[y]?.categories[cat]?.ht|currency: 'EUR':'symbol':'0.2-2':'fr-FR'): ''}}</strong></h6></td>
                    <td class="pl-0 pr-0 text-center bg-prospect-prev"><h6><strong>{{(prevProspects?.totaux?.recettesProsPrevi[y] && prevProspects?.totaux?.recettesProsPrevi[y]?.categories && prevProspects?.totaux?.recettesProsPrevi[y]?.categories[cat]) ? (prevProspects?.totaux?.recettesProsPrevi[y]?.categories[cat]?.ht|currency: 'EUR':'symbol':'0.2-2':'fr-FR'): ''}}</strong></h6></td>
                    <td class="pl-0 pr-0 text-center bg-prev"><h6><strong>{{(prevProspects?.totaux?.recettesPrev[y] && prevProspects?.totaux?.recettesPrev[y]?.categories && prevProspects?.totaux?.recettesPrev[y]?.categories[cat]) ? (prevProspects?.totaux?.recettesPrev[y]?.categories[cat]?.ht|currency: 'EUR':'symbol':'0.2-2':'fr-FR'): ''}}</strong></h6></td>
                    <td class="pl-0 pr-0 text-center bg-reel"><h6><strong>{{(prevProspects?.totaux?.recettes[y] && prevProspects?.totaux?.recettes[y]?.categories && prevProspects?.totaux?.recettes[y]?.categories[cat]) ? (prevProspects?.totaux?.recettes[y]?.categories[cat]?.ht|currency: 'EUR':'symbol':'0.2-2':'fr-FR'): ''}}</strong></h6></td>
                  </ng-container>
                </tr>

                <tr>
                  <td class="sticky"><h5><strong>Totaux</strong></h5></td>
                  <ng-container *ngFor="let y of prevProspects?.years">
                    <td class="pl-0 pr-0 text-center bg-prospect"><h5><strong>{{(prevProspects?.totaux?.recettesPros[y]) ? (prevProspects?.totaux?.recettesPros[y]?.ht|currency: 'EUR':'symbol':'0.2-2':'fr-FR') : ''}}</strong></h5></td>
                    <td class="pl-0 pr-0 text-center bg-prospect-prev"><h5><strong>{{(prevProspects?.totaux?.recettesProsPrevi[y]) ? (prevProspects?.totaux?.recettesProsPrevi[y]?.ht|currency: 'EUR':'symbol':'0.2-2':'fr-FR') : ''}}</strong></h5></td>
                    <td class="pl-0 pr-0 text-center bg-prev"><h5><strong>{{(prevProspects?.totaux?.recettesPrev[y]) ? (prevProspects?.totaux?.recettesPrev[y]?.ht|currency: 'EUR':'symbol':'0.2-2':'fr-FR') : ''}}</strong></h5></td>
                    <td class="pl-0 pr-0 text-center bg-reel"><h5><strong>{{(prevProspects?.totaux?.recettes[y]) ? (prevProspects?.totaux?.recettes[y]?.ht|currency: 'EUR':'symbol':'0.2-2':'fr-FR') : ''}}</strong></h5></td>
                  </ng-container>
                </tr>

                <tr>
                  <td class="sticky"><h5><strong>Total Général</strong></h5></td>
                  <ng-container *ngFor="let y of prevProspects?.years">
                    <td colspan="4" class="pl-0 pr-0 text-center"><h5><strong>{{( ((prevProspects?.totaux?.recettes[y]) ? prevProspects?.totaux?.recettes[y]?.ht : 0) + ((prevProspects?.totaux?.recettesPrev[y]) ? prevProspects?.totaux?.recettesPrev[y]?.ht : 0) + ((prevProspects?.totaux?.recettesPros[y]) ? prevProspects?.totaux?.recettesPros[y]?.ht : 0) + ((prevProspects?.totaux?.recettesProsPrevi[y]) ? prevProspects?.totaux?.recettesProsPrevi[y]?.ht : 0) )|currency: 'EUR':'symbol':'0.2-2':'fr-FR'}}</strong></h5></td>
                  </ng-container>
                </tr>

                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </accordion-group>

  <accordion-group heading="" [isOpen]="true">

    <button class="btn btn-link btn-block d-flex justify-content-between" accordion-heading type="button">
      <div class="">Global</div>
    </button>

    <div class="row">
      <div class="col-12">
        <div class="card">
          <div class="card-content" style="overflow: auto;max-height: 800px; min-height: 350px">

            <table class="table table-bordered table-compact table-xs mB0 fixedTopRow">

              <thead>
              <tr class="sticky">
                <th class="sticky"></th>
                <ng-container *ngFor="let y of prevProspects?.years">
                  <th class="pl-0 pr-0 text-center" colspan="5">{{y}}</th>
                </ng-container>
              </tr>
              <tr class="sticky">
                <th class="sticky"></th>
                <ng-container *ngFor="let y of prevProspects?.years">
                  <td class="pl-0 pr-0 text-center" [placement]="'top'" [container]="'body'" [ngbTooltip]="'Estimation facturation prospect provenant des contrats prospects'">Contr. prosp.</td>
                  <td class="pl-0 pr-0 text-center" [placement]="'top'" [container]="'body'" [ngbTooltip]="'Estimation facturation prévisionnelle provenant des contrats prospects'">Contr. prosp. prévi.</td>
                  <td class="pl-0 pr-0 text-center" [placement]="'top'" [container]="'body'" [ngbTooltip]="'Factures prévisionnelles'">Prév</td>
                  <td class="pl-0 pr-0 text-center" [placement]="'top'" [container]="'body'" [ngbTooltip]="'Facturés et factures réelles'">Réel</td>
                  <th class="pl-0 pr-0 text-center bg-depense" [placement]="'top'" [container]="'body'" [ngbTooltip]="'Dépenses prévisionnelles & réelles'">Dépenses</th>
                </ng-container>
              </tr>
              </thead>
              <tbody>

              <tr>
                <td class="sticky"></td>
                <ng-container *ngFor="let y of prevProspects?.years">
                  <td class="pl-0 pr-0 text-center bg-prospect" (click)="openModaleDetails(y, 'recettesPros')"><h5><strong>{{(prevProspects?.totaux?.recettesPros[y]) ? (prevProspects?.totaux?.recettesPros[y]?.ht|currency: 'EUR':'symbol':'0.2-2':'fr-FR') : ''}}</strong></h5></td>
                  <td class="pl-0 pr-0 text-center bg-prospect" (click)="openModaleDetails(y, 'recettesProsPrevi')"><h5><strong>{{(prevProspects?.totaux?.recettesProsPrevi[y]) ? (prevProspects?.totaux?.recettesProsPrevi[y]?.ht|currency: 'EUR':'symbol':'0.2-2':'fr-FR') : ''}}</strong></h5></td>
                  <td class="pl-0 pr-0 text-center bg-prev" (click)="openModaleFactures(y, 'prev')"><h5><strong>{{(prevProspects?.totaux?.recettesPrev[y]) ? (prevProspects?.totaux?.recettesPrev[y]?.ht|currency: 'EUR':'symbol':'0.2-2':'fr-FR') : ''}}</strong></h5></td>
                  <td class="pl-0 pr-0 text-center bg-reel" (click)="openModaleFactures(y, 'reel')"><h5><strong>{{(prevProspects?.totaux?.recettes[y]) ? (prevProspects?.totaux?.recettes[y]?.ht|currency: 'EUR':'symbol':'0.2-2':'fr-FR') : ''}}</strong></h5></td>
                  <td></td>
                </ng-container>
              </tr>


              <tr>
                <td class="sticky"><h5><strong>Totaux prév.</strong></h5></td>
                <ng-container *ngFor="let y of prevProspects?.years">
                  <td colspan="2" class="pl-0 pr-0 text-center"><h5><strong>{{( ((prevProspects?.totaux?.recettesPros[y]) ? prevProspects?.totaux?.recettesPros[y]?.ht : 0) + ((prevProspects?.totaux?.recettesProsPrevi[y]) ? prevProspects?.totaux?.recettesProsPrevi[y]?.ht : 0) )|currency: 'EUR':'symbol':'0.2-2':'fr-FR'}}</strong></h5></td>
                  <td colspan="2" class="pl-0 pr-0 text-center"><h5><strong>{{( ((prevProspects?.totaux?.recettes[y]) ? prevProspects?.totaux?.recettes[y]?.ht : 0) + ((prevProspects?.totaux?.recettesPrev[y]) ? prevProspects?.totaux?.recettesPrev[y]?.ht : 0) )|currency: 'EUR':'symbol':'0.2-2':'fr-FR'}}</strong></h5></td>
                  <td>
                    <strong>{{((depenses[y]?.montantHT) ? depenses[y]?.montantHT : 0 )|currency: 'EUR':'symbol':'0.2-2':'fr-FR'}} HT</strong><br/>
                    <strong *ngIf="depenses[y]?.montantTTC">({{((depenses[y]?.montantTTC) ? depenses[y]?.montantTTC : 0)|currency: 'EUR':'symbol':'0.2-2':'fr-FR'}} TTC)</strong>
                  </td>
                </ng-container>
              </tr>

              <tr>
                <td class="sticky"><h5><strong>Totaux</strong></h5></td>
                <ng-container *ngFor="let y of prevProspects?.years">
                  <td colspan="4" class="pl-0 pr-0 text-center"><h5><strong>{{( ((prevProspects?.totaux?.recettes[y]) ? prevProspects?.totaux?.recettes[y]?.ht : 0) + ((prevProspects?.totaux?.recettesPrev[y]) ? prevProspects?.totaux?.recettesPrev[y]?.ht : 0) + ((prevProspects?.totaux?.recettesPros[y]) ? prevProspects?.totaux?.recettesPros[y]?.ht : 0) + ((prevProspects?.totaux?.recettesProsPrevi[y]) ? prevProspects?.totaux?.recettesProsPrevi[y]?.ht : 0) )|currency: 'EUR':'symbol':'0.2-2':'fr-FR'}}</strong></h5></td>
                  <td>
                    <strong>{{((depenses[y]?.montantHT) ? depenses[y]?.montantHT : 0 )|currency: 'EUR':'symbol':'0.2-2':'fr-FR'}} HT</strong><br/>
                    <strong *ngIf="depenses[y]?.montantTTC">({{((depenses[y]?.montantTTC) ? depenses[y]?.montantTTC : 0)|currency: 'EUR':'symbol':'0.2-2':'fr-FR'}} TTC)</strong>
                  </td>
                </ng-container>
              </tr>

              <tr>
                <td class="sticky" style="min-width: 120px !important;"><h5><strong>Résultat Net Prévisionnel</strong></h5></td>
                <ng-container *ngFor="let y of prevProspects?.years">
                  <td colspan="5" class="pl-0 pr-0 text-center"><h5><strong>{{(( ((prevProspects?.totaux?.recettes[y]) ? prevProspects?.totaux?.recettes[y]?.ht : 0) + ((prevProspects?.totaux?.recettesPrev[y]) ? prevProspects?.totaux?.recettesPrev[y]?.ht : 0) + ((prevProspects?.totaux?.recettesPros[y]) ? prevProspects?.totaux?.recettesPros[y]?.ht : 0) + ((prevProspects?.totaux?.recettesProsPrevi[y]) ? prevProspects?.totaux?.recettesProsPrevi[y]?.ht : 0) ) - depenses[y]?.montantHT )|currency: 'EUR':'symbol':'0.2-2':'fr-FR'}}</strong></h5></td>
                </ng-container>
              </tr>

              <tr>
                <td class="sticky" style="min-width: 120px !important;"><h5><strong>Résultat Net Réel</strong></h5></td>
                <ng-container *ngFor="let y of prevProspects?.years">
                  <td colspan="5" class="pl-0 pr-0 text-center"><h5><strong>{{(( ((prevProspects?.totaux?.recettes[y]) ? prevProspects?.totaux?.recettes[y]?.ht : 0) + ((prevProspects?.totaux?.recettesPrev[y]) ? prevProspects?.totaux?.recettesPrev[y]?.ht : 0) ) - depenses[y]?.montantHT )|currency: 'EUR':'symbol':'0.2-2':'fr-FR'}}</strong></h5></td>
                </ng-container>
              </tr>

              <tr>
                <td class="sticky" style="min-width: 120px !important;"><h5><strong>Impots Sociétés</strong></h5></td>
                <ng-container *ngFor="let y of prevProspects?.years">

                  <ng-template #impots>
                    <div [innerHTML]="prevProspects?.totaux?.impots[y]?.impot?.explain"></div>
                  </ng-template>

                  <td colspan="5" class="pl-0 pr-0 text-center"><h5 [placement]="'top'" [container]="'body'" [ngbPopover]="impots" triggers="mouseenter:mouseleave" popoverTitle="Calcul impôts"><strong>{{( (prevProspects?.totaux?.impots[y]) ? prevProspects?.totaux?.impots[y]?.impot?.montant : 0 )|currency: 'EUR':'symbol':'0.2-2':'fr-FR'}}</strong></h5></td>
                </ng-container>
              </tr>

              <tr>
                <td class="sticky" style="min-width: 120px !important;"><h5><strong>Résultat Net après Impôts</strong></h5></td>
                <ng-container *ngFor="let y of prevProspects?.years">
                  <td colspan="5" class="pl-0 pr-0 text-center"><h5><strong>{{((prevProspects?.totaux?.impots[y]?.apresIS?.montant) ? prevProspects?.totaux?.impots[y]?.apresIS?.montant : 0)|currency: 'EUR':'symbol':'0.2-2':'fr-FR'}}</strong></h5></td>
                </ng-container>
              </tr>

              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

  </accordion-group>

</accordion>
