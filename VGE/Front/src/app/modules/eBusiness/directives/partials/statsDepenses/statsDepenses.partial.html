<div class="card">
  <div class="card-header">
    <h3 class="mB20 text-center">Stats Dépenses {{currentYear}}</h3>
  </div>

  <div class="card-content p-1">
    <div class="row">
      <div class="col-12 p-1 mB10">
        <div class="row">
          <div class="col-4">
            <div class="btn-group">
              <button
                class="btn btn-primary"
                (click)="changeYear(currentYear-1)"
              >
                Année précédente
              </button>
              <button
                class="btn btn-outline-secondary"
                (click)="changeYear(parseInt(moment().format('YYYY')))"
              >
                Année en cours
              </button>
              <button
                class="btn btn-primary"
                (click)="changeYear(currentYear+1)"
              >
                <PERSON><PERSON> suivante
              </button>
            </div>
          </div>

          <div class="col-8 text-center">
          </div>
        </div>
      </div>


      <div class="col-12 p-1 mB10">

        <div class="row mB10">
          <div class="col-6 text-center">
            <button class="btn btn-block" [ngClass]="{'btn-success': !viewDetails, 'btn-adn': viewDetails}" (click)="resetGraph()">Vue Globale</button>
          </div>
          <div class="col-6 text-center">
            <button class="btn btn-block" [ngClass]="{'btn-success': viewDetails, 'btn-adn': !viewDetails}" (click)="viewDetails = true">Vue Détaillée</button>
          </div>
        </div>


        <!-- Afficher un bouton de retour si on est en vue détaillée -->
        <div *ngIf="isDetailView" class="text-center mt-2">
          <button class="btn btn-sm btn-outline-secondary" (click)="resetGraph()">
            <i class="fa fa-arrow-left"></i> Retour à la vue globale des dépenses
          </button>
        </div>

        <div class="row" *ngIf="viewDetails">

          <div class="col-8">
            <div echarts [options]="options" class="demo-chart" style="height: 900px"></div>
          </div>
          <div class="col-4">
            <table class="table table-bordered table-compact table-striped table-xs mB0">
              <thead>
              <tr>
                <th><i class="fa fa-duotone fa-bars-sort fa-2x"></i> Dépense</th>
                <th>Montant HT</th>
                <th>%</th>
              </tr>
              </thead>
              <tbody>
              <ng-container *ngFor="let depense of filteredStats.depenses">
                <tr *ngIf="depense?.depenses?.total?.ht > 0">
                  <td>{{depense?.libelle}}</td>
                  <td>{{depense?.depenses?.total.ht|currency: 'EUR':'symbol':'0.2-2':'fr-FR'}}</td>
                  <td>{{(depense?.depenses?.total?.ht/total * 100).toFixed(2)}}%</td>
                </tr>
              </ng-container>
              <tr>
                <td>Total</td>
                <td>{{total|currency: 'EUR':'symbol':'0.2-2':'fr-FR'}}</td>
                <td>100%</td>
              </tr>

              </tbody>
            </table>
          </div>
        </div>


        <div class="row" *ngIf="!viewDetails">


          <div class="col-8">
            <div echarts #chart="echarts" [options]="optionsGlobal" (chartInit)="onChartInit($event)" class="demo-chart" style="height: 700px"></div>
          </div>
          <div class="col-4">
            <table class="table table-bordered table-compact table-striped table-xs mB0">
              <thead>
              <tr>
                <th><i class="fa fa-duotone fa-bars-sort fa-2x"></i> Familles</th>
                <th>Montant HT</th>
                <th>%</th>
              </tr>
              </thead>
              <tbody>
              <ng-container *ngFor="let stat of stats?.depenses">
                <tr *ngIf="stat?.depenses?.total?.ht > 0">
                  <td>{{stat?.libelle}}</td>
                  <td>{{stat?.depenses?.total?.ht|currency: 'EUR':'symbol':'0.2-2':'fr-FR'}}</td>
                  <td>{{(stat?.depenses?.total?.ht/total * 100).toFixed(2)}}%</td>
                </tr>
              </ng-container>
              <tr>
                <td>Total</td>
                <td>{{total|currency: 'EUR':'symbol':'0.2-2':'fr-FR'}}</td>
                <td>100%</td>
              </tr>

              </tbody>
            </table>
          </div>
        </div>

      </div>


    </div>
  </div>
</div>

