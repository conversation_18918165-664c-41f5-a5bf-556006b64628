<div class="row">
  <div class="col-12 mb-1 text-right">
    <button class="btn btn-danger btn-social btn-sm mL5" [routerLink]="['/eBusiness', 'depenses']"><i class="ft-corner-down-left"></i> Retour à la liste des dépenses</button>
    <button class="btn btn-success btn-social btn-sm mL5" *ngIf="(user|hasRight:'admin')" [routerLink]="['/', 'eBusiness', 'depenses-recurrentes', 'edit','new']"><i class="ft-plus"></i> Ajouter une dépense récursive</button>
  </div>
</div>


<div class="content-body">

  <selfTablePaginate #tabdepensesrecurs
       [columns]="columns"
       [serie]="serie"
       [blackListFilters]="[]"
       [defaultKeyColumn]="'libelle'"
       [data]="depensesrecurrentes$"
       list="depensesrecurs"

       [bodyTopTemplate]="bodyTop"
       [bodyBottomTemplate]="bodyBottom"

       [headerTopTemplate]="headerTop"
       [headerBottomTemplate]="headerBottom"

       [footerTopTemplate]="footerTop"
       [footerBottomTemplate]="footerBottom"

       [actionsTemplate]="actions"

  >
  </selfTablePaginate>

  <ng-template #bodyTop let-object let-index>
  </ng-template>
  <ng-template #bodyBottom let-object let-index>
    <td>{{object?.typeDepense?.libelle}}</td>
  </ng-template>

  <ng-template #headerTop let-object>
  </ng-template>
  <ng-template #headerBottom>
    <th>Type de dépense</th>
  </ng-template>

  <ng-template #actions let-object>
    <ng-container *ngIf="(user|hasRight:'admin')">
      <div class="btn-group" role="group">

        <button type="button" [ngbTooltip]="'Modifier'" class="btn btn-icon btn-sm btn-success" [routerLink]="['/eBusiness','depenses-recurrentes', 'edit', object.id]"><i class="ft-edit-3"></i></button>

        <button *ngIf="user|hasRight:'admin'"
                [ngbTooltip]="'Supprimer cette dépense récurente ?'" class="btn btn-danger btn-sm"
                mwlConfirmationPopover
                [popoverTitle]="'Suppression de cette dépense récurente ?'"
                [popoverMessage]="'Êtes vous sur de vouloir supprimer cette dépense récurente ?'"
                placement="left"
                (confirm)="delete(object)"
        ><i class="fa-duotone fa-trash"></i></button>
      </div>
    </ng-container>
  </ng-template>


  <ng-template #footerTop>
  </ng-template>
  <ng-template #footerBottom>
    <th></th>
  </ng-template>

</div>
