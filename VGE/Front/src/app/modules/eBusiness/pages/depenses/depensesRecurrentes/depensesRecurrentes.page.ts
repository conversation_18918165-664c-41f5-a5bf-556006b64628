import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>t, ViewChild} from "@angular/core";
import {ToastrService} from "ngx-toastr";
import {AppGlobalEventManagerService} from "../../../../../common/services/app.globalEventManager.service";
import {IntraEbuzDepenseRecurrente, People} from "../../../../../shared/sdk/models";
import {AppSessionService} from "../../../../../common/services/app.session.service";
import {LoopBackAuth} from "../../../../../shared/sdk/services/core";
import moment from 'moment';
import 'moment-duration-format';
import {ListPageInterface} from "../../../../shared/interfaces/list.page.interface";
import {TableColumnInterface} from "../../../../shared/interfaces/table.column.interface";
import {SelftablepaginateDirective} from "../../../../../directives/tables/selftablepaginate/selftablepaginate.directive";
import {Observable} from "rxjs";
import {PeopleWithrightsInterface} from "../../../../../common/interfaces/peopleWithrights.interface";
import {IntraEbuzDepenseRecurrenteApi} from "../../../../../shared/sdk";

@Component({
  selector: 'DepensesRecurrentesPage',
  templateUrl: './depensesRecurrentes.page.html',
})
export class DepensesRecurrentesPage implements OnInit, OnDestroy, ListPageInterface{

  public moment = moment;
  public user: PeopleWithrightsInterface;

  public serie:string = 'depensesrecurrentes.list';

  public sub;
  public loaded = false;


  public depensesRecurrentes$: Observable<IntraEbuzDepenseRecurrente[]> = new Observable<IntraEbuzDepenseRecurrente[]>();
  public columns: Array<TableColumnInterface>;


  @ViewChild('tabdepensesrecurs', {static:false}) tabdepensesrecurs : SelftablepaginateDirective
  filtre = {};

  constructor(
    public _toastr: ToastrService,
    public appGlobalEventManagerService: AppGlobalEventManagerService,
    public _depensesRecursiveService: IntraEbuzDepenseRecurrenteApi,
    public _sessionService: AppSessionService,
    public authService: LoopBackAuth,
  ) {

    this._sessionService.logguedAccountEmitter.subscribe((people) => {
      this.user = people;
    });

    this.appGlobalEventManagerService.updateTitle('Dépenses récurrentes');
    this.appGlobalEventManagerService.updateBreadcrumb([{title:'Home eBusiness', url:'/eBusiness/home'}, {title:'Dépenses récurrentes', url:''}])

    this.columns =
      [
        // N°	Date	Localisation	Lot	Intervenant	Description	A lever pour le	Etat	Date etat	Urgent	Export

        {key: 'libelle', name:"Libellé", type: 'input', order:true, filter:true},
        {key: 'date', name:"Date de début", type: 'date', order:false, filter:true},
        {key: 'datefin', name:"Date de fin", type: 'date', order:false, filter:true},
        {key: 'montantHT', name:"Montant HT", type: 'input', typeInput: 'number', order:false, filter:false},
        {key: 'montantTTC', name:"Montant TTC", type: 'input', typeInput: 'number', order:false, filter:false},
        {key: 'periodicite', name:"Periodicité", type: 'input', typeInput: 'number', order:false, filter:false},
        {key: 'datemois', name:"Date du paiement (jour du mois)", type: 'input', typeInput: 'number', order:false, filter:false},
        // {key: 'type_depense_id', name:"Type de dépense", type: 'input', order:false, filter:false},
      ];


  }

  delete(object: any) {
  }

  filter(value: string, $event) {
  }

  addFilter(column, value){

    if(value && typeof value.key != 'undefined'){
      if(value.text){
        this.tabdepensesrecurs.addFilter(column, value.key, 'equal', value.text, this.serie);
      }else if(value.value){
        this.tabdepensesrecurs.addFilter(column, value.key, 'equal', value.value, this.serie);
      }
    }else{
      this.tabdepensesrecurs.addFilter(column, value, 'equal', null, this.serie);
    }
  }

  load() {
    this.depensesRecurrentes$ = this._depensesRecursiveService.find();
  }

  order(value: string, $event, way: "ASC" | "DESC") {
  }

  ngOnInit(): void {
    this.load();
  }

  ngOnDestroy(): void {
    if(this.sub){this.sub.unsubscribe();}
  }

}
