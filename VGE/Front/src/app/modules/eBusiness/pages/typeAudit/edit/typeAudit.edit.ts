import {<PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit} from "@angular/core";
import {ToastrService} from "ngx-toastr";
import moment from 'moment';
import 'moment-duration-format';
import {Observable, of} from "rxjs";
import {ActivatedRoute, Router} from "@angular/router";
import {catchError, map, share} from "rxjs/operators";
import {FormBuilder, FormGroup, Validators} from "@angular/forms";
import {EditPageInterface} from "../../../../shared/interfaces/edit.page.interface";
import {TableColumnInterface} from "../../../../shared/interfaces/table.column.interface";
import {apiParameterInterface} from "../../../../shared/interfaces/apiParameter.interface";
import {IntraEbuzCategorieAudit, IntraEbuzCategorieAuditApi, LoopBackAuth} from "../../../../../shared/sdk";
import {AppGlobalEventManagerService} from "../../../../../common/services/app.globalEventManager.service";
import {AppSessionService} from "../../../../../common/services/app.session.service";

@Component({
  selector: 'TypeauditEditPage',
  templateUrl: './typeAudit.edit.html',
})
export class TypeAuditEdit implements OnInit, EditPageInterface{

  public etat$: Observable<any>;
  public columns: Array<TableColumnInterface>;
  public hiddenColumns;
  public form: FormGroup;
  public id;
  public object = null;
  public modelDef;
  public apiParameters: apiParameterInterface;

  public folder = 'typedepenses'; // For Uploads !
  public mode = null;

  constructor(
    public router: Router,
    public route: ActivatedRoute,
    public formBuilder: FormBuilder,
    public _sessionService: AppSessionService,
    public _toastr: ToastrService,
    public authService: LoopBackAuth,
    public apiService: IntraEbuzCategorieAuditApi,
    public appGlobalEventManagerService: AppGlobalEventManagerService
  ) {

    let self = this;

    this.appGlobalEventManagerService.getApiParameters().subscribe((parameters:apiParameterInterface) => {
      this.apiParameters = parameters
    });

    this.form = this.formBuilder.group({
      libelle: ['', [Validators.required]],
    });

    this.columns =
      [
        {type:"separator", name:"Type de dépense", icon:"fa-duotone fa-bookmark", exclude:true},
        {key: "libelle",name: "Libelle", type: "input"},
      ];

    this.modelDef = IntraEbuzCategorieAudit.getModelDefinition();
  }


  saveObject(datas:object){


    if(this.mode == 'edit'){
      this.apiService.patchAttributes(this.id, datas).pipe(
        map((res) => res),
        catchError((err) => {return of(err)})
      ).subscribe((res) => {
        if(res && !res.message){

          this._toastr.success('Succès !', 'Edition du type d\'audit')
          this.router.navigate(['/eBusiness','typesaudit']);

        }else{
          this._toastr.error(res.message, 'Edition du type d\'audit');
        }
      });
    }else{
      /* Create */


      this.apiService.create(datas).pipe(
        map((res) => res),
        catchError((err) => {return of(err)})
      ).subscribe((res) => {
        if(res && !res.message){

          this._toastr.success('Succès !', 'Création du type d\'audit')
          this.router.navigate(['/eBusiness','typesaudit']);

        }else{
          this._toastr.error(res.message, 'Création du type d\'audit');
        }
      });
    }
  }

  getParameter(){
    this.route.paramMap.subscribe(params => {
      this.id = params.get("id");
      if(this.id === 'new'){
        this.mode = 'create';
        this.etat$ = of(null);
      }else{
        this.mode = 'edit';
        this.etat$ = this.apiService.findById(this.id).pipe(share());
      }
    })
  }

  ngOnInit(): void {

    let self = this;

    this.getParameter();

    this.etat$.subscribe((etat:IntraEbuzCategorieAudit) => {
      if(etat){
        this.object = etat;

        /** Set Values **/
        this.columns.forEach((el) => {
          if(el.type != 'separator'  && (self.object[el.key] || self.object[el.key] === 0)){
            self.form.controls[el.key].setValue(self.object[el.key]);
          }
        })

        this.appGlobalEventManagerService.updateTitle('Edition type d\'audit '+this.object.libelle);
        this.appGlobalEventManagerService.updateBreadcrumb([{title:'Home', url:'/'}, {title:'Liste des types d\'audit', url:'/eBusiness/typesaudit'}, {title:'Edition type d\'audit '+this.object.libelle, url:null }])
      }else{
        this.appGlobalEventManagerService.updateTitle('Création type de dépense');
        this.appGlobalEventManagerService.updateBreadcrumb([{title:'Home', url:'/'}, {title:'Liste des types d\'audit', url:'/eBusiness/typesaudit'}, {title:'Création type d\'audit ', url:null }])
      }
    });

  }

}
