import {Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit} from "@angular/core";
import {ToastrService} from "ngx-toastr";
import moment from 'moment';
import 'moment-duration-format';
import {Observable} from "rxjs";
import {Router} from "@angular/router";
import {share} from "rxjs/operators";
import {ListPageInterface} from "../../../../shared/interfaces/list.page.interface";
import {TableColumnInterface} from "../../../../shared/interfaces/table.column.interface";
import {
  IntraEbuzCategorieAuditApi,
  LoopBackAuth,
  LoopBackFilter
} from "../../../../../shared/sdk";
import {AppSessionService} from "../../../../../common/services/app.session.service";
import {AppGlobalEventManagerService} from "../../../../../common/services/app.globalEventManager.service";

@Component({
  selector: 'TypeAuditListPage',
  templateUrl: './typeAudit.list.html',
})
export class TypeAuditList implements OnInit, ListPageInterface{

  public elements$: Observable<any>;
  public columns: Array<TableColumnInterface>;
  public hiddenColumns;

  public filtre: LoopBackFilter = {};

  constructor(
    public router: Router,
    public _sessionService: AppSessionService,
    public _toastr: ToastrService,
    public authService: LoopBackAuth,
    public apiService: IntraEbuzCategorieAuditApi,
    public appGlobalEventManagerService: AppGlobalEventManagerService
  ) {

    /** Titles & Breadcrumbs */
    this.appGlobalEventManagerService.updateTitle('Liste des types d\'audit');
    this.appGlobalEventManagerService.updateBreadcrumb([{title:'Home', url:'/'}, {title:'Liste des types d\'audit', url:null}])

    this.columns =
      [
        {key: 'libelle', name:"Nom", type: 'link', order:false, filter:false},
      ];
    this.load();
  }

  load() {

    this.elements$ = this.apiService.find({order:'libelle ASC'}).pipe(share());

  }

  delete(object: any) {
    if (object && object.id) {
      this.apiService.deleteById(object.id).subscribe(
        (res) => {
          this._toastr.success('Suppression du type d\'audit', 'Le type d\'audit a été supprimé avec succès');
          this.load(); // Refresh list
        },
        (err) => {this._toastr.success('Suppression du type d\'audit', err.message); }
      );
    }
  }

  filter(value: string, $event) {

  }

  ngOnInit(): void {
  }

}
