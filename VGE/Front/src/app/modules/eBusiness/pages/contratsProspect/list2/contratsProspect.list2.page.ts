import {Component, OnInit, ViewChild} from "@angular/core";
import {ToastrService} from "ngx-toastr";
import {AppGlobalEventManagerService} from "../../../../../common/services/app.globalEventManager.service";
import {
  IntraEbuzContratProspect,
  IntraEbuzContratProspectApi, IntraEbuzOperation,
  LoopBackFilter, SocietesApi, StorageBrowser
} from "../../../../../shared/sdk";
import {AppSessionService} from "../../../../../common/services/app.session.service";
import {LoopBackAuth} from "../../../../../shared/sdk";
import moment from 'moment';
import 'moment-duration-format';
import {forkJoin, Observable} from "rxjs";
import {TableColumnInterface} from "../../../../shared/interfaces/table.column.interface";
import {Router} from "@angular/router";
import {IntraEbuzContrat<PERSON>pi, IntraEbuzOperationApi} from "../../../../../shared/sdk";
import {share, shareReplay} from "rxjs/operators";
import {ListPageInterface} from "../../../../shared/interfaces/list.page.interface";
import {filterBuilder} from "../../../../shared/models/filterBuilder";
import {SelftablepaginateDirective} from "../../../../../directives/tables/selftablepaginate/selftablepaginate.directive";
import {NgxSpinnerService} from "ngx-spinner";

@Component({
  selector: 'ContratsProspectList2Page',
  templateUrl: './contratsProspect.list2.page.html',
})
export class ContratsProspectList2Page implements OnInit, ListPageInterface{

  public contratsProspects$: Observable<{[client:string]: {[operation:string]: {operation: IntraEbuzOperation, contratsProspects: IntraEbuzContratProspect[]}}}>;
  public contratsProspects: {[client:string]: {[operation:string]: {operation: IntraEbuzOperation, contratsProspects: IntraEbuzContratProspect[]}}} = {};

  public columns: TableColumnInterface[];
  public hiddenColumns;
  public moment = moment;

  public totalByYear: {[year:number]: number} = {};

  public filtre: LoopBackFilter = {};

  public operations$: Observable<any[]> = new Observable<any[]>();
  public clients$: Observable<any[]> = new Observable<any[]>();

  @ViewChild('list', {static:false}) declare table : SelftablepaginateDirective
  public serie:string = 'contratsPropects.list';

  public currentClient;
  public currentOperation;

  constructor(
    public router: Router,
    public _sessionService: AppSessionService,
    public spinner: NgxSpinnerService,
    public _toastr: ToastrService,
    public authService: LoopBackAuth,
    public apiService: IntraEbuzContratProspectApi,
    public apiContratService: IntraEbuzContratApi,
    public _apiOperationService: IntraEbuzOperationApi,
    public _apiSocieteService: SocietesApi,
    public appGlobalEventManagerService: AppGlobalEventManagerService,
    public storageService: StorageBrowser,
  ) {



    this.filtre.where = {};
    this.filtre.order = 'libelle ASC';

    this.operations$ = this._apiOperationService.find({include:[], fields:['id', 'libelle'], 'order':'libelle ASC'}).pipe(shareReplay(1));
    this.clients$ = this._apiSocieteService.find({include:[], fields:['id', 'nom'], 'order':'nom ASC'}).pipe(shareReplay(1));

    /** Titles & Breadcrumbs */
    this.appGlobalEventManagerService.updateTitle('Gestion des contrats prospects');
    this.appGlobalEventManagerService.updateBreadcrumb([{title:'Home', url:'/'}, {title:'Liste des contrats prospects', url:null}])

    this.columns =
      [
        {key: 'libelle', name:"Nom", type: 'link', order:false, filter:true},
        {key: 'etat', name:"État contrat", type: 'input', order:false, filter:true},
        {key: 'date', name:"date du contrat", type: 'date', order:false, filter:false},
        // {key: 'debut', name:"Date début validité contrat", type: 'date', order:false, filter:false},
        // {key: 'fin', name:"Date fin validité contrat", type: 'date', order:false, filter:false},
        {key: 'file', name:"Contrat joint", type: 'file', order:false, filter:false},
      ];

    this.load();

  }


  load(){
    this.appGlobalEventManagerService.openSpinner();

    this.contratsProspects$ = this.apiService.getContratsProspectByClientOperation().pipe(shareReplay(1));

    this.totalByYear = {};

    this.apiService.getContratsProspectByClientOperation().pipe(shareReplay(1)).subscribe((res) => {
      this.contratsProspects = res;

      Object.keys(this.contratsProspects).forEach(client => {
        Object.keys(this.contratsProspects[client]).forEach(operation => {
          this.contratsProspects[client][operation].contratsProspects.forEach(propect => {
            Object.keys(propect['previsionnelFacturation']).forEach((y) => {
              if(!this.totalByYear[y]){this.totalByYear[y] = 0;}
              this.totalByYear[y] += propect['previsionnelFacturation'][y];
            })
          })
        })
      })



      setTimeout(() => {
        if(this.storageService.get('TabCli')){
          this.currentClient = this.storageService.get('TabCli');
        }
        if(this.storageService.get('TabOp')){
          this.currentOperation = this.storageService.get('TabOp');
        }
        this.appGlobalEventManagerService.closeSpinner();
      }, 600)
    })
  }

  /**
   * AddFilter to tabReclamations
   * @param column
   * @param value
   */
  addFilter(column: TableColumnInterface, value, operateur="equal"){
    if(value && value.value && value.key){
      this.table.addFilter(column, value.key, operateur, value.value, this.serie);
    }else{
      if(column.key == 'num'){
        if(value.indexOf(',') > -1){
          this.table.addFilter(column, value.split(','), 'in', null, this.serie);
        }else{
          this.table.addFilter(column, value, operateur, null, this.serie);
        }
      }else{
        this.table.addFilter(column, value, operateur, null, this.serie);
      }

    }
  }

  updateCli(open, cli){
    if(open){
      this.storageService.set('TabCli', cli);
      // save localstorage
    } else {
      this.currentClient = null;
    }

  }

  updateOp(open, op) {
    if(open){
      this.storageService.set('TabOp', op);
      // save localstorage
    }else {
      this.currentOperation = null;
    }
  }

  clearFilter(column: TableColumnInterface){
    this.table.clearFilter(column);
  }

  delete(object: IntraEbuzContratProspect) {
    if (object && object.id) {
      this.apiService.deleteById(object.id).subscribe(
        (res) => {
          this._toastr.success('Suppression du contrat', 'Le contrat a été supprimé avec succès');
          this.load(); // Refresh list
        },
        (err) => {this._toastr.success('Suppression du contrat', err.message); }
      );
    }
  }

  transform(object: IntraEbuzContratProspect){

    this.apiContratService.convertFromContratProspect(object.id).subscribe({
      error: (err) => {
        this._toastr.error(err.message, 'Problème sur le la conversion');
      },
      next: (res) => {
        this._toastr.success('Convertir en contrat, veuillez définir la date de signature, et les détails sur les prestations', 'Le contrat a été converti avec succès');
        this.router.navigate(['/eBusiness','contrats', 'edit', res.id])
      },
      complete: () => {

      }

    })

  }

  filter(value: string, $event, operator="like") {
    if(!this.filtre || !this.filtre['where']){ this.filtre['where'] = {} } // init
    if(value && $event){
      if($event.target && $event.target.value){
        this.filtre['where'] = filterBuilder.buildTerm(value, $event.target.value, operator) ;
      } else { // cas du select !
        this.filtre['where'] = filterBuilder.buildTerm(value, $event, operator) ;
      }
    }
    this.load();
  }

  ngOnInit(): void {
  }

}
