<!-- Buttons TOP -->
<div class="row">
  <div class="col-7">



  </div>
  <div class="col-5 mb-1 text-right">

    <button class="btn btn-danger btn-social btn-sm mL5" [routerLink]="['/eBusiness', 'factures', 'previsionnelFacturation']"><i class="ft-corner-down-left"></i> Retour sur le previ. facturation</button>

  </div>
</div>
<!-- / Buttons TOP -->

<div class="row mB10">
  <div class="col-5">
    <div class="d-flex justify-content-start">
      <div class="flex-fill">
        <ng-select
          #listSeme
          [items]="operations$|async"
          [multiple]="false"
          [closeOnSelect]="true"
          [searchable]="true"
          bindLabel="libelle"
          placeholder="Choisir une opération"
          [(ngModel)]="currentOperation2"
          (change)="redirect($event)"
        >
        </ng-select>
      </div>
    </div>
  </div>

</div>

<prevBudgetaire
  [currentOperation]="currentOperation"
></prevBudgetaire>
