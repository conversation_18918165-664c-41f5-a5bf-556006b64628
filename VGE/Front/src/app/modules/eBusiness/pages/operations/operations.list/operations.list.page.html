<!--{{filtre|json}}-->

<div class="row mb-">
  <div class="col-4 mb-1">

  </div>
  <div class="col-8 mb-1 text-right">
    <button class="btn btn-success btn-social btn-sm mL5" [routerLink]="['/eBusiness', 'operations', 'edit', 'new']"><i class="fa-duotone fa-plus-square"></i> Créer d'une opération eBusiness</button>
  </div>
</div>

<div class="row">

  <div class="content-body col-12">
    <!-- / Buttons TOP -->

    <selfTablePaginate
      [columns]="columns"
      [defaultKeyColumn]="null"
      [data]="operationList|AsObservable"
      list="operationsEbuz"
      [idKey]="'id'"
      [bodyTopTemplate]="bodyTop"
      [bodyBottomTemplate]="bodyBottom"
      [headerTopTemplate]="headerTop"
      [headerBottomTemplate]="headerBottom"
      [footerTopTemplate]="headerTop"
      [footerBottomTemplate]="footerBottom"
      [actionsTemplate]="actions"
      #list
      [headerSearchTemplate]="headerSearchTemplate"
    >
    </selfTablePaginate>

    <ng-template #headerSearchTemplate>
      <tr>
        <th style="vertical-align: middle">
          <ng-select
            [items]="operationList"
            [multiple]="false"
            [closeOnSelect]="false"
            [searchable]="true"
            [bindLabel]="'libelle'"
            [bindValue]="'id'"
            (change)="addFilter({key:'id', type:'unknown',  bindLabel: 'libelle', libelle: 'Opération'}, $event?.id, 'equal', opSelect)"
            (clear)="addFilter({key:'id', type:'unknown',  bindLabel: 'libelle', libelle: 'Opération'}, null, 'equal', opSelect)"
            #opSelect
            [placeholder]="'Opération'"
          ></ng-select>
        </th>
        <th>
          <ng-select
            [items]="cliList"
            [multiple]="false"
            [closeOnSelect]="false"
            [searchable]="true"
            [bindLabel]="'cli'"
            [bindValue]="'id'"
            (change)="addFilter({key:'cli', type:'unknown',  bindLabel: 'cli', libelle: 'Client'}, $event?.cli, 'equal', opSelect)"
            (clear)="addFilter({key:'cli', type:'unknown',  bindLabel: 'cli', libelle: 'Client'}, null, 'equal', opSelect)"
            #opSelect
            [placeholder]="'Client'"
          ></ng-select>
        </th>
        <th>
          <ng-select
            [items]="fondList"
            [multiple]="false"
            [closeOnSelect]="false"
            [searchable]="true"
            [bindLabel]="'fond'"
            [bindValue]="'id'"
            (change)="addFilter({key:'fond', type:'unknown',  bindLabel: 'fond', libelle: 'Fond'}, $event?.fond, 'equal', opSelect)"
            (clear)="addFilter({key:'fond', type:'unknown',  bindLabel: 'fond', libelle: 'Fond'}, null, 'equal', opSelect)"
            #opSelect
            [placeholder]="'Fond'"
          ></ng-select>
        </th>
        <th>
          <ng-select
            [items]="[{id: 0, text:'Non archivée DO'}, {id: 1, text: 'Archivée DO'}]"
            [multiple]="false"
            [closeOnSelect]="false"
            [searchable]="true"
            [bindLabel]="'text'"
            [bindValue]="'id'"
            (change)="addFilter({key:'archiveDO', type:'unknown', libelle: 'Archivée DO ?'}, $event?.id, 'equal', opSelectArchiveeDO)"
            (clear)="addFilter({key:'archiveDO', type:'unknown'}, null, 'equal', opSelectArchiveeDO)"
            #opSelectArchiveeDO
            [placeholder]="'Archivée DO ?'"
          ></ng-select>
        </th>
        <th>
          <ng-select
            [items]="[{id: 0, text:'Non archivée GPA'}, {id: 1, text: 'Archivée GPA'}]"
            [multiple]="false"
            [closeOnSelect]="false"
            [searchable]="true"
            [bindLabel]="'text'"
            [bindValue]="'id'"
            (change)="addFilter({key:'archiveGPA', type:'unknown', libelle: 'Archivée GPA ?'}, $event?.id, 'equal', opSelectArchiveeGPA)"
            (clear)="addFilter({key:'archiveGPA', type:'unknown'}, null, 'equal', opSelectArchiveeGPA)"
            #opSelectArchiveeGPA
            [placeholder]="'Archivée GPA ?'"
          ></ng-select>
        </th>
        <th>
          <ng-select
            [items]="[{id: 0, text:'Non prospect'}, {id: 1, text: 'Prospect'}]"
            [multiple]="false"
            [closeOnSelect]="false"
            [searchable]="true"
            [bindLabel]="'text'"
            [bindValue]="'id'"
            (change)="addFilter({key:'isProspect', type:'unknown', libelle: 'Prospect ?'}, $event?.id, 'equal', opSelectProspect)"
            (clear)="addFilter({key:'isProspect', type:'unknown'}, null, 'equal', opSelectProspect)"
            #opSelectProspect
            [placeholder]="'Prospect ?'"
          ></ng-select>
        </th>
        <th>
          <ng-select
            [items]="[{id: 0, text:'Non mission audit'}, {id: 1, text: 'Mission audit'}]"
            [multiple]="false"
            [closeOnSelect]="false"
            [searchable]="true"
            [bindLabel]="'text'"
            [bindValue]="'id'"
            (change)="addFilter({key:'isAudit', type:'unknown', libelle: 'Mission audit ?'}, $event?.id, 'equal', opSelectAudit)"
            (clear)="addFilter({key:'isAudit', type:'unknown'}, null, 'equal', opSelectAudit)"
            #opSelectAudit
            [placeholder]="'Mission audit ?'"
          ></ng-select>
        </th>
        <th>
          <ng-select
            [items]="[{id: 0, text:'Non Opération GPA'}, {id: 1, text: 'Opération GPA'}]"
            [multiple]="false"
            [closeOnSelect]="false"
            [searchable]="true"
            [bindLabel]="'text'"
            [bindValue]="'id'"
            (change)="addFilter({key:'isgpa', type:'unknown', libelle: 'Opération GPA ?'}, $event?.id, 'equal', opSelectGPA)"
            (clear)="addFilter({key:'isgpa', type:'unknown'}, null, 'equal', opSelectGPA)"
            #opSelectGPA
            [placeholder]="'Opération GPA ?'"
          ></ng-select>
        </th>
        <th style="vertical-align: middle">
          <ng-select
            [items]="[{id: 0, text:'Non Opération DO'}, {id: 1, text: 'Opération DO'}]"
            [multiple]="false"
            [closeOnSelect]="false"
            [searchable]="true"
            [bindLabel]="'text'"
            [bindValue]="'id'"
            (change)="addFilter({key:'isdo', type:'unknown', libelle: 'Opération DO ?'}, $event?.id, 'equal', opSelectDO)"
            (clear)="addFilter({key:'isdo', type:'unknown'}, null, 'equal', opSelectDO)"
            #opSelectDO
            [placeholder]="'Opération DO ?'"
          ></ng-select>
        </th>
        <th style="vertical-align: middle">
          <ng-select
            [items]="[{id: 0, text:'Non Opération Multi DO'}, {id: 1, text: 'Opération Multi DO'}]"
            [multiple]="false"
            [closeOnSelect]="false"
            [searchable]="true"
            [bindLabel]="'text'"
            [bindValue]="'id'"
            (change)="addFilter({key:'ismri', type:'unknown', libelle: 'Opération Multi DO ?'}, $event?.id, 'equal', opSelectMultiDO)"
            (clear)="addFilter({key:'ismri', type:'unknown'}, null, 'equal', opSelectMultiDO)"
            #opSelectMultiDO
            [placeholder]="'Opération Multi DO ?'"
          ></ng-select>
        </th>
        <th style="vertical-align: middle">
          <ng-select
            [items]="adminsSelect"
            [multiple]="true"
            [closeOnSelect]="false"
            [searchable]="true"
            [bindLabel]="'text'"
            [bindValue]="'id'"
            [groupBy]="'societe'"
            (change)="addFilter({key:'adminsIds', type:'unknown', libelle: 'Admin lié', bindLabel: 'text', bindValue: 'id', map:'key'}, $event, 'in', getAdminsSelect)"
            (clear)="addFilter( {key:'adminsIds', type:'unknown', libelle: 'Admin lié', bindLabel: 'text', bindValue: 'id'}, null, 'equal', getAdminsSelect)"
            #getAdminsSelect
            [placeholder]="'Admins'"
          ></ng-select>
        </th>
        <th style="vertical-align: middle">
          <ng-select
            [items]="[{id: 1, text: 'Selon la date de création du sinistre'}, {id: 0, text: 'Selon la date de facturation'}]"
            [multiple]="false"
            [closeOnSelect]="false"
            [searchable]="true"
            [bindLabel]="'text'"
            [bindValue]="'id'"
            (change)="addFilter({key:'getContratByDateDo', type:'unknown', libelle: 'Méthode de récupération de contrat'}, $event?.id, 'equal', getContratopSelect)"
            (clear)="addFilter({key:'getContratByDateDo', type:'unknown', libelle: 'Méthode de récupération de contrat'}, null, 'equal', getContratopSelect)"
            #getContratopSelect
            [placeholder]="'Méthode de récupération de contrat'"
          ></ng-select>
        </th>
        <th style="vertical-align: middle"></th>
      </tr>

    </ng-template>

    <ng-template #bodyTop let-object let-orderWay="orderWay" let-orderField="orderField"></ng-template>
    <ng-template #bodyBottom let-object let-orderWay="orderWay" let-orderField="orderField">
      <td>
        {{object?.cli}}
      </td>
      <td>
        {{object?.fond}}
      </td>
      <td>
        <ui-switch
          [checked]="object?.archiveDO === 1"
          [size]="'small'"
          (change)="updateObject(object, $event, 'archiveDO')"
        >
        </ui-switch>
      </td>
      <td>
        <ui-switch
          [checked]="object?.archiveGPA === 1"
          [size]="'small'"
          (change)="updateObject(object, $event, 'archiveGPA')"
        >
        </ui-switch>
      </td>
      <td>
        <ui-switch
          [checked]="object?.isProspect === 1"
          [size]="'small'"
          (change)="updateObject(object, $event, 'isProspect')"
        >
        </ui-switch>
      </td>
      <td>
        <ui-switch
          [checked]="object?.isAudit === 1"
          [size]="'small'"
          (change)="updateObject(object, $event, 'isAudit')"
        >
        </ui-switch>
      </td>
      <td>
        <ui-switch
          [checked]="object?.isgpa === 1"
          [size]="'small'"
          (change)="updateObject(object, $event, 'isgpa')"
        >
        </ui-switch>
      </td>
      <td>
        <ui-switch
          [checked]="object?.isdo === 1"
          [size]="'small'"
          (change)="updateObject(object, $event, 'isdo')"
        >
        </ui-switch>
      </td>
      <td>
        <ui-switch
          [checked]="object?.ismri === 1"
          [size]="'small'"
          (change)="updateObject(object, $event, 'ismri')"
        >
        </ui-switch>
      </td>
      <td>
        <div class="d-flex justify-content-between">
          <div>
            <div class="media mB2" *ngFor="let admin of object.admins">
              <people [showMediaPhoto]="true" [showInitiales]="false" [showGroup]="false" [small]="true" [people]="admin"></people>
            </div>
          </div>
          <div>
            <button class="btn btn-sm btn-info pull-right"
                    placement="right"
                    [ngbTooltip]="'Attribuer des admins à cette opération'"
                    (click)="openModaleAttribueAdminsOperation(object)"
            > <i class="ft-edit"></i>
            </button>
          </div>
        </div>
      </td>
      <td>
        <span class="text-warning" *ngIf="object?.getContratByDateDo === 1">Selon la date de création du sinistre</span>
        <span class="text-success" *ngIf="object?.getContratByDateDo === 0">Selon la date de facturation</span>
      </td>
    </ng-template>

    <ng-template #headerTop></ng-template>
    <ng-template #headerBottom>
      <th>Client</th>
      <th>Fond</th>
      <th>Archivée DOnet</th>
      <th>Archivée GPAnet</th>
      <th>Operation Prospect</th>
      <th>Operation Audit</th>
      <th>Operation GPA</th>
      <th>Operation DO</th>
      <th>Operation Multi-risques DO</th>
      <th>Admins attribués</th>
      <th>Méthode de récupération de contrat</th>
    </ng-template>
    <ng-template #actions let-object let-orderWay="orderWay" let-orderField="orderField">
      <ng-container *ngIf="user|hasRight:'admin'">
        <div class="btn-group" role="group">
          <button [ngbTooltip]="'Modifier'" [routerLink]="['/eBusiness','operations', 'edit', object.id]" class="btn btn-icon btn-sm btn-success">
            <i class="ft-edit-3"></i>
          </button>
          <button type="button" [ngbTooltip]="'Supprimer'"
                  *ngIf="!(object.isgpa || object.isdo)"
                  class="btn btn-icon btn-sm btn-danger"
                  mwlConfirmationPopover
                  [popoverTitle]="'Suppression de l\'opération Ebusiness'"
                  [popoverMessage]="'Êtes vous sur de vouloir supprimer cette opération '+object.libelle+' ?'"
                  placement="left"
                  (confirm)="delete(object)"
          >
            <i class="fa-duotone fa-trash-alt"></i>
          </button>
        </div>
      </ng-container>
    </ng-template>

    <ng-template #footerTop let-orderWay="orderWay" let-orderField="orderField"></ng-template>
    <ng-template #footerBottom let-orderWay="orderWay" let-orderField="orderField"></ng-template>

  </div>
</div>

