<!-- Buttons TOP -->
<div class="row">
  <div class="col-12 mb-1 text-right">

    <button class="btn btn-success btn-social btn-sm mL5" [routerLink]="['/eBusiness','prestations', 'edit', 'new']"><i class="ft-plus-square"></i> Ajouter une prestation</button>

  </div>
</div>
<!-- / Buttons TOP -->

<div class="content-body">

  <selfTablePaginate
    [data]="elements$"
    [columns]="columns"
    [defaultKeyColumn]="'libelle'"
    [idKey]="'id'"

    [actionsTemplate]="actions"

    #list
    list="prestations">
  </selfTablePaginate>

  <ng-template #actions let-object let-orderWay="orderWay" let-orderField="orderField">
    <div class="btn-group" role="group" aria-label="Basic example">
      <button type="button" [ngbTooltip]="'Modifier'" class="btn btn-icon btn-sm btn-success" [routerLink]="['/eBusiness','prestations', 'edit', object.id]"><i class="ft-edit-3"></i></button>
      <button type="button" [ngbTooltip]="'Supprimer'"
              *ngIf="!object.code"
              class="btn btn-icon btn-sm btn-danger"
              mwlConfirmationPopover
              [popoverTitle]="'Suppression de la prestation'"
              [popoverMessage]="'Êtes vous sur de vouloir supprimer cette prestation '+object.libelle+' ?'"
              placement="left"
              (confirm)="delete(object)"
      >
        <i class="fa-duotone fa-trash-alt"></i>
      </button>
    </div>

  </ng-template>

</div>
