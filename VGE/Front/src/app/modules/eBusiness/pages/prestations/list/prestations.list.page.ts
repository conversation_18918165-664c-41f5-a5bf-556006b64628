import {<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>t, ViewChild} from "@angular/core";
import {ToastrService} from "ngx-toastr";
import {AppGlobalEventManagerService} from "../../../../../common/services/app.globalEventManager.service";
import {
  IntraEbuzPrestation,
  LoopBackFilter,
  People
} from "../../../../../shared/sdk/models";
import {AppSessionService} from "../../../../../common/services/app.session.service";
import {LoopBackAuth} from "../../../../../shared/sdk/services/core";
import moment from 'moment';
import 'moment-duration-format';
import {Observable} from "rxjs";
import {TableColumnInterface} from "../../../../shared/interfaces/table.column.interface";
import {SelftablepaginateDirective} from "../../../../../directives/tables/selftablepaginate/selftablepaginate.directive";
import {Router} from "@angular/router";
import {NgxSpinnerService} from "ngx-spinner";
import {IntraEbuzPrestationApi} from "../../../../../shared/sdk";
import {shareReplay} from "rxjs/operators";
import {filterBuilder} from "../../../../shared/models/filterBuilder";
import {NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {ROLES} from "../../../../../datas/roles.helpers";
import {PeopleWithrightsInterface} from "../../../../../common/interfaces/peopleWithrights.interface";

@Component({
  selector: 'PrestationsListPage',
  templateUrl: './prestations.list.page.html',
})
export class PrestationsListPage implements OnInit{

  public elements$: Observable<IntraEbuzPrestation[]>;
  public elementsSubscribed: IntraEbuzPrestation[] = [];
  public columns: TableColumnInterface[];
  public hiddenColumns;

  public moment = moment;

  public checkFilterValue: boolean = false;

  public filtre: LoopBackFilter = {};

  @ViewChild('list', {static:false}) declare table : SelftablepaginateDirective
  public serie:string = 'prestations.list';

  public user: PeopleWithrightsInterface;
  public ROLES = ROLES;

  constructor(
    public router: Router,
    public _sessionService: AppSessionService,
    public spinner: NgxSpinnerService,
    public _toastr: ToastrService,
    public authService: LoopBackAuth,
    public apiService: IntraEbuzPrestationApi,
    public modalService: NgbModal,
    public appGlobalEventManagerService: AppGlobalEventManagerService
  ) {

    this._sessionService.logguedAccountEmitter.subscribe((people) => {
      this.user = people;
    });

    this.filtre.where = {};

    this.filtre.order = 'libelle ASC';

    /** Titles & Breadcrumbs */
    this.appGlobalEventManagerService.updateTitle('Liste des prestations modèles');
    this.appGlobalEventManagerService.updateBreadcrumb([{title:'Home', url:'/'}, {title:'Liste des prestations', url:null}])

    this.columns =
      [
        {key: 'libelle', name:"Nom de la prestation", type: 'input', order:false, filter:false},
        {key: 'code', name:"Code", type: 'input', order:false, filter:false},
        {key: 'recurenceFacture', name:"Facturé", type: 'input', order:false, filter:false},
        {key: 'typeMontant', name:"Type de montant", type: 'input', order:false, filter:false},
        {key: 'unitesMontant', name:"Nombre d'unité", type: 'input', order:false, filter:false},
        {key: 'montant', name:"Montant", type: 'input', order:false, filter:false},
        {key: 'nombreLimiteDo', name:"Nombre limite de DO", type: 'input', typeInput:'number', order:false, filter:false},
      ];
    this.load();

  }


  load(){
    this.appGlobalEventManagerService.openSpinner();

    this.filtre.where = {};

    this.elements$ = <Observable<IntraEbuzPrestation[]>>this.apiService.find(this.filtre).pipe(shareReplay(1));

    this.elements$.subscribe((elements:IntraEbuzPrestation[]) => {
      this.elementsSubscribed = elements;

      setTimeout(() => {
        this.appGlobalEventManagerService.closeSpinner();
      }, 600)
    });

  }

  /**
   * AddFilter to tabReclamations
   * @param column
   * @param value
   */
  addFilter(column: TableColumnInterface, value, operateur="equal"){

    if(value && value.value && value.key){
      this.table.addFilter(column, value.key, operateur, value.value, this.serie);
    }else{
      if(column.key == 'num'){
        if(value.indexOf(',') > -1){
          this.table.addFilter(column, value.split(','), 'in', null, this.serie);
        }else{
          this.table.addFilter(column, value, operateur, null, this.serie);
        }
      }else{
        this.table.addFilter(column, value, operateur, null, this.serie);
      }

    }
  }

  clearFilter(column: TableColumnInterface){
    this.table.clearFilter(column);
  }

  delete(object: any) {
    if (object && object.id) {
      this.apiService.deleteById(object.id).subscribe(
        (res) => {
          this._toastr.success('Suppression de la facture', 'La facture a été supprimé avec succès');
          this.load(); // Refresh list
        },
        (err) => {this._toastr.success('Suppression de la facture', err.message); }
      );
    }
  }

  filter(value: string, $event, operator="like") {
    if(!this.filtre || !this.filtre['where']){ this.filtre['where'] = {} } // init
    if(value && $event){
      if($event.target && $event.target.value){
        this.filtre['where'] = filterBuilder.buildTerm(value, $event.target.value, operator) ;
      } else { // cas du select !
        this.filtre['where'] = filterBuilder.buildTerm(value, $event, operator) ;
      }
    }
    this.load();
  }


  ngOnInit(): void {



  }


}
