<!-- Buttons TOP -->
<div class="row">

  <div class="col-6">
    <factureFinder
      [operations$]="operations$"
    ></factureFinder>
  </div>
  <div class="col-6 mb-1 text-right">
    <button class="btn btn-info btn-social btn-sm mL5" [routerLink]="['/eBusiness','factures', 'echeanciers', 'list']"><i class="fa-duotone fa-table"></i> Echéanciers opérations GPA</button>
    <button class="btn btn-info btn-social btn-sm mL5" [routerLink]="['/eBusiness', 'factures', 'previsionnelBudgetaire']" [ngbTooltip]="'Accéder à la Facturation DO'"><i class="fa-duotone fa-table"></i> Facturation Opérations DO</button>
    <a class="btn btn-success btn-social btn-sm mL5" [routerLink]="['/eBusiness','factures', 'create']"><i class="ft-plus-square"></i> Ajouter une facture</a>
  </div>
</div>

<div class="row mB20">
  <div class="col-6 mT20">
    <factureYearFinder></factureYearFinder>
  </div>
  <div class="col-6 mT20">
  </div>
</div>

<!-- / Buttons TOP -->


<div class="row">
  <div class="col-6">
    <h3 class="text-center">Prévisionnel Facturation</h3>
    <div echarts [options]="statsPrevisionnel" class="demo-chart" style="height: 350px; width: 100%"></div>
  </div>
  <div class="col-6">
    <h3 class="text-center">Réel Facturation</h3>
    <div echarts [options]="statsReel" class="demo-chart" style="height: 350px; width: 100%"></div>
  </div>
</div>


<div class="row mB30 d-flex justify-content-center">
  <div class="" *ngFor="let year of years$|async; let i = index " [ngClass]="{
    'col-2': years?.length >= 6,
    'col-3': years?.length == 4,
    'col-4': years?.length == 3 || years?.length == 5,
    'col-6': years?.length == 2,
    'col-12': years?.length == 1
  }">
    <a (click)="changeYear(year)" href="javascript:void(0)" class="btn btn-success btn-block mB10" [ngClass]="{'btn-warning': currentYear == year}">
      <span>{{year}}</span>
    </a>
  </div>
</div>


<div class="row mB30 d-flex justify-content-center">
  <div class="col-4">
    <label for="typeFact">Types de factures (Toutes, factures prévisionnelles, factures réelles) :</label>
    <ng-select
      id="typeFact"
      [items]="[{id: 'ALL', text: 'Tous'}, {id: 'prev', text: 'Factures prévisionnelles'}, {id: 'reel', text: 'Factures réelles'}]"
      [ngModel]="typeFacture"
      [bindValue]="'id'"
      [bindLabel]="'text'"
      (change)="changeTypefacture($event)"
      ></ng-select>
  </div>
  <div class="col-4">
    <label for="typeFact">Payées ou non :</label>
    <ng-select
      id="payees"
      [items]="[{id: 'ALL', text: 'Toutes'}, {id: 'payees', text: 'Factures payées'}, {id: 'nonpayees', text: 'Factures non payées'}]"
      [ngModel]="payeFacture"
      [bindValue]="'id'"
      [bindLabel]="'text'"
      (change)="changePayeefacture($event)"
    ></ng-select>
  </div>
</div>

<div class="row mB20">
  <div class="col-md-6">
    <h4>Affichage des factures de l'année <strong>{{currentYear}}</strong></h4>
  </div>

  <div class="col-md-6 text-right">

    <div class="d-flex justify-content-end">
      <exporteBuizDownload
        [ngbTooltip]="'Exporter la liste des factures réelles de '+currentYear+' ?'"
        placement="left"
        [class]="'btn btn-blue btn-social btn-sm mL5'"
        [operationId]=""
        [refresh]="true"
        [refreshButton]="true"
        [XLSX]="true"
        [typeExport]="'facturesReelles'"
        [year]="currentYear"
        [label]="'Exporter factures réelles'"
      ></exporteBuizDownload>
    </div>
  </div>

</div>

<div class="content-body">

  <selfTablePaginate
    [data]="elements$"
    [columns]="columns"
    [defaultKeyColumn]="'libelle'"
    [idKey]="'id'"

    [actionsTemplate]="actions"
    [bodyTopTemplate]="bodyTop"
    [headerTopTemplate]="headerTop"
    [footerTopTemplate]="footerTop"
    [bodyBottomTemplate]="bodyBottom"
    [headerBottomTemplate]="headerBottom"
    [footerBottomTemplate]="footerBottom"

    [headerSearchTemplate]="headerSearchTemplate"

    [ligneSupTemplate]="ligneSupTemplate"

    #list
    list="factures">
  </selfTablePaginate>

  <ng-template #ligneSupTemplate let-object>
    <table class="w-100">
      <thead>
        <tr>
          <th colspan="6" class="text-center"><strong>Prestations {{(object?.numreal) ? object?.numreal : ('PR-'+object?.num)}}</strong></th>
        </tr>
        <tr>
          <th>Libellé</th>
          <th>Type Prestation</th>
          <th>Montant HT</th>
          <th>Montant TTC</th>
        </tr>
      </thead>
      <tbody>
        <ng-container *ngFor="let ligne of object.lignesFacture">
          <tr [ngClass]="{disabled: !ligne?.state}" [ngbTooltip]="(!ligne?.state) ? 'Ligne désactivée et non comptabilisée dans la facture, désactivée de la génération automatique' : null">
            <td>{{ligne.libelle}}</td>
            <td>{{ligne?.prestation?.typePrestation?.libelle}}</td>
            <td>{{ligne?.grandTotalHT}}</td>
            <td>{{ligne?.grandTotalTTC}}</td>
          </tr>
        </ng-container>
      </tbody>
    </table>
  </ng-template>

  <ng-template #actions let-object let-orderWay="orderWay" let-orderField="orderField">
    <div class="btn-group" role="group" aria-label="Basic example">

      <exporteBuizDownload
          [ngbTooltip]="'Exporter la facture '+(object?.numreal) ? object?.numreal : ('PR-'+object?.num)+' ?'"
          placement="left"
          [class]="'btn btn-blue btn-icon btn-sm'"
          [factureId]="object?.id"
          [typeExport]="'facture'"
      ></exporteBuizDownload>

      <button type="button" [ngbTooltip]="'Modifier'" class="btn btn-icon btn-sm btn-success" [routerLink]="['/eBusiness','factures', 'fiche', object.id]"><i class="ft-edit-3"></i></button>
      <button type="button" [ngbTooltip]="'Supprimer'"
              class="btn btn-icon btn-sm btn-danger"
              mwlConfirmationPopover
              [popoverTitle]="'Suppression du contrat'"
              [popoverMessage]="'Êtes vous sur de vouloir supprimer cette facture ' + object.titre + ' ?'"
              placement="left"
              (confirm)="delete(object)"
      >
        <i class="fa-duotone fa-trash-alt"></i>
      </button>
    </div>

  </ng-template>

  <ng-template #bodyTop let-object let-index>
    <td class="text-nowrap">
      <button class="btn btn-warning btn-sm" *ngIf="!list.isVisible(object.id)"
              [ngbTooltip]="'Voir les prestations de la facture '+(object?.numreal) ? object?.numreal : ('PR-'+object?.num)" (click)="list.setVisible(object.id, true)" placement="right" ><i class="ft-eye"></i></button>
      <button class="btn btn-success btn-sm" *ngIf="list.isVisible(object?.id)"
              [ngbTooltip]="'Cacher les prestations de la facture '+(object?.numreal) ? object?.numreal : ('PR-'+object?.num)" (click)="list.setVisible(object.id, false)" placement="right" ><i class="ft-eye-off"></i></button>
      <button class="btn btn-sm btn-primary mL5 mR5" (click)="openFacture(object)" [ngbTooltip]="'Options de gestion de la facture ' + (object?.numreal) ? object?.numreal : ('PR-'+object?.num)"><i class="fa-duotone fa-cog"></i></button>
    </td>
    <td class="text-nowrap">
      <a class="btn btn-success btn-sm mR5"
              [ngbTooltip]="'Voir la fiche de la facture '+(object?.numreal) ? object?.numreal : ('PR-'+object?.num)"
              placement="right"
              [routerLink]="['/eBusiness', 'factures', 'fiche', object.id]"
      ><i class="ft-folder"></i></a>
      <span *ngIf="object?.numreal" class="text-success">{{object?.numreal}}</span>
      <span *ngIf="!object?.numreal" class="text-warning">{{'PR-'+object?.num}}</span>
    </td>
    <td style="white-space: nowrap">
      <ng-container *ngIf="object?.datePaye === null">
        <span *ngIf="moment(object?.dateRelance).isValid()">{{object?.dateRelance|momentDateFormat:'YYYY-MM-DD HH:mm:ss':'DD/MM/YYYY' }}</span>
<!--        <button class="btn btn-info btn-sm pull-right"-->
<!--                placement="right"-->
<!--                [ngbTooltip]="'Effectuer une relance sur la facture '+object?.num"-->
<!--                (click)="openModaleChangePayerFacture(object, 'datePaye')"-->
<!--        ><i class="ft-mail"></i></button>-->
      </ng-container>
    </td>
    <td>
      <span *ngIf="object?.factureContrat === 0" class="badge badge-success">Opération</span>
      <span *ngIf="object?.factureContrat === 1" class="badge badge-warning">Contrat</span>
    </td>

    <td class="text-center" >{{ moment(object?.dateFacturation).quarter() }}</td>

    <td>{{ object?.operation?.libelle }}</td>

    <td>{{ object?.client?.nom }}</td>
    <td>{{ object?.dateFacturation|momentDateFormat:'YYYY-MM-DD HH:mm:ss':'DD/MM/YYYY' }}
      <ng-container *ngIf="object.datePaye === null && moment(object?.dateFacturation).isValid()">
        (<span>{{(moment(object?.dateFacturation, 'YYYY-MM-DD').isBefore(moment())) ? '-' : '+'}}</span>{{moment.duration(moment().diff(moment(object?.dateFacturation, 'YYYY-MM-DD'))).humanize()}})
      </ng-container>
      <button class="btn btn-sm btn-info pull-right"
              placement="left"
              *ngIf="!(object?.numreal)"
              [ngbTooltip]="'Changer la date de facturation de cette facture'"
              (click)="openModaleChangeDateFacture(object, 'dateFacturation', 'Date de facturation')"
      > <i class="ft-edit"></i>
      </button>
    </td>
<!--    <td>{{ object?.date|momentDateFormat:'YYYY-MM-DD HH:mm:ss':'DD/MM/YYYY' }}-->
<!--      <button class="btn btn-sm btn-info pull-right"-->
<!--              placement="left"-->
<!--              *ngIf="object?.datePaye === null"-->
<!--              [ngbTooltip]="'Changer la date de création de cette facture'"-->
<!--              (click)="openModaleChangeDateFacture(object, 'date', 'Date de création')"-->
<!--      > <i class="ft-edit"></i>-->
<!--      </button>-->
<!--    </td>-->
    <td>
      <span [ngStyle]="{'color':object?.echeanceColor}">{{object?.echeance|momentDateFormat:'YYYY-MM-DD HH:mm:ss':'DD/MM/YYYY'}}</span>

      <ng-container *ngIf="object.datePaye === null && moment(object?.echeance).isValid()">
        (<span>{{(moment(object?.echeance, 'YYYY-MM-DD').isBefore(moment())) ? '-' : '+'}}</span>{{moment.duration(moment().diff(moment(object?.echeance, 'YYYY-MM-DD'))).humanize()}})
      </ng-container>

      <button class="btn btn-sm btn-info pull-right"
              placement="left"
              *ngIf="!(object?.datePaye !== null || object?.dateEnvoyee !== null)"
              [ngbTooltip]="'Changer la date d\'échéance de cette facture'"
              (click)="openModaleChangeDateFacture(object, 'echeance', 'Date d\'échéance')"
      > <i class="ft-edit"></i>
      </button>
    </td>
<!--    <td>-->
<!--      {{object?.titre}}-->
<!--      <button class="btn btn-sm btn-info pull-right"-->
<!--              placement="left"-->
<!--              [ngbTooltip]="'Changer le titre de cette facture'"-->
<!--              (click)="openModaleChangeTitreFacture(object, 'titre', 'Titre')"-->
<!--      > <i class="ft-edit"></i>-->
<!--      </button>-->
<!--    </td>-->
  </ng-template>
  <ng-template #bodyBottom let-object let-index>

    <td width="120">
      {{object?.montantHT|currency: 'EUR':'symbol':'0.2-2':'fr-FR'}}
<!--      <button class="btn btn-sm btn-info pull-right"-->
<!--              placement="left"-->
<!--              *ngIf="(user|hasRight:'admin')"-->
<!--              [ngbTooltip]="'Changer le montant HT de cette facture'"-->
<!--              (click)="openModaleChangeMontantFacture(object, 'montantHT', 'Montant HT')"-->
<!--      > <i class="ft-edit"></i>-->
<!--      </button>-->
    </td>

    <td width="120">
      {{object?.montantTTC|currency: 'EUR':'symbol':'0.2-2':'fr-FR'}}
<!--      <button class="btn btn-sm btn-info pull-right"-->
<!--              placement="left"-->
<!--              *ngIf="(user|hasRight:'admin')"-->
<!--              [ngbTooltip]="'Changer le montant TTC de cette facture'"-->
<!--              (click)="openModaleChangeMontantFacture(object, 'montantTTC', 'Montant TTC')"-->
<!--      > <i class="ft-edit"></i>-->
<!--      </button>-->
    </td>

    <td width="120">
      {{object?.TVA}}%
    </td>

    <td width="120">
      {{(object?.montantTTC - object?.montantHT)|currency: 'EUR':'symbol':'0.2-2':'fr-FR'}}
    </td>

    <td width="100" class="text-nowrap">
      <ng-container *ngFor="let ech of object?.echeances">
        <div>
          <a class="btn btn-success btn-sm mR5"
             [ngbTooltip]="'Voir la fiche de la facture '+(ech?.fac2?.numreal) ? ech?.fac2?.numreal : ('PR-'+ech?.fac2?.num)"
             placement="right"
             [routerLink]="['/eBusiness', 'factures', 'fiche', ech?.fac2.id]"
          ><i class="ft-folder"></i></a>
          <span *ngIf="ech?.fac2?.numreal" class="text-success">{{ech?.fac2?.numreal}}</span>
          <span *ngIf="!ech?.fac2?.numreal" class="text-warning">{{'PR-'+ech?.fac2?.num}}</span>
        </div>
      </ng-container>

      <ng-container *ngFor="let ech of object?.estEcheancier">
        <ng-container *ngIf="ech?.facture_id !== ech?.facture2_id">
          <div>
            <a class="btn btn-success btn-sm mR5"
               [ngbTooltip]="'Voir la fiche de la facture '+(ech?.fac?.numreal) ? ech?.fac?.numreal : ('PR-'+ech?.facnum)"
               placement="right"
               [routerLink]="['/eBusiness', 'factures', 'fiche', ech?.fac?.id]"
            ><i class="ft-folder"></i></a>
            <span *ngIf="ech?.fac?.numreal" class="text-success">{{ech?.fac?.numreal}}</span>
            <span *ngIf="!ech?.fac?.numreal" class="text-warning">{{'PR-'+ech?.fac?.num}}</span>
          </div>
        </ng-container>
      </ng-container>
    </td>

    <td>
      <div class="d-flex justify-content-around">
        <div class="" [innerHTML]="object?.txtlibre"></div>
        <div>
          <button class="btn btn-sm btn-info pull-right"
                  placement="left"
                  [ngbTooltip]="'Modifier l\'observation de la facture'"
                  (click)="openModaleChangeObservationFacture(object)"
          > <i class="ft-edit"></i>
          </button>
        </div>
      </div>
    </td>

    <td width="120">
      <span class="badge badge-danger" *ngIf="object?.dateEnvoyee === null || object?.dateEnvoyee === '0000-00-00 00:00:00'">Non envoyée</span>
      <span class="badge badge-success" *ngIf="object?.dateEnvoyee !== null && object?.dateEnvoyee !== '0000-00-00 00:00:00'" [ngbTooltip]="'Date d\'envoi'">{{object?.dateEnvoyee|momentDateFormat:'YYYY-MM-DD HH:mm:ss':'DD/MM/YYYY'}}</span>

      <button class="btn btn-sm btn-info pull-right"
              placement="left"
              *ngIf="object?.dateEnvoyee === null"
              [ngbTooltip]="'Changer le statut et la date d\'envoi de cette facture'"
              (click)="openModaleChangeEnvoyerFacture(object, 'dateEnvoyee')"
      > <i class="ft-edit"></i>
      </button>
    </td>

    <td width="120">
      <span class="badge badge-danger" *ngIf="object?.datePaye === null || object?.datePaye === '0000-00-00 00:00:00'">Non payée</span>
      <span class="badge badge-success" *ngIf="object?.datePaye !== null && object?.datePaye !== '0000-00-00 00:00:00'" [ngbTooltip]="'Date de règlement effectif'">{{object?.datePaye|momentDateFormat:'YYYY-MM-DD HH:mm:ss':'DD/MM/YYYY'}}</span>

      <button class="btn btn-sm btn-info pull-right"
        placement="left"
        *ngIf="object?.datePaye === null"
        [ngbTooltip]="'Changer le statut payé de cette facture'"
        (click)="openModaleChangePayerFacture(object, 'datePaye')"
      > <i class="ft-edit"></i>
      </button>
    </td>

  </ng-template>

  <ng-template #headerTop let-object>
    <th></th>
    <th>Numéro</th>
    <th>Relance</th>
    <th>Type</th>
    <th class="text-center" [container]="'body'" [ngbTooltip]="'Trimestre de la date de facturation'">T</th>
    <th>Opération</th>
    <th>Client</th>
    <th><span [ngbTooltip]="'Date prévue d\'envoi de la facture'">Date Facturation</span></th>
<!--    <th>Date de création</th>-->
    <th>Date d'échéance</th>
<!--    <th>Titre</th>-->
  </ng-template>
  <ng-template #headerBottom>
    <th>HT</th>
    <th>TTC</th>
    <th>TVA %</th>
    <th>TVA</th>
    <th>Échéanciers</th>
    <th>Observations</th>
    <th><span [ngbTooltip]="'Date réellement envoyée'">Envoyée ?</span></th>
    <th>Payée ?</th>
  </ng-template>

  <ng-template #footerTop>
    <th></th>
    <th>{{NB}}</th>
    <th></th>
    <th></th>
    <th></th>
    <th></th>
    <th></th>
    <th></th>
<!--    <th></th>-->
    <th></th>
<!--    <th></th>-->
  </ng-template>
  <ng-template #footerBottom>
    <th>{{THT|currency: 'EUR':'symbol':'0.2-2':'fr-FR'}}</th>
    <th>{{TTTC|currency: 'EUR':'symbol':'0.2-2':'fr-FR'}}</th>
    <th></th>
    <th>{{TTVA|currency: 'EUR':'symbol':'0.2-2':'fr-FR'}}</th>
    <th></th>
    <th></th>
    <th></th>
    <th></th>
  </ng-template>

  <ng-template  #headerSearchTemplate let-object>

    <tr>
      <th></th>
      <th>
        <input   type="search"
                 class="form-control"
                 (change)="addFilter({key: 'num', name: 'Numéro'}, $event.target['value'], 'like')"
                 (search)="clearFilter({key: 'num', name: 'Numéro'})"
                 [placeholder]="'Numéro'"
        />
      </th>
      <th></th>

      <th>
        <ng-select
          [items]="[{id: 0, 'libelle': 'Facture opération'}, {id: 1, 'libelle': 'Facture contrat'}]"
          [multiple]="false"
          [closeOnSelect]="true"
          [searchable]="true"
          bindLabel="libelle"
          bindValue="id"
          [placeholder]="'Type'"

          (change)="addFilter({key: 'factureContrat', name: 'Type de facture'}, $event && $event['id'] ? $event['id'] : null)"
        ></ng-select>
      </th>
      <th>
        <ng-select
          [items]="[{id: 1, 'libelle': '1er'}, {id: 2, 'libelle': '2ème'}, {id: 3, 'libelle': '3ème'}, {id: 4, 'libelle': '4ème'}]"
          [multiple]="false"
          [closeOnSelect]="true"
          [searchable]="true"
          bindLabel="libelle"
          bindValue="id"
          [placeholder]="'Tri.'"

          (change)="addFilter({key: 'quarter', name: 'T'}, $event && $event['id'] ? $event['id'] : null)"
        ></ng-select>
      </th>
<!--      <th></th>-->
      <th>
        <ng-select
          [items]="operations$|async"
          [multiple]="false"
          [closeOnSelect]="true"
          [searchable]="true"
          bindLabel="libelle"
          bindValue="id"
          [placeholder]="'Opération'"

          (change)="addFilter({key: 'operation_id', name: 'Opération'}, $event && $event.id ? $event.id : null)"
        ></ng-select>
      </th>

      <th>
        <ng-select
          [items]="clients$|async"
          [multiple]="false"
          [closeOnSelect]="true"
          [searchable]="true"
          bindLabel="nom"
          bindValue="id"
          [placeholder]="'Client'"

          (change)="addFilter({key: 'client_id', name: 'Client'}, $event && $event.id ? $event.id : null)"
        ></ng-select>

      </th>

      <th>
        <input type="search" #dd0="ngbDatepicker"
               ngbDatepicker=""
               (focus)="dd0.toggle()"
               (dateSelect)="table.addFilter({key:'dateFacturation', object:object}, $event, 'equal')"
               class="form-control"
               [autoClose]="true"
               placeholder="Date de facturation"
               (change)="table.addFilter({key:'dateFacturation', object:object}, $event.target['value'], 'equal'); dd0.close()" />
      </th>

<!--      <th>-->
<!--        <input type="search" #dd1="ngbDatepicker"-->
<!--               ngbDatepicker=""-->
<!--               (focus)="dd1.toggle()"-->
<!--               (dateSelect)="table.addFilter({key:'date', object:object}, $event, 'equal')"-->
<!--               class="form-control"-->
<!--               placeholder="Date de création"-->
<!--               (change)="table.addFilter({key:'date', object:object}, $event.target['value'], 'equal')" />-->
<!--      </th>-->

      <th>
        <input type="search" #dd2="ngbDatepicker"
               ngbDatepicker=""
               (focus)="dd2.toggle()"
               (dateSelect)="table.addFilter({key:'echeance', object:object}, $event, 'equal')"
               class="form-control"
               [autoClose]="true"
               placeholder="Date d'échéance"
               (change)="table.addFilter({key:'echeance', object:object}, $event.target['value'], 'equal'); dd2.close()" />
      </th>

      <ng-container *ngFor="let column of columns">
        <th>
          <ng-container *ngIf="column.filter === true">
            <input   type="search"
                     class="form-control"
                     (change)="table.addFilter(column, $event.target['value'], 'like')"
                     (search)="clearFilter(column)"
                     [placeholder]="column.name"
            />
          </ng-container>
        </th>
      </ng-container>

      <th>
        <input   type="search"
                 class="form-control"
                 (change)="table.addFilter({key:'montantHT', object:object}, $event.target['value'], 'number', 'Montant HT')"
                 (search)="clearFilter({key:'montantHT', object:object})"
                 [placeholder]="'HT'"
        />
        </th>
      <th>
        <input   type="search"
                   class="form-control"
                   (change)="table.addFilter({key:'montantTTC', object:object}, $event.target['value'], 'number', 'Montant TTC')"
                   (search)="clearFilter({key:'montantTTC', object:object})"
                   [placeholder]="'TTC'"
        />
      </th>
      <th></th>
      <th></th>
      <th></th>
      <th></th>
      <th>
        <ng-select
          [items]="[{id: 1, text: 'Envoyées'}, {id: -1, text: 'Non envoyées'}]"
          [multiple]="false"
          [closeOnSelect]="true"
          [searchable]="true"
          bindLabel="text"
          bindValue="id"
          [placeholder]="'Envoyée ?'"
          #envoyeSelect
          (change)="addFilter({key: 'envoyee', name: 'Envoyée'}, $event && $event.id ? $event.id : null, 'equal', envoyeSelect)"
        ></ng-select>
      </th>
      <th>
        <ng-select
          [items]="[{id: 1, text: 'Payée'}, {id: -1, text: 'Non payée'}]"
          [multiple]="false"
          [closeOnSelect]="true"
          [searchable]="false"
          bindLabel="text"
          bindValue="id"
          [placeholder]="'Payée ?'"
          #payeSelect
          (change)="addFilter({key: 'payee', name: 'Payée'}, $event && $event.id ? $event.id : null, 'equal', payeSelect)"
        ></ng-select>
      </th>
      <th></th>
    </tr>

  </ng-template>

</div>
