import {Component, Input, OnInit} from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import {
  IntraDoOperation, IntraDoOperationPeopleRole,
  IntraDoSinistre,
  IntraEbuzFacture, IntraEbuzFactureApi, IntraMultiOperationPeopleRole, IntraMultiSinistre,
  People,
  SmileMoodCampaignApi, SmileMoodTemplate, SmileMoodTemplateApi
} from '../../../../../shared/sdk';
import moment from 'moment';
import {AppGlobalEventManagerService} from "../../../../../common/services/app.globalEventManager.service";
import {AppSessionService} from "../../../../../common/services/app.session.service";
import {PeopleWithrightsInterface} from "../../../../../common/interfaces/peopleWithrights.interface";
import {ToastrService} from "ngx-toastr";
import {FormBuilder, FormGroup, Validators} from "@angular/forms";
import {TableColumnInterface} from "../../../../shared/interfaces/table.column.interface";
import {forkJoin, Observable} from "rxjs";
import {AppCacheService} from "../../../../../common/services/app.cache.service";
import {tap} from "rxjs/operators";

@Component({
  selector: 'SmileMoodFormModale',
  templateUrl: './smileMood.form.modale.html'
})
export class SmileMoodFormModale implements OnInit {

  @Input() destinataires: People[] = [];
  @Input() libelle: string = '';
  @Input() subject: string = '';
  @Input() message: string = '';
  @Input() files!: {text: string; key: string; type?: string}[];

  @Input() sinistre: IntraDoSinistre = null;
  @Input() sinistreMulti: IntraMultiSinistre = null;
  @Input() operation: IntraDoOperation = null;
  @Input() facture: IntraEbuzFacture = null;

  public responsables: {key: number; societe: string; fullname: string; text: string}[] = [];

  public user: PeopleWithrightsInterface;

  protected moment = moment;

  public folder:string = 'smilemood';

  public columns: Array<TableColumnInterface>;
  public hiddenColumns;

  public form: FormGroup = null;

  public context;
  public showContextes: boolean = false;

  public templates: SmileMoodTemplate[] = [];

  constructor(
    public activeModal: NgbActiveModal,
    public _sessionService: AppSessionService,
    private _apiService: SmileMoodCampaignApi,
    private toastr: ToastrService,
    private _factureService: IntraEbuzFactureApi,
    private _smileMoodTemplateService: SmileMoodTemplateApi,
    public formBuilder: FormBuilder,
    private cacheService: AppCacheService,
    private appGlobalEventManagerService: AppGlobalEventManagerService
  ) {
    this._sessionService.logguedAccountEmitter.subscribe((people) => {
      this.user = people;
    })
  }

  ngOnInit() {
    this._smileMoodTemplateService.find({}).subscribe((templates: SmileMoodTemplate[]) => {
      this.templates = templates;
      this.buildForm()
    })
  }

  updateForm(){
    setTimeout(() => {
      this.buildForm()
    }, 200)
  }

  bindresponsables(peoples: People[]): {key: number; societe: string; fullname: string; text: string}[]{
    // Traitement des destinataires d'abord
    const peoplesForSelect: {key: number; societe: string; fullname: string; text: string}[] = [];

    peoples.forEach((destinataire) => {
      if (destinataire) {

        let p: {key: number; societe: string; fullname: string; text: string} = {
          key: null,
          societe: '',
          fullname: '',
          text: ''
        };

        p['key'] = destinataire.id;
        p['fullname'] = `${destinataire.firstname} ${destinataire.lastname}`;
        p['text'] = destinataire['fullname'];

        if(destinataire.societes && destinataire.societes.length > 0) {
          p['societe'] = destinataire.societes[0].nom;
        }

        peoplesForSelect.push(p);
      }
    });

    return peoplesForSelect;
  }

  bindresponsablesrelations(relations: (IntraDoOperationPeopleRole | IntraMultiOperationPeopleRole)[]): {key: number; societe: string; fullname: string; text: string}[] {
    return this.bindresponsables(
      relations
        .map((relation: IntraDoOperationPeopleRole | IntraMultiOperationPeopleRole) => relation.people)
        .filter(Boolean)
    );
  }

  buildForm(){

    const currentContext = this.context;

    if(currentContext === 'autre'){
      this.showContextes = true;
      this.sinistre = null;
      this.sinistreMulti = null;
      this.operation = null;
      this.facture = null;
      this.responsables = [];
    }


    if(this.responsables.length === 0){
      this.responsables = this.bindresponsables(this.destinataires.filter(destinataire => destinataire != null));
    }

    let config = {}
    if(this.files && this.files.length > 0) {
      config = {
        destinataires: ['', [Validators.required]],
        libelle: [((this.libelle) ? this.libelle : ''), [Validators.required]],

        context: ['', [Validators.required]],
        template_id: ['', [Validators.required]],

        subject: ['', [Validators.required]],
        message: ['', [Validators.required]],

        files: ['', []],
        attachements: ['', []],

      }
    } else {
      config = {
        destinataires: ['', [Validators.required]],
        libelle: [((this.libelle) ? this.libelle : ''), [Validators.required]],

        context: ['', [Validators.required]],
        template_id: ['', [Validators.required]],

        subject: ['', [Validators.required]],
        message: ['', [Validators.required]],

        attachements: ['', []],

      }
    }

    if(this.context && !(this.sinistre || this.operation || this.facture)){
      switch (currentContext) {
        case 'sinistre':
          config['sinistre_id'] = [null, []];
        break;
        case 'sinistreMulti':
          config['sinistreMulti_id'] = [null, []];
          break;
        case 'operation':
          config['operation_id'] = [null, []];
        break;
        case 'facture':
          config['facture_id'] = [null, []];
        break;
      }
    } else {
      if(this.sinistre && this.sinistre.id){
        config['sinistre_id'] = [this.sinistre.id, []];
      } else if(this.sinistreMulti && this.sinistreMulti.id){
        config['sinistreMulti_id'] = [this.sinistreMulti.id, []];
      } else if(this.operation && this.operation.id){
        config['operation_id'] = [this.operation.id, []];
      } else if(this.facture && this.facture.id){
        config['facture_id'] = [this.facture.id, []];
      }
    }

    this.form = this.formBuilder.group(config);

    this.columns = [
      {key: "destinataires", name: "Destinataires", type: "select", required: true, selectOptions: {options: this.responsables, keyBind: 'id', labelBind: 'fullname', groupBy: 'societe', multiple: true}},
      {key: "libelle", name: "Libellé", type: "input", typeInput: "text", required: true},

      // {key: "context", name: "Contexte", type: "select", required: true, selectOptions: {options: [{key: 'sinistre', value: 'Sinistre'}, {key: 'operation', value: 'Opération'}, {key: 'facture', value: 'Facture'}], keyBind: 'key', labelBind: 'value'}},
    ]

    if(currentContext && !(this.sinistre || this.operation || this.facture)){
      this.form.get('context').setValue(this.context)
      switch (currentContext) {
        case 'sinistre':
          this.columns = this.columns.concat([
            {key: "sinistre_id",name: "Sinistre lié", type: "input", explain:""} // Here !
          ]);
        break;
        case 'sinistreMulti':
          this.columns = this.columns.concat([
            {key: "sinistreMulti_id",name: "Sinistre multi-risques lié", type: "input", explain:""} // Here !
          ]);
          break;
        case 'operation':
          this.columns = this.columns.concat([
            {key: "operation_id",name: "Opération liée", type: "input", explain:""} // Here !
          ]);
        break;
        case 'facture':
          this.columns = this.columns.concat([
            {key: "facture_id",name: "Facture liée", type: "input", explain:""} // Here !
          ]);
        break;
        default:

        break;
      }
    } else if(this.sinistre || this.operation || this.facture){
      if(this.sinistre && this.sinistre.id){
        this.form.get('sinistre_id').setValue(this.sinistre.id);

        this.form.get('libelle').setValue(`Sondage sinistre DO référence ${this.sinistre.referencevge}`);
        this.form.get('subject').setValue(`Sondage sinistre DO référence ${this.sinistre.referencevge}`);

      }if(this.sinistreMulti && this.sinistreMulti.id){
        this.form.get('sinistreMulti_id').setValue(this.sinistreMulti.id);

        this.form.get('libelle').setValue(`Sondage sinistre multi-risques référence ${this.sinistreMulti.referencevge}`);
        this.form.get('subject').setValue(`Sondage sinistre multi-risques référence ${this.sinistreMulti.referencevge}`);

      } else if(this.operation && this.operation.id){
        this.form.get('operation_id').setValue(this.operation.id);

        this.form.get('libelle').setValue(`Sondage opération ${this.operation.libelle}`);
        this.form.get('subject').setValue(`Sondage opération ${this.operation.libelle}`);

      } else if(this.facture && this.facture.id){
        this.form.get('facture_id').setValue(this.facture.id);

        this.form.get('libelle').setValue(`Sondage facture N°${this.facture.numreal}`);
        this.form.get('subject').setValue(`Sondage facture N°${this.facture.numreal}`);

      }
    }

    this.columns = this.columns.concat([
      {key: "template_id",name: "Template du sondage (survey)", type: "select", selectOptions:{multiple:false, labelBind:'name', keyBind:'id', selectableGroupAsModel: false, selectableGroup: false, groupBy:'categorie', options:this.templates.filter(template => template.categorie === this.context)}},
      {key: "subject",name: "Objet du mail", type: "input", explain:""},
      {key: "message",name: "Contenu du mail", type: "text"}, // Emails séparés par des , !
    ]);

    if(this.files && this.files.length > 0) {
      this.columns.push(
        {key: "files",name: "PDF(s) attaché(s) au sondage SmileMood", type: "select", selectOptions:{multiple:true, labelBind:'text', keyBind:'key', selectableGroupAsModel: true, selectableGroup: true, groupBy:'type', options:[]}},
      )
    }

    this.columns = this.columns.concat(
      [
        {key: "attachements",name: "PDF(s) externe(s) attaché(s) au sondage SmileMood", type: "files"},
      ]
    )

    // bind ?
    setTimeout(() => {
      this.bind();
    }, 200)
  }

  selectSinistre(sinistre){
    this.showContextes = true;
    if(sinistre.sinistre){
      this.sinistre = sinistre.sinistre;
      this.form.get('sinistre_id').setValue(this.sinistre.id);

      console.log(this.sinistre)

      if(sinistre.opId){
        this.loadDestinatairesDo(sinistre.opId).subscribe(() => {
          this.updateForm();
        });
      } else {
        this.updateForm();
      }

    }
  }

  selectSinistreMulti(sinistreMulti){
    this.showContextes = true;
    if(sinistreMulti.sinistre){
      this.sinistreMulti = sinistreMulti.sinistre;
      this.form.get('sinistreMulti_id').setValue(this.sinistreMulti.id);

      if(sinistreMulti.opId){
        this.loadDestinatairesMulti(sinistreMulti.opId).subscribe(() => {
          this.updateForm();
        });
      } else {
        this.updateForm();
      }
    }
  }

  selectFacture(facture){
    this.showContextes = true;

    this._factureService.findById(facture.id, {include: ['contrat']}).subscribe((f: IntraEbuzFacture) => {
      this.facture = f;
      this.loadDestinatairesFacture(this.facture);
      this.updateForm();
    });
  }

  selectOperation(operation){
    this.showContextes = true;

    console.log(operation)

    // this.loadDestinataires(operation.id);

    // this.operation = operation;

    this.form.get('libelle').setValue(`Sondage référence ${this.sinistreMulti.referencevge}, opération ${this.sinistreMulti.operation.libelle}`);
    this.form.get('subject').setValue(`Sondage référence ${this.sinistreMulti.referencevge}, opération ${this.sinistreMulti.operation.libelle}`);

    this.updateForm();
  }

  loadDestinatairesFacture(facture: IntraEbuzFacture){

    const resps = [];

    if(facture.contrat){
      if(facture.contrat.comptable){
        const c1: People = facture.contrat.comptable;
        let nom = ((c1 && c1.societes && c1.societes.length > 0) ? c1.societes[0].nom : '') + ' (' + c1.firstname + ' ' + c1.lastname + ')';
        resps.push({
          key: c1.id,
          societe: ((c1.societes && c1.societes.length > 0) ? c1.societes[0].nom : ''),
          fullname: c1.firstname + ' ' + c1.lastname,
          text: nom
        })
      }

      if(facture.contrat.comptable2){
        const c1: People = facture.contrat.comptable2;
        let nom = ((c1 && c1.societes && c1.societes.length > 0) ? c1.societes[0].nom : '') + ' (' + c1.firstname + ' ' + c1.lastname + ')';
        resps.push({
          key: c1.id,
          societe: ((c1.societes && c1.societes.length > 0) ? c1.societes[0].nom : ''),
          fullname: c1.firstname + ' ' + c1.lastname,
          text: nom
        })
      }

      if(facture.contrat.comptable3){
        const c1: People = facture.contrat.comptable3;
        let nom = ((c1 && c1.societes && c1.societes.length > 0) ? c1.societes[0].nom : '') + ' (' + c1.firstname + ' ' + c1.lastname + ')';
        resps.push({
          key: c1.id,
          societe: ((c1.societes && c1.societes.length > 0) ? c1.societes[0].nom : ''),
          fullname: c1.firstname + ' ' + c1.lastname,
          text: nom
        })
      }

      if(facture.contrat.comptable4){
        const c1: People = facture.contrat.comptable4;
        let nom = ((c1 && c1.societes && c1.societes.length > 0) ? c1.societes[0].nom : '') + ' (' + c1.firstname + ' ' + c1.lastname + ')';
        resps.push({
          key: c1.id,
          societe: ((c1.societes && c1.societes.length > 0) ? c1.societes[0].nom : ''),
          fullname: c1.firstname + ' ' + c1.lastname,
          text: nom
        })
      }

      if(facture.contrat.comptable5){
        const c1: People = facture.contrat.comptable5;
        let nom = ((c1 && c1.societes && c1.societes.length > 0) ? c1.societes[0].nom : '') + ' (' + c1.firstname + ' ' + c1.lastname + ')';
        resps.push({
          key: c1.id,
          societe: ((c1.societes && c1.societes.length > 0) ? c1.societes[0].nom : ''),
          fullname: c1.firstname + ' ' + c1.lastname,
          text: nom
        })
      }
    }

    this.responsables = resps;

  }


  loadDestinatairesDo(opId: number): Observable<any> {
    return this.cacheService.getListResponsablesRelationsOperation(opId).pipe(
      tap((responsablesRelations: IntraDoOperationPeopleRole[]) => {
        this.responsables = this.bindresponsablesrelations(responsablesRelations);
      })
    );
  }

  loadDestinatairesMulti(opId: number){
    return this.cacheService.getListResponsablesRelationsOperationMulti(opId).pipe(
      tap((responsablesRelations: IntraMultiOperationPeopleRole[]) => {
        this.responsables = this.bindresponsablesrelations(responsablesRelations);
      })
    );
  }

  bind(){
    if(this.files && this.files.length > 0){
      let col = this.columns.find((el) => {return el.key == 'files';});
      if(col){col.selectOptions.options = this.files};
    }

    if(this.libelle){
      this.form.get('libelle').setValue(this.libelle)
    }
    if(this.subject) {
      this.form.get('subject').setValue(this.subject)
    }
    if(this.message) {
      this.form.get('message').setValue(this.message)
    }

  }

  save(datas){

    const destinataires = datas['destinataires'];

    const obs = [];

    destinataires.forEach((destinataireId) => {

      obs.push(
        this._apiService.create({
          libelle: datas['libelle'],
          destinataireId: destinataireId,
          subject: datas['subject'],
          message: datas['message'],
          files: (datas['files']) ? datas['files'] : [],
          attachements: datas['attachements'],
        })
      );
    });

    forkJoin(obs).subscribe((res) => {
      this.toastr.success('Succès !', 'Création du sondage pour '+destinataires.length+' personnes');
      this.activeModal.close();
    });

  }

}
