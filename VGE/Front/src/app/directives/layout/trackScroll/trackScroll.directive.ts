import {Directive, ElementRef, EventEmitter, HostListener, Output} from "@angular/core";

@Directive({
  selector: '[track-scroll]',
})

export class TrackScrollDirective {
  @Output() setScroll = new EventEmitter();
  private scroll: number;

  constructor(private el: ElementRef) { }

  @HostListener('scroll', ['$event'])
  scrollIt() { this.scroll = event.srcElement['scrollTop'] }

  reset() {  this.el.nativeElement.scrollTop = this.scroll }
}
