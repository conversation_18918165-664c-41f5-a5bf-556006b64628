<div class="form-group row" [formGroup]="(form) ? form : null" [ngClass]="{'validate': f && f[formControlName].valid, 'error': submitted && f && f[formControlName].errors}">
  <label [for]="name+range" class="col-md-3 label-control">{{label}} <span class="text-danger" *ngIf="required === true">*</span></label>
  <div class="col-md-9">
    <small *ngIf="explain"><i [innerHTML]="explain"></i></small>
    <div class="controls">
      <ng-select
        #select
        [items]="options"
        [multiple]="true"
        [id]="name+range"
        [closeOnSelect]="false"
        [searchable]="true"
        [bindLabel]="labelBind"
        [bindValue]="keyBind"
        [groupBy]="groupBy"
        [selectableGroup]="selectableGroup"
        [selectableGroupAsModel]="selectableGroupAsModel"
        [placeholder]="label"
        formControlName="{{formControlName}}"
      >
      </ng-select>
    </div>

    <p class="mt-1" *ngIf="submitted && f[formControlName].errors" class="parsley-errors-list filled">
      <span *ngIf="f && f[formControlName].errors.required" class="text-danger">{{label}} est obligatoire</span>
    </p>

  </div>
</div>
