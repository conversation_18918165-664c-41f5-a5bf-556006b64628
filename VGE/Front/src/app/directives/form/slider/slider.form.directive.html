<div class="form-group" [attr.formGroup]="(form) ? form : null" >
  <label [for]="name" class="control-label">{{label}} <span class="tx-danger" *ngIf="required === true">*</span></label>

  <ion-range-slider #sliderElement
                    [type]="type"
                    [min]="min"
                    [max]="max"
                    [from]="value"
                    [from_min]="from_min"
                    [from_max]="from_max"
                    [from_shadow]="from_shadow"
                    [to]="to"
                    [to_min]="to_min"
                    [to_max]="to_max"
                    [to_shadow]="to_shadow"
                    [grid]="grid"
                    [grid_num]="grid_num"
                    [grid_snap]="grid_snap"
                    [step]="step"
                    [prefix]="prefix"
                    [postfix]="postfix"
                    [decorate_both]="decorate_both"
                    (onUpdate)="update($event)"
                    (onChange)="change($event)"
                    (onFinish)="finish($event)"
  >
  </ion-range-slider>

  <div *ngIf="submitted && f[name].errors" class="parsley-errors-list filled">
    <div *ngIf="f && f[name].errors.required" class="parsley-required">{{label}} est obligatoire</div>
  </div>
</div>
