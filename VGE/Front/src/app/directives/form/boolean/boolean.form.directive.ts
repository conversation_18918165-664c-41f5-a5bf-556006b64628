import {Component, forwardRef, Input} from '@angular/core';
import {ControlValueAccessor, FormGroup, NG_VALUE_ACCESSOR} from '@angular/forms';

@Component({
  selector: 'booleanForm',
  templateUrl: './boolean.form.directive.html',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => BooleanFormDirective),
      multi: true
    }
  ]
})
export class BooleanFormDirective implements ControlValueAccessor{

  @Input('id') id: string;
  @Input('form') form: FormGroup;
  @Input('name') name: string;
  @Input('type') type: string = 'text';
  @Input('explain') explain: string;
  @Input('value') value: any;
  @Input('label') label: string;
  @Input('classes') classes: string;
  @Input('placeholder') placeholder: string;
  @Input('formControlName') formControlName: string;
  @Input('submitted') submitted: boolean = false;


  @Input('required') required: boolean;

  onChange: any = () => { };
  onTouched: any = () => { };

  constructor(
  ){

  }

  get f() { return (this.form) ? this.form.controls : null; }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  writeValue(value: any): void {
    if (value) {
      this.value = value;
    }
  }


}
