<!-- - orderField {{orderField}}<br/>-->
<!-- - orderWay {{orderWay}}<br/>-->
<!-- - defaultKeyColumn {{defaultKeyColumn}}<br/>-->

<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-content collapse show">
        <div class="card-body card-dashboard">
          <div class="dataTables_wrapper dt-bootstrap4">
            <div class="row">
              <div class="col-sm-12 col-md-6">
                <div class="dataTables_length">
                  <label>
                    Afficher
                    <select class="custom-select custom-select-sm form-control form-control-sm" #selectNbPerPage (change)="defineItemsPerPage(selectNbPerPage.value)">
                      <option value="{{itemtoshow}}" *ngFor="let itemtoshow of itemsToShow" [selected]="itemPerPage == itemtoshow">{{itemtoshow}}</option>
                    </select>
                    éléments
                  </label>
                </div>
              </div>
            </div>

            <div class="row" *ngIf="(filters|keyvalue).length > 0">
              <div class="col-sm-12 d-flex">
                <div class="mR5">
                  <button class="btn btn-default"><i class="fa-duotone fa-filter"></i> Filtres : </button>
                </div>
<!--                <button type="button"  class="btn btn-sm btn-social btn-secondary"><i class="ft-delete text-danger"></i><span>Réinitialiser</span></button>-->
                <ng-container *ngFor="let filter of (filters|keyvalue); let i=index">
                  <div class="mL5" *ngIf="blackListFilters && blackListFilters.indexOf(filter.key.toString()) == -1">
                    <button (click)="updateInstigator(filter.value)" type="button" class="btn btn-sm btn-social btn-secondary"><i class="ft-delete text-danger"></i>
                      <span *ngIf="filter.value?.libelle">{{filter.value?.libelle}}</span>
                      <span *ngIf="!filter.value?.libelle">{{filter.value?.value}}</span>
                    </button>
                  </div>
                </ng-container>
              </div>
            </div>


            <div class="row">
              <div class="col-sm-12 col-md-5">
                <div class="dataTables_info" id="DataTables_Table_0_info" role="status" aria-live="polite">{{pageStatus}}</div>
              </div>
              <div class="col-sm-12 col-md-7">
                <div class="dataTables_paginate paging_simple_numbers" id="DataTables_Table_0_paginate">
                  <ngb-pagination
                    #nbgPagination [page]="pageToInit"
                    [pageSize]="itemPerPage" [collectionSize]="nbItems"
                    [maxSize]="5" [boundaryLinks]="false"
                    [rotate]="true" [ellipses]="false"
                    [directionLinks]="true" (pageChange)="pageChange($event)"
                    class="d-flex justify-content-end" aria-label="Pagination elements"
                  >
                  </ngb-pagination>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="col-sm-12">

                <div [class.container-table]="tableResponsive === true">

                  <ng-container *ngIf="scrollable">
                    <ng-container *ngIf="overflow">
                      <div class="w-100" style="overflow: auto;height: 800px;">
                        <ng-container *ngTemplateOutlet="table"></ng-container>
                      </div>
                    </ng-container>
                    <ng-container *ngIf="!overflow">
                      <div class="w-100">
                        <ng-container *ngTemplateOutlet="table"></ng-container>
                      </div>
                    </ng-container>

                  </ng-container>

                  <ng-container *ngIf="!scrollable">
                    <ng-container *ngIf="overflow">
                      <div class="w-100" style="overflow: auto;height: 800px;">
                        <ng-container *ngTemplateOutlet="table"></ng-container>
                      </div>
                    </ng-container>
                    <ng-container *ngIf="!overflow">
                      <ng-container *ngTemplateOutlet="table"></ng-container>
                    </ng-container>
                  </ng-container>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>



<ng-template #table>
  <table [class.fixedTopRow]="fixTopRow === true" [class.table-responsive]="tableResponsive === true" class="table table-striped table-compact table-hover compact table-bordered zero-configuration dataTable " role="grid" aria-describedby="DataTables_Table_0_info">
    <thead>
    <tr role="row" class="sticky">
      <ng-container *ngTemplateOutlet="headerTopTemplate"></ng-container>
      <th [ngClass]="{
                        'sorting' : column.order,
                        'sorting_asc' : column.order && ((column.object) ? (column.object+'.'+column.key == orderField) : column.key == orderField) && orderWay == 'ASC',
                        'sorting_desc': column.order && ((column.object) ? (column.object+'.'+column.key == orderField) : column.key == orderField) && orderWay == 'DESC'
                    }"
          (click)="sort(column, this.serie)" *ngFor="let column of columns" [ngStyle]="column.width"><i class="ft-info text-primary mR5" *ngIf="column.explain" [ngbTooltip]="column.explain"></i>{{column.name}}
      </th>
      <ng-container *ngTemplateOutlet="headerBottomTemplate"></ng-container>
      <th></th>
    </tr>
    <ng-container *ngTemplateOutlet="headerSearchTemplate"></ng-container>
    </thead>
    <tfoot>
    <tr>
      <ng-container *ngTemplateOutlet="footerTopTemplate"></ng-container>
      <th *ngFor="let column of columns"></th>
      <ng-container *ngTemplateOutlet="footerBottomTemplate"></ng-container>
      <th></th>
    </tr>
    </tfoot>
    <tbody>
<!--    {{(collectionFiltred$|async)|json}}-->
    <ng-container *ngFor="let object of ((collectionFiltred$ | async) | slice: (page-1) * itemPerPage : (page-1) * itemPerPage + itemPerPage); let index = index">
      <tr role="row" [ngStyle]="{'background': (object.hasOwnProperty('rowColor') ? object['rowColor'] : '')}">
        <ng-container *ngTemplateOutlet="bodyTopTemplate; context: {$implicit: object, orderField: orderField, orderWay: orderWay, index: index}"></ng-container>
        <td *ngFor="let column of columns">
          <ng-container [ngSwitch]="column.type">
            <ng-container *ngSwitchCase="'color'">
              <colorTable [column]="column" [object]="object"></colorTable>
            </ng-container>
            <ng-container *ngSwitchCase="'boolean'">
              <booleanTable [column]="column" [object]="object"></booleanTable>
            </ng-container>
            <ng-container *ngSwitchCase="'image'">

              <imageTable
                [column]="column" [object]="object"
                [width]="64" [preview]="true"
                [resize]="true"></imageTable>

            </ng-container>

            <ng-container *ngSwitchCase="'file'">

              <ng-container *ngIf="column?.object"><fileDownload type="file" [lien]="object[column.object][column.key]"></fileDownload><span class="mL10"><filePresentation [link]="object[column.object][column.key]"></filePresentation></span></ng-container>
              <ng-container *ngIf="!column?.object"><fileDownload type="file" [lien]="object[column.key]" *ngIf="!column?.object"></fileDownload><span class="mL10"><filePresentation [link]="object[column.key]"></filePresentation></span></ng-container>

            </ng-container>

            <ng-container *ngSwitchDefault>
              <div *ngIf="column?.object" innerHTML="{{object[column.object][column.key]|formatTable:column:{key: idKey, value:object[column.object][idKey]} }}"></div>
              <div *ngIf="!column?.object" innerHTML="{{object[column.key]|formatTable:column:{key:idKey, value:object[idKey]} }}"></div>
            </ng-container>
          </ng-container>
        </td>
        <ng-container *ngTemplateOutlet="bodyBottomTemplate; context: {$implicit: object, orderField: orderField, orderWay: orderWay, index: index}"></ng-container>
        <td class="text-nowrap">
          <ng-container *ngTemplateOutlet="actionsTemplate; context: {$implicit: object, orderField: orderField, orderWay: orderWay}"></ng-container>
        </td>
      </tr>
      <ng-container *ngIf="ligneSupTemplate">
        <tr role="row" [ngClass]="{'d-none': !listVisible[object.id]}">
          <td colspan="100">
            <ng-container *ngTemplateOutlet="ligneSupTemplate; context: {$implicit: object, listVisible: listVisible}"></ng-container>
          </td>
        </tr>
      </ng-container>
    </ng-container>
    <ng-container *ngIf="(collection$ | async)?.length == 0">
      <tr>
        <th colspan="100" class="text-center">Aucun élément</th>
      </tr>
    </ng-container>
    </tbody>
  </table>
</ng-template>
