import {Component, Inject, Input, Optional} from "@angular/core";
import {apiParameterInterface} from "../../../modules/shared/interfaces/apiParameter.interface";
import {AppDownloadService} from "../../../common/services/app.download.service";

@Component({
  selector: 'fileDownload',
  templateUrl: './fileDownload.partial.html'
})
export class FileDownloadPartial{

  @Input('lien') lien : string;
  @Input('label') label: string = null;
  @Input('tooltip') tooltip: string = 'Télécharger';
  @Input('type') type: 'image'|'file' = 'file';
  @Input('class') class: string = 'btn btn-sm btn-info';
  @Input('icon') icon: string = 'fa-duotone fa-download';
  @Input('PDFView') PDFView: boolean = true;

  public apiParameters: apiParameterInterface;

  constructor(
    public _appDownloadService: AppDownloadService
  ){

  }

  download(lien){
    this._appDownloadService.download(lien, this.type);
  }




}
