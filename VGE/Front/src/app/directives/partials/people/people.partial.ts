import {Component, Input} from '@angular/core';
import {AdminRole, People} from "../../../shared/sdk/models";

@Component({
  selector: 'people',
  templateUrl: './people.partial.html'
})
export class PeoplePartial{

  @Input('people') people: People | Partial<People> = null;
  @Input('break') break: boolean = false;
  @Input('showSociete') showSociete: boolean = true;
  @Input('reverseNoms') reverseNoms: boolean = false;
  @Input('showAddress') showAddress: boolean = false;
  @Input('showInitiales') showInitiales: boolean = false;

  @Input('small') small: boolean = false;

  @Input() role: Partial<AdminRole>;


  @Input('noWrap') noWrap: boolean = false;

  @Input('showGroup') showGroup: boolean = true;

  @Input('showMediaPhoto') showMediaPhoto: boolean = false;

  constructor(

  ){



  }


}
