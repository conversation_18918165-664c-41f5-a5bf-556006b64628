import {Component, EventEmitter, Input, OnChanges, OnInit, Output} from "@angular/core";
import {NgbActiveModal, NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {ToastrService} from "ngx-toastr";
import moment from 'moment';
import 'moment-duration-format';

@Component({
  selector: 'PDFViewModale',
  templateUrl: './PDFViewModale.directive.html',
})
export class PDFViewModaleDirective implements OnInit{

  @Input() link: string

  public moment = moment;

  zoom: number = 1;
  page: number = 1;
  totalPages: number = 0;
  isLoaded: boolean = false;

  constructor(
    public activeModal: NgbActiveModal,
    public modalService: NgbModal,
    public _toastr: ToastrService,
  ) {

  }

  ngOnInit() {

  }

  trigger(){
    this.activeModal.dismiss('Cross click');
  }


  afterLoadComplete(pdfData: any) {
    this.totalPages = pdfData.numPages;
    this.isLoaded = true;
  }

  nextPage() {
    this.page++;
  }

  prevPage() {
    this.page--;
  }

  zoomIn() {
    if (this.zoom < 3) {
      this.zoom += 0.1;
    }
  }

  zoomOut() {
    if (this.zoom > 0.5) {
      this.zoom -= 0.1;
    }
  }


}
