<div class="modal-header">
  <h4 class="modal-title">Aperçu du PDF</h4>
  <button type="button" class="close" aria-label="Close" (click)="activeModal.dismiss('Cross click')">
    <span aria-hidden="true">&times;</span>
  </button>
</div>
<div class="modal-body" style="height: calc(100% - 55px) !important; padding: 0">
  <div class="container-fluid h-100 pdf-viewer-container">
    <div class="row h-100">
      <div class="col-md-3 col-lg-2 left-panel">
        <div class="mb-3">
          <div class="btn-group d-flex" role="group">
            <button (click)="prevPage()" [disabled]="page === 1" class="btn btn-outline-primary">
              <i class="fa fa-chevron-left"></i> Précédent
            </button>
            <button (click)="nextPage()" [disabled]="page === totalPages" class="btn btn-outline-primary">
              Suivant <i class="fa fa-chevron-right"></i>
            </button>
          </div>
          <div class="text-center mt-2">
            <span class="badge badge-secondary">Page {{ page }} / {{ totalPages }}</span>
          </div>
        </div>

        <div class="mb-3">
          <div class="input-group">
            <div class="input-group-prepend">
              <button (click)="zoomOut()" class="btn btn-outline-secondary">
                <i class="fa fa-search-minus"></i>
              </button>
            </div>
            <input type="text" class="form-control text-center" [value]="(zoom * 100).toFixed(0) + '%'" readonly>
            <div class="input-group-append">
              <button (click)="zoomIn()" class="btn btn-outline-secondary">
                <i class="fa fa-search-plus"></i>
              </button>
            </div>
          </div>
        </div>

        <div class="thumbnail-list">
          <!-- Add thumbnails here if needed -->
        </div>
      </div>

      <div class="col-md-9 col-lg-10 pdf-content filigrane">
        <pdf-viewer [src]="link"
                    style="width: 100%; height: 100vh; position: absolute; z-index: 10000;"
                    [rotation]="0"
                    [original-size]="false"
                    [show-all]="false"
                    [fit-to-page]="true"
                    [zoom]="zoom"
                    [zoom-scale]="'page-fit'"
                    [stick-to-page]="true"
                    [render-text]="true"
                    [external-link-target]="'blank'"
                    [autoresize]="true"
                    [(page)]="page"
                    [show-borders]="false"
                    (after-load-complete)="afterLoadComplete($event)"
        ></pdf-viewer>
      </div>
    </div>
  </div>


</div>
