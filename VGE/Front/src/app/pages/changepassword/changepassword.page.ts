import {Component, OnInit} from '@angular/core';
import { Form<PERSON><PERSON>er, FormGroup, Validators } from "@angular/forms";
import { Router } from "@angular/router";
import { ToastrService } from 'ngx-toastr';
import { environment } from "../../../environments/environment";
import { <PERSON><PERSON><PERSON>oken, LoopBackAuth, PeopleApi, People } from '../../shared/sdk/index';
import { AuthGuard } from "../../common/guards/auth.guard";
import {AppSessionService} from "../../common/services/app.session.service";
import moment from 'moment';
import {OnlineService} from "../../modules/eBoarding/services/online.service";

@Component({
    selector: 'changepasswordPage',
    templateUrl: './changepassword.page.html'
})
export class ChangepasswordPage implements OnInit{

  public loginForm: FormGroup;
  public message: string;
  public submitted = false;

  public environment = environment;

  constructor(
      public formBuilder: FormBuilder,
      public router: Router,
      public _toastr : ToastrService,
      public PeopleApi: PeopleApi,
      public authService: LoopBackAuth,
      public authGuard: AuthGuard,
      public sessionService: AppSessionService,
      private _onlineService: OnlineService,
  ){

    this.loginForm = this.formBuilder.group({
        curpassword: ['', [Validators.required]],
        newpassword: ['', [Validators.required]],
        confnewpassword: ['', [Validators.required]],
    });

    // console.log('LOGIN', this.authService.getAccessTokenId())


  }

  get f() { return this.loginForm.controls; }

  login(){

      this.submitted = true;
      if (this.loginForm.invalid) {
          return;
      }else{

          const cur = this.f.curpassword.value;
          const newp = this.f.newpassword.value;
          const confp = this.f.confnewpassword.value;

          if(newp != confp){
            this._toastr.error('La confirmation du mot de passe et le nouveau mot de passe ne sont pas identiques', 'Changement du mot de passe')
            this.submitted = false;
            return;
          }

          this.PeopleApi.changePassword(cur, newp)
            .subscribe(
              (res: any) => {


                // console.log(res)
                /* Auth in SDK */
                this._toastr.success('Changement du mot de passe', 'Bonjour, votre nouveau mot de passe vient d\'être redéfini')

                setTimeout(() => {
                  this.PeopleApi.logout().subscribe((r) => {
                    this.authService.clear();
                    this.sessionService.updateLogguedAccount(null);
                    this._onlineService.emitLogout(null);
                    this._toastr.success('Vous êtes correctement déconnecté, veuillez vous reconnecter avec votre nouveau mot de passe.', 'Déconnexion');
                    this.router.navigate(['/login']);
                  });
                }, 500)

            }, err => {

                if(err){
                  this._toastr.error(err.message, 'Changement du mot de passe')
                  return;
                }

            }
          );

      }
  }

  ngOnInit(){


  }
}
